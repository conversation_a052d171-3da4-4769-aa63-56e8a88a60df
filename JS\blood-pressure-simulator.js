// ===== BLOOD PRESSURE SIMULATOR ===== //

// Blood Pressure State
const bpState = {
    isRunning: false,
    mode: 'oscillometric',
    currentPressure: 0,
    targetPressure: 180,
    deflationRate: 3, // mmHg/sec
    patient: {
        systolic: 120,
        diastolic: 80,
        heartRate: 75,
        condition: 'normal'
    },
    measurement: {
        systolic: 0,
        diastolic: 0,
        map: 0,
        pulse: 0,
        isComplete: false
    },
    cuffSize: 'adult',
    inflationPressure: 180
};

// Canvas contexts
let oscillometricCanvas, arterialCanvas;
let oscillometricCtx, arterialCtx;

// Animation variables
let bpAnimationId;
let bpTimeOffset = 0;
let measurementPhase = 'idle'; // idle, inflating, measuring, deflating

// Patient scenarios
const bpScenarios = {
    normal: {
        systolic: 120,
        diastolic: 80,
        heartRate: 75,
        name: 'Normal Adult'
    },
    hypertensive: {
        systolic: 160,
        diastolic: 95,
        heartRate: 85,
        name: 'Hypertensive Patient'
    },
    hypotensive: {
        systolic: 90,
        diastolic: 60,
        heartRate: 110,
        name: 'Hypotensive Patient'
    },
    elderly: {
        systolic: 140,
        diastolic: 70,
        heartRate: 65,
        name: 'Elderly Patient'
    },
    athlete: {
        systolic: 110,
        diastolic: 70,
        heartRate: 55,
        name: 'Athletic Patient'
    }
};

// Initialize Blood Pressure Simulator
function initializeBloodPressureSimulator() {
    console.log('Initializing Blood Pressure Simulator...');
    
    // Get canvas elements
    oscillometricCanvas = document.getElementById('oscillometricWaveform');
    arterialCanvas = document.getElementById('arterialWaveform');
    
    if (oscillometricCanvas) oscillometricCtx = oscillometricCanvas.getContext('2d');
    if (arterialCanvas) arterialCtx = arterialCanvas.getContext('2d');
    
    // Update displays
    updateBPDisplay();
    updateBPClassification();
    
    // Start animation
    animateBPSystem();
    
    console.log('Blood Pressure Simulator initialized');
}

// Update BP Display
function updateBPDisplay() {
    // Update pressure readings
    document.getElementById('systolicValue').textContent = bpState.patient.systolic;
    document.getElementById('diastolicValue').textContent = bpState.patient.diastolic;
    document.getElementById('mapValue').textContent = calculateMAP();
    document.getElementById('pulseValue').textContent = bpState.patient.heartRate;
    
    // Update cuff pressure
    document.getElementById('cuffPressureValue').textContent = Math.round(bpState.currentPressure);
    
    // Update pressure gauge needle
    updatePressureGauge();
    
    // Update mode display
    document.getElementById('currentBPMode').textContent = 
        bpState.mode.charAt(0).toUpperCase() + bpState.mode.slice(1);
}

// Calculate Mean Arterial Pressure
function calculateMAP() {
    const systolic = bpState.patient.systolic;
    const diastolic = bpState.patient.diastolic;
    return Math.round(diastolic + (systolic - diastolic) / 3);
}

// Update Pressure Gauge
function updatePressureGauge() {
    const needle = document.getElementById('pressureNeedle');
    if (needle) {
        // Calculate rotation angle (0-300 mmHg range, 270 degrees)
        const angle = (bpState.currentPressure / 300) * 270 - 135;
        needle.style.transform = `rotate(${angle}deg)`;
    }
}

// Update BP Classification
function updateBPClassification() {
    const systolic = bpState.patient.systolic;
    const diastolic = bpState.patient.diastolic;
    const classificationDiv = document.getElementById('bpClassification');
    
    let classification, className, range;
    
    if (systolic < 120 && diastolic < 80) {
        classification = 'Normal Blood Pressure';
        className = 'normal';
        range = '<120/80 mmHg';
    } else if (systolic >= 120 && systolic <= 129 && diastolic < 80) {
        classification = 'Elevated Blood Pressure';
        className = 'elevated';
        range = '120-129/<80 mmHg';
    } else if ((systolic >= 130 && systolic <= 139) || (diastolic >= 80 && diastolic <= 89)) {
        classification = 'Stage 1 Hypertension';
        className = 'stage1';
        range = '130-139/80-89 mmHg';
    } else if (systolic >= 140 || diastolic >= 90) {
        classification = 'Stage 2 Hypertension';
        className = 'stage2';
        range = '≥140/≥90 mmHg';
    } else if (systolic > 180 || diastolic > 120) {
        classification = 'Hypertensive Crisis';
        className = 'crisis';
        range = '>180/>120 mmHg';
    }
    
    if (classificationDiv) {
        classificationDiv.innerHTML = `
            <div class="classification-result ${className}">
                <i class="fas fa-${className === 'normal' ? 'check-circle' : 'exclamation-triangle'}"></i>
                <span class="classification-text">${classification}</span>
                <span class="classification-range">${range}</span>
            </div>
        `;
    }
}

// Animate BP System
function animateBPSystem() {
    bpTimeOffset += 0.02;
    
    // Draw waveforms
    drawOscillometricWaveform();
    drawArterialWaveform();
    
    // Update cuff pressure based on measurement phase
    updateCuffPressure();
    
    // Update displays
    updateBPDisplay();
    
    bpAnimationId = requestAnimationFrame(animateBPSystem);
}

// Update Cuff Pressure
function updateCuffPressure() {
    switch (measurementPhase) {
        case 'inflating':
            bpState.currentPressure += 5; // Fast inflation
            if (bpState.currentPressure >= bpState.targetPressure) {
                measurementPhase = 'measuring';
                setTimeout(() => {
                    measurementPhase = 'deflating';
                }, 2000); // Hold pressure for 2 seconds
            }
            break;
            
        case 'deflating':
            bpState.currentPressure -= bpState.deflationRate * 0.02;
            if (bpState.currentPressure <= 0) {
                bpState.currentPressure = 0;
                measurementPhase = 'idle';
                completeBPMeasurement();
            }
            break;
            
        case 'measuring':
            // Slight pressure variations during measurement
            bpState.currentPressure += Math.sin(bpTimeOffset * 10) * 2;
            break;
    }
}

// Draw Oscillometric Waveform
function drawOscillometricWaveform() {
    if (!oscillometricCtx || !oscillometricCanvas) return;
    
    const width = oscillometricCanvas.width;
    const height = oscillometricCanvas.height;
    
    // Clear canvas
    oscillometricCtx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawBPGrid(oscillometricCtx, width, height, '#3b82f6');
    
    // Draw oscillometric pattern during deflation
    if (measurementPhase === 'deflating') {
        oscillometricCtx.strokeStyle = '#3b82f6';
        oscillometricCtx.lineWidth = 2;
        oscillometricCtx.beginPath();
        
        const heartRate = bpState.patient.heartRate;
        const beatInterval = 60 / heartRate; // seconds per beat
        
        for (let x = 0; x < width; x++) {
            const time = (x / width) * 10 + bpTimeOffset; // 10 seconds visible
            const beatPhase = (time % beatInterval) / beatInterval;
            
            // Calculate oscillation amplitude based on cuff pressure
            let amplitude = 0;
            const systolic = bpState.patient.systolic;
            const diastolic = bpState.patient.diastolic;
            const map = calculateMAP();
            
            if (bpState.currentPressure <= systolic && bpState.currentPressure >= diastolic) {
                // Maximum amplitude at MAP
                const distanceFromMAP = Math.abs(bpState.currentPressure - map);
                amplitude = Math.max(0, 30 - distanceFromMAP * 2);
            }
            
            // Generate oscillation pattern
            const oscillation = amplitude * Math.sin(beatPhase * Math.PI * 2);
            const y = height / 2 + oscillation;
            
            if (x === 0) {
                oscillometricCtx.moveTo(x, y);
            } else {
                oscillometricCtx.lineTo(x, y);
            }
        }
        
        oscillometricCtx.stroke();
        
        // Mark systolic, MAP, and diastolic points
        markPressurePoints();
    }
}

// Draw Arterial Waveform
function drawArterialWaveform() {
    if (!arterialCtx || !arterialCanvas) return;
    
    const width = arterialCanvas.width;
    const height = arterialCanvas.height;
    
    // Clear canvas
    arterialCtx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawBPGrid(arterialCtx, width, height, '#10b981');
    
    // Draw arterial pressure waveform
    arterialCtx.strokeStyle = '#10b981';
    arterialCtx.lineWidth = 3;
    arterialCtx.beginPath();
    
    const heartRate = bpState.patient.heartRate;
    const beatInterval = 60 / heartRate;
    const systolic = bpState.patient.systolic;
    const diastolic = bpState.patient.diastolic;
    
    for (let x = 0; x < width; x++) {
        const time = (x / width) * 5 + bpTimeOffset; // 5 seconds visible
        const beatPhase = (time % beatInterval) / beatInterval;
        
        let pressure;
        if (beatPhase < 0.3) {
            // Systolic upstroke
            pressure = diastolic + (systolic - diastolic) * Math.sin(beatPhase * Math.PI / 0.3);
        } else if (beatPhase < 0.4) {
            // Systolic peak
            pressure = systolic;
        } else if (beatPhase < 0.6) {
            // Dicrotic notch
            const notchPhase = (beatPhase - 0.4) / 0.2;
            pressure = systolic - (systolic - diastolic) * 0.3 * notchPhase;
        } else {
            // Diastolic decay
            const decayPhase = (beatPhase - 0.6) / 0.4;
            pressure = diastolic + (systolic - diastolic) * 0.7 * Math.exp(-decayPhase * 3);
        }
        
        const y = height - (pressure / 200) * height;
        
        if (x === 0) {
            arterialCtx.moveTo(x, y);
        } else {
            arterialCtx.lineTo(x, y);
        }
    }
    
    arterialCtx.stroke();
}

// Draw BP Grid
function drawBPGrid(ctx, width, height, color) {
    ctx.strokeStyle = color;
    ctx.globalAlpha = 0.1;
    ctx.lineWidth = 0.5;
    
    // Vertical lines
    for (let x = 0; x < width; x += 50) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = 0; y < height; y += 25) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
    
    ctx.globalAlpha = 1;
}

// Mark Pressure Points
function markPressurePoints() {
    const systolicMarker = document.getElementById('systolicMarker');
    const diastolicMarker = document.getElementById('diastolicMarker');
    const mapMarker = document.getElementById('mapMarker');
    
    // Position markers based on current measurement
    if (measurementPhase === 'deflating') {
        const systolic = bpState.patient.systolic;
        const diastolic = bpState.patient.diastolic;
        const map = calculateMAP();
        
        if (bpState.currentPressure <= systolic + 5 && bpState.currentPressure >= systolic - 5) {
            if (systolicMarker) systolicMarker.style.opacity = '1';
        }
        
        if (bpState.currentPressure <= map + 3 && bpState.currentPressure >= map - 3) {
            if (mapMarker) mapMarker.style.opacity = '1';
        }
        
        if (bpState.currentPressure <= diastolic + 5 && bpState.currentPressure >= diastolic - 5) {
            if (diastolicMarker) diastolicMarker.style.opacity = '1';
        }
    }
}

// Start BP Measurement
function startBPMeasurement() {
    if (measurementPhase !== 'idle') return;
    
    console.log('Starting BP measurement');
    measurementPhase = 'inflating';
    bpState.currentPressure = 0;
    
    // Reset markers
    const markers = document.querySelectorAll('.marker');
    markers.forEach(marker => marker.style.opacity = '0');
    
    // Show measurement message
    showBPMessage('Starting blood pressure measurement...');
}

// Stop BP Measurement
function stopBPMeasurement() {
    measurementPhase = 'idle';
    bpState.currentPressure = 0;
    showBPMessage('Measurement stopped');
}

// Deflate Cuff
function deflateCuff() {
    if (measurementPhase === 'inflating' || measurementPhase === 'measuring') {
        measurementPhase = 'deflating';
        showBPMessage('Deflating cuff...');
    }
}

// Complete BP Measurement
function completeBPMeasurement() {
    bpState.measurement.systolic = bpState.patient.systolic;
    bpState.measurement.diastolic = bpState.patient.diastolic;
    bpState.measurement.map = calculateMAP();
    bpState.measurement.pulse = bpState.patient.heartRate;
    bpState.measurement.isComplete = true;
    
    updateBPDisplay();
    updateBPClassification();
    
    showBPMessage(`Measurement complete: ${bpState.patient.systolic}/${bpState.patient.diastolic} mmHg`);
}

// Load BP Scenario
function loadBPScenario(scenario) {
    const scenarioData = bpScenarios[scenario];
    if (scenarioData) {
        bpState.patient.systolic = scenarioData.systolic;
        bpState.patient.diastolic = scenarioData.diastolic;
        bpState.patient.heartRate = scenarioData.heartRate;
        bpState.patient.condition = scenario;
        
        updateBPDisplay();
        updateBPClassification();
        
        showBPMessage(`Loaded scenario: ${scenarioData.name}`);
    }
}

// Change Cuff Size
function changeCuffSize(size) {
    bpState.cuffSize = size;
    showBPMessage(`Cuff size changed to: ${size}`);
}

// Change Measurement Mode
function changeMeasurementMode(mode) {
    bpState.mode = mode;
    updateBPDisplay();
    showBPMessage(`Measurement mode: ${mode}`);
}

// Set Inflation Pressure
function setInflationPressure(pressure) {
    bpState.targetPressure = parseInt(pressure);
    bpState.inflationPressure = parseInt(pressure);
    document.getElementById('inflationPressureValue').textContent = `${pressure} mmHg`;
}

// Show BP Message
function showBPMessage(message) {
    let messageDiv = document.getElementById('bpMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'bpMessage';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--module-gradient-primary);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = message;
    messageDiv.style.transform = 'translateX(0)';
    
    // Hide after 3 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.transform = 'translateX(100%)';
        }
    }, 3000);
}

// Start BP Animations
function startBPAnimations() {
    // Animate status indicators
    const statusLight = document.getElementById('bpPowerStatus');
    if (statusLight) {
        statusLight.style.animation = 'pulse 2s ease-in-out infinite';
    }
}

// Export functions for global access
window.initializeBloodPressureSimulator = initializeBloodPressureSimulator;
window.startBPMeasurement = startBPMeasurement;
window.stopBPMeasurement = stopBPMeasurement;
window.deflateCuff = deflateCuff;
window.loadBPScenario = loadBPScenario;
window.changeCuffSize = changeCuffSize;
window.changeMeasurementMode = changeMeasurementMode;
window.setInflationPressure = setInflationPressure;
window.startBPAnimations = startBPAnimations;
