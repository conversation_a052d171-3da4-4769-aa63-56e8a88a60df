<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Ventilator Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
        }

        h1, h2 {
            color: #333;
            text-align: center;
        }

        .main-content {
            display: flex;
            flex-wrap: wrap; /* Allow stacking on smaller screens */
            gap: 20px;
            margin-bottom: 20px;
        }

        .lung-area, .controls-area {
            flex: 1;
            min-width: 300px; /* Minimum width before stacking */
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .lung-area h2, .controls-area h2 {
            margin-top: 0;
        }

        .lung-container {
            width: 90%;
            max-width: 250px;
            height: 200px;
            border: 2px solid #888;
            margin: 20px auto;
            display: flex;
            justify-content: center;
            align-items: flex-end; /* Lung expands upwards from bottom */
            background-color: #e0f0ff; /* Light blue, like air */
            border-radius: 5px;
            overflow: hidden; /* Safety for visual */
        }

        .lung {
            width: 70%; /* Relative to lung-container */
            height: 40px; /* Base height, representing FRC/exhale state */
            background-color: #ffb6c1; /* Pinkish color for lungs */
            border-radius: 15px 15px 0 0; /* Rounded top */
            transform-origin: bottom center;
            /* CSS variables will be set by JS */
            transform: scaleY(var(--lung-scale-exhale, 1)); 
            animation-name: breathe;
            animation-iteration-count: infinite;
            animation-timing-function: ease-in-out;
            /* animation-duration will be set by JS */
        }

        @keyframes breathe {
            0%, 100% {
                transform: scaleY(var(--lung-scale-exhale, 1));
            }
            50% { /* Peak inhale */
                transform: scaleY(var(--lung-scale-inhale, 1.5));
            }
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .control-group input[type="range"] {
            width: calc(100% - 70px); /* Adjust width to fit next to number input */
            margin-right: 10px;
            vertical-align: middle;
        }
        
        .control-group input[type="number"] {
            width: 60px;
            padding: 5px;
            vertical-align: middle;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        #minuteVolumeNum { /* MV input specifically */
             width: 70px; /* Slightly wider for L/min value */
        }


        .explanation-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9e9e9;
            border-radius: 5px;
        }
        .explanation-area h2 {
            margin-top:0;
        }

        /* Responsive adjustments */
        @media (max-width: 680px) {
            .main-content {
                flex-direction: column;
            }
            .control-group input[type="range"] {
                width: calc(100% - 80px); /* Full width minus number input and some margin */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Anesthesia Ventilator Simulator</h1>

        <div class="main-content">
            <div class="lung-area">
                <h2>Patient Lungs</h2>
                <div class="lung-container">
                    <div id="lungVisual" class="lung"></div>
                </div>
            </div>

            <div class="controls-area">
                <h2>Ventilator Settings</h2>
                <div class="control-group">
                    <label for="tidalVolume">Tidal Volume (TV): <span id="tvValue">500</span> mL</label>
                    <input type="range" id="tidalVolumeSlider" min="200" max="800" value="500" step="10">
                    <input type="number" id="tidalVolumeNum" min="200" max="800" value="500" step="10">
                </div>
                <div class="control-group">
                    <label for="respiratoryRate">Respiratory Rate (RR): <span id="rrValue">12</span> breaths/min</label>
                    <input type="range" id="respiratoryRateSlider" min="8" max="30" value="12" step="1">
                    <input type="number" id="respiratoryRateNum" min="8" max="30" value="12" step="1">
                </div>
                <div class="control-group">
                    <label for="minuteVolumeNum">Minute Volume (MV): <span id="mvValue">6.0</span> L/min</label>
                    <input type="number" id="minuteVolumeNum" min="1.6" max="24.0" value="6.0" step="0.1">
                </div>
            </div>
        </div>

        <div class="explanation-area">
            <h2>Understanding Ventilator Parameters</h2>
            <p><strong>Tidal Volume (TV):</strong> The volume of air delivered to the lungs with each breath. Measured in milliliters (mL).</p>
            <p><strong>Respiratory Rate (RR):</strong> The number of breaths delivered per minute. Measured in breaths per minute (bpm).</p>
            <p><strong>Minute Volume (MV):</strong> The total volume of air delivered to the lungs in one minute. Measured in liters per minute (L/min).</p>
            <p><strong>Formula:</strong> Minute Volume (L/min) = (Tidal Volume (mL) * Respiratory Rate (breaths/min)) / 1000</p>
            <p>You can adjust any two of Tidal Volume, Respiratory Rate, or Minute Volume. The third parameter will update automatically. Note that physiological limits are enforced.</p>
        </div>
    </div>

    <script>
        // DOM Elements
        const tidalVolumeSlider = document.getElementById('tidalVolumeSlider');
        const tidalVolumeNum = document.getElementById('tidalVolumeNum');
        const tvValueSpan = document.getElementById('tvValue');

        const respiratoryRateSlider = document.getElementById('respiratoryRateSlider');
        const respiratoryRateNum = document.getElementById('respiratoryRateNum');
        const rrValueSpan = document.getElementById('rrValue');

        const minuteVolumeNumInput = document.getElementById('minuteVolumeNum');
        const mvValueSpan = document.getElementById('mvValue');

        const lungVisual = document.getElementById('lungVisual');

        // Constants for physiological ranges
        const TV_MIN = 200, TV_MAX = 800;
        const RR_MIN = 8, RR_MAX = 30;
        const MV_MIN_ABS = (TV_MIN * RR_MIN) / 1000; // 1.6
        const MV_MAX_ABS = (TV_MAX * RR_MAX) / 1000; // 24.0

        function updateLungAnimation(tv, rr) {
            const baseScale = 1.0; // Corresponds to the .lung base height (exhale state)
            // Max scale relative to base height. e.g. 3.0 means lung visual can triple its base height.
            // This needs to be chosen so max lung height fits in lung-container.
            // .lung height = 40px. lung-container height = 200px. Max scale = 200/40 = 5.
            // Let's use a slightly more conservative max visual increase.
            const maxTotalVisualScaleY = 4.0; // Max visual height will be 40px * 4.0 = 160px. Fits 200px container.

            // Normalize TV (0 for min TV, 1 for max TV)
            let normalizedTV = (tv - TV_MIN) / (TV_MAX - TV_MIN);
            normalizedTV = Math.max(0, Math.min(1, normalizedTV)); // Clamp between 0 and 1

            const inhaleScale = baseScale + (normalizedTV * (maxTotalVisualScaleY - baseScale));
            
            lungVisual.style.setProperty('--lung-scale-exhale', baseScale.toFixed(2));
            lungVisual.style.setProperty('--lung-scale-inhale', inhaleScale.toFixed(2));
            
            const animationDuration = 60 / rr;
            lungVisual.style.animationDuration = animationDuration.toFixed(2) + 's';

            // Ensure animation is running
            if (lungVisual.style.animationPlayState !== 'running') {
                lungVisual.style.animationPlayState = 'running';
            }
        }

        function updateDisplaysAndAnimation(tv, rr, mv) {
            // Update TV displays
            tvValueSpan.textContent = tv.toFixed(0);
            tidalVolumeSlider.value = tv.toFixed(0);
            tidalVolumeNum.value = tv.toFixed(0);

            // Update RR displays
            rrValueSpan.textContent = rr.toFixed(0);
            respiratoryRateSlider.value = rr.toFixed(0);
            respiratoryRateNum.value = rr.toFixed(0);

            // Update MV displays
            mvValueSpan.textContent = mv.toFixed(1);
            minuteVolumeNumInput.value = mv.toFixed(1);
            
            updateLungAnimation(tv, rr);
        }

        function handleTVOrRRChange() {
            let tv = parseFloat(tidalVolumeSlider.value);
            let rr = parseFloat(respiratoryRateSlider.value);
            let mv = (tv * rr) / 1000;
            updateDisplaysAndAnimation(tv, rr, mv);
        }

        function handleMVChange() {
            let targetMV = parseFloat(minuteVolumeNumInput.value);
            let tv = parseFloat(tidalVolumeSlider.value);

            // If targetMV is not a number (e.g., empty input), recalculate from current TV/RR
            // and update the MV input to a valid state.
            if (isNaN(targetMV)) {
                let currentRR = parseFloat(respiratoryRateSlider.value);
                targetMV = (tv * currentRR) / 1000;
                // No early return, proceed to clamp and update RR based on this fallback MV
            }

            // Clamp targetMV to absolute physiological limits
            targetMV = Math.max(MV_MIN_ABS, Math.min(MV_MAX_ABS, targetMV));
            
            let newRR = (targetMV * 1000) / tv;
            if (isNaN(newRR) || !isFinite(newRR)) { // Handle division by zero or other issues
                newRR = RR_MIN; // Default to a safe RR
            }

            // Clamp newRR to its limits
            newRR = Math.round(Math.max(RR_MIN, Math.min(RR_MAX, newRR)));

            // Recalculate actual MV based on the (potentially clamped) newRR and current TV
            let actualMV = (tv * newRR) / 1000;
            
            updateDisplaysAndAnimation(tv, newRR, actualMV);
        }

        // Event Listeners for Tidal Volume
        tidalVolumeSlider.addEventListener('input', () => {
            tidalVolumeNum.value = tidalVolumeSlider.value;
            handleTVOrRRChange();
        });
        tidalVolumeNum.addEventListener('input', () => {
            let val = parseFloat(tidalVolumeNum.value);
            if (isNaN(val)) return; // Allow typing
            // Clamp on blur/change, or live if preferred (can be jerky)
            // For now, just sync slider if within rough range
            if (val >= TV_MIN && val <= TV_MAX) {
                 tidalVolumeSlider.value = val;
                 handleTVOrRRChange();
            }
        });
        tidalVolumeNum.addEventListener('change', () => { // Final validation
            let val = parseFloat(tidalVolumeNum.value);
            if (isNaN(val) || val < TV_MIN) val = TV_MIN;
            if (val > TV_MAX) val = TV_MAX;
            tidalVolumeNum.value = val;
            tidalVolumeSlider.value = val;
            handleTVOrRRChange();
        });


        // Event Listeners for Respiratory Rate
        respiratoryRateSlider.addEventListener('input', () => {
            respiratoryRateNum.value = respiratoryRateSlider.value;
            handleTVOrRRChange();
        });
        respiratoryRateNum.addEventListener('input', () => {
            let val = parseFloat(respiratoryRateNum.value);
            if (isNaN(val)) return;
            if (val >= RR_MIN && val <= RR_MAX) {
                respiratoryRateSlider.value = val;
                handleTVOrRRChange();
            }
        });
        respiratoryRateNum.addEventListener('change', () => { // Final validation
            let val = parseFloat(respiratoryRateNum.value);
            if (isNaN(val) || val < RR_MIN) val = RR_MIN;
            if (val > RR_MAX) val = RR_MAX;
            respiratoryRateNum.value = val;
            respiratoryRateSlider.value = val;
            handleTVOrRRChange();
        });

        // Event Listener for Minute Volume
        minuteVolumeNumInput.addEventListener('input', () => {
            // Live update can be tricky with typing. Let's trigger on input,
            // handleMVChange is designed to cope with partial/NaN input somewhat.
            handleMVChange();
        });
        minuteVolumeNumInput.addEventListener('change', () => {
            // This will ensure final clamping and updates if user leaves an invalid value
            handleMVChange();
        });

        // Initial Setup on DOMContentLoaded
        document.addEventListener('DOMContentLoaded', () => {
            // Set initial values from HTML defaults
            const initialTV = parseFloat(tidalVolumeSlider.value);
            const initialRR = parseFloat(respiratoryRateSlider.value);
            const initialMV = (initialTV * initialRR) / 1000;
            
            updateDisplaysAndAnimation(initialTV, initialRR, initialMV);
            
            // Set min/max for MV input field based on calculated absolute limits
            minuteVolumeNumInput.min = MV_MIN_ABS.toFixed(1);
            minuteVolumeNumInput.max = MV_MAX_ABS.toFixed(1);
        });

    </script>
</body>
</html>
