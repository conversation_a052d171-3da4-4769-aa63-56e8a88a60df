<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Monitoring & Vital Signs - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/modules.css">
    <link rel="stylesheet" href="../CSS/module-content.css">
    <link rel="stylesheet" href="../CSS/responsive.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app-container">
        <!-- Header Section -->
        <header class="module-header">
            <div class="header-content">
                <div class="module-nav">
                    <a href="index.html" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                    <div class="module-title">
                        <i class="fas fa-heartbeat module-icon"></i>
                        <h1>Patient Monitoring & Vital Signs</h1>
                    </div>
                </div>
                <div class="module-progress-header">
                    <div class="progress-info">
                        <span class="progress-label">Module Progress</span>
                        <span class="progress-percentage" id="moduleProgressText">0%</span>
                    </div>
                    <div class="progress-bar-header">
                        <div class="progress-fill" id="moduleProgressBar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Module Navigation -->
        <nav class="module-navigation">
            <div class="nav-content">
                <ul class="module-nav-list">
                    <li><a href="#introduction" class="module-nav-link active" data-section="introduction">
                        <i class="fas fa-play-circle"></i>
                        Introduction
                    </a></li>
                    <li><a href="#vital-signs" class="module-nav-link" data-section="vital-signs">
                        <i class="fas fa-heartbeat"></i>
                        Vital Signs Basics
                    </a></li>
                    <li><a href="#monitor-interface" class="module-nav-link" data-section="monitor-interface">
                        <i class="fas fa-desktop"></i>
                        Monitor Interface
                    </a></li>
                    <li><a href="#emergency-scenarios" class="module-nav-link" data-section="emergency-scenarios">
                        <i class="fas fa-exclamation-triangle"></i>
                        Emergency Scenarios
                    </a></li>
                    <li><a href="#simulations" class="module-nav-link" data-section="simulations">
                        <i class="fas fa-laptop"></i>
                        Interactive Simulations
                    </a></li>
                    <li><a href="#assessment" class="module-nav-link" data-section="assessment">
                        <i class="fas fa-clipboard-check"></i>
                        Assessment
                    </a></li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="module-content">
            <!-- Introduction Section -->
            <section id="introduction" class="content-section active">
                <div class="section-container">
                    <div class="section-header">
                        <h2>Introduction to Patient Monitoring</h2>
                        <p class="section-description">Welcome to the comprehensive patient monitoring training module. Learn the fundamentals of vital signs monitoring and patient care.</p>
                    </div>
                    
                    <div class="content-grid">
                        <div class="content-card">
                            <div class="card-header">
                                <i class="fas fa-bullseye"></i>
                                <h3>Learning Objectives</h3>
                            </div>
                            <div class="card-content">
                                <ul class="objectives-list">
                                    <li>Understand fundamental vital signs and their normal ranges</li>
                                    <li>Interpret patient monitor displays and alarms</li>
                                    <li>Recognize emergency situations and appropriate responses</li>
                                    <li>Master different monitoring modalities and their applications</li>
                                    <li>Develop skills in patient assessment and documentation</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <div class="card-header">
                                <i class="fas fa-info-circle"></i>
                                <h3>Module Overview</h3>
                            </div>
                            <div class="card-content">
                                <div class="overview-stats">
                                    <div class="stat-item">
                                        <i class="fas fa-clock"></i>
                                        <span class="stat-label">Duration</span>
                                        <span class="stat-value">4-6 hours</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-desktop"></i>
                                        <span class="stat-label">Simulations</span>
                                        <span class="stat-value">5 Interactive</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="fas fa-question-circle"></i>
                                        <span class="stat-label">Assessments</span>
                                        <span class="stat-value">25 Questions</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="video-container">
                        <div class="video-placeholder">
                            <i class="fas fa-play-circle"></i>
                            <h4>Introduction Video</h4>
                            <p>Overview of Patient Monitoring Systems</p>
                            <button class="btn-primary" onclick="playIntroVideo()">
                                <i class="fas fa-play"></i>
                                Play Video (5:30)
                            </button>
                        </div>
                    </div>
                    
                    <div class="section-actions">
                        <button class="btn-primary" onclick="nextSection()">
                            <i class="fas fa-arrow-right"></i>
                            Start Learning
                        </button>
                    </div>
                </div>
            </section>

            <!-- Vital Signs Section -->
            <section id="vital-signs" class="content-section">
                <div class="section-container">
                    <div class="section-header">
                        <h2>Vital Signs Fundamentals</h2>
                        <p class="section-description">Master the five essential vital signs and their clinical significance.</p>
                    </div>
                    
                    <div class="vital-signs-grid">
                        <div class="vital-sign-card" data-vital="heart-rate">
                            <div class="vital-icon">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <h3>Heart Rate (HR)</h3>
                            <div class="vital-info">
                                <div class="normal-range">
                                    <span class="range-label">Normal Range:</span>
                                    <span class="range-value">60-100 bpm</span>
                                </div>
                                <p class="vital-description">The number of heartbeats per minute, indicating cardiac function and overall cardiovascular health.</p>
                            </div>
                            <button class="btn-secondary" onclick="exploreVitalSign('heart-rate')">
                                <i class="fas fa-search"></i>
                                Explore
                            </button>
                        </div>
                        
                        <div class="vital-sign-card" data-vital="blood-pressure">
                            <div class="vital-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <h3>Blood Pressure (BP)</h3>
                            <div class="vital-info">
                                <div class="normal-range">
                                    <span class="range-label">Normal Range:</span>
                                    <span class="range-value">90-120/60-80 mmHg</span>
                                </div>
                                <p class="vital-description">The force of blood against arterial walls, measured as systolic over diastolic pressure.</p>
                            </div>
                            <button class="btn-secondary" onclick="exploreVitalSign('blood-pressure')">
                                <i class="fas fa-search"></i>
                                Explore
                            </button>
                        </div>
                        
                        <div class="vital-sign-card" data-vital="oxygen-saturation">
                            <div class="vital-icon">
                                <i class="fas fa-lungs"></i>
                            </div>
                            <h3>Oxygen Saturation (SpO2)</h3>
                            <div class="vital-info">
                                <div class="normal-range">
                                    <span class="range-label">Normal Range:</span>
                                    <span class="range-value">95-100%</span>
                                </div>
                                <p class="vital-description">The percentage of oxygen-saturated hemoglobin in the blood.</p>
                            </div>
                            <button class="btn-secondary" onclick="exploreVitalSign('oxygen-saturation')">
                                <i class="fas fa-search"></i>
                                Explore
                            </button>
                        </div>
                        
                        <div class="vital-sign-card" data-vital="respiratory-rate">
                            <div class="vital-icon">
                                <i class="fas fa-wind"></i>
                            </div>
                            <h3>Respiratory Rate (RR)</h3>
                            <div class="vital-info">
                                <div class="normal-range">
                                    <span class="range-label">Normal Range:</span>
                                    <span class="range-value">12-20 breaths/min</span>
                                </div>
                                <p class="vital-description">The number of breaths taken per minute, indicating respiratory function.</p>
                            </div>
                            <button class="btn-secondary" onclick="exploreVitalSign('respiratory-rate')">
                                <i class="fas fa-search"></i>
                                Explore
                            </button>
                        </div>
                        
                        <div class="vital-sign-card" data-vital="temperature">
                            <div class="vital-icon">
                                <i class="fas fa-thermometer-half"></i>
                            </div>
                            <h3>Temperature (Temp)</h3>
                            <div class="vital-info">
                                <div class="normal-range">
                                    <span class="range-label">Normal Range:</span>
                                    <span class="range-value">36.5-37.5°C</span>
                                </div>
                                <p class="vital-description">Core body temperature, indicating metabolic function and infection status.</p>
                            </div>
                            <button class="btn-secondary" onclick="exploreVitalSign('temperature')">
                                <i class="fas fa-search"></i>
                                Explore
                            </button>
                        </div>
                    </div>
                    
                    <div class="knowledge-check">
                        <h3>Knowledge Check</h3>
                        <div class="quiz-question">
                            <p><strong>Question:</strong> What is considered a normal resting heart rate for a healthy adult?</p>
                            <div class="quiz-options">
                                <button class="quiz-option" data-answer="incorrect">40-60 bpm</button>
                                <button class="quiz-option" data-answer="correct">60-100 bpm</button>
                                <button class="quiz-option" data-answer="incorrect">100-120 bpm</button>
                                <button class="quiz-option" data-answer="incorrect">120-140 bpm</button>
                            </div>
                            <div class="quiz-feedback" id="quizFeedback"></div>
                        </div>
                    </div>
                    
                    <div class="section-actions">
                        <button class="btn-secondary" onclick="previousSection()">
                            <i class="fas fa-arrow-left"></i>
                            Previous
                        </button>
                        <button class="btn-primary" onclick="nextSection()">
                            <i class="fas fa-arrow-right"></i>
                            Next Section
                        </button>
                    </div>
                </div>
            </section>

            <!-- Monitor Interface Section -->
            <section id="monitor-interface" class="content-section">
                <div class="section-container">
                    <div class="section-header">
                        <h2>Patient Monitor Interface</h2>
                        <p class="section-description">Learn to navigate and interpret patient monitor displays, alarms, and controls.</p>
                    </div>
                    
                    <div class="monitor-demo">
                        <div class="demo-monitor">
                            <div class="monitor-screen">
                                <div class="monitor-header">
                                    <span class="patient-info">Patient: John Doe | Room: 101</span>
                                    <span class="monitor-time" id="monitorTime">12:34:56</span>
                                </div>
                                <div class="vitals-display">
                                    <div class="vital-display-item">
                                        <span class="vital-label">HR</span>
                                        <span class="vital-value" id="demoHR">75</span>
                                        <span class="vital-unit">bpm</span>
                                    </div>
                                    <div class="vital-display-item">
                                        <span class="vital-label">BP</span>
                                        <span class="vital-value" id="demoBP">120/80</span>
                                        <span class="vital-unit">mmHg</span>
                                    </div>
                                    <div class="vital-display-item">
                                        <span class="vital-label">SpO2</span>
                                        <span class="vital-value" id="demoSpO2">98</span>
                                        <span class="vital-unit">%</span>
                                    </div>
                                    <div class="vital-display-item">
                                        <span class="vital-label">RR</span>
                                        <span class="vital-value" id="demoRR">16</span>
                                        <span class="vital-unit">/min</span>
                                    </div>
                                    <div class="vital-display-item">
                                        <span class="vital-label">Temp</span>
                                        <span class="vital-value" id="demoTemp">37.0</span>
                                        <span class="vital-unit">°C</span>
                                    </div>
                                </div>
                                <div class="alarm-status" id="alarmStatus">
                                    <i class="fas fa-check-circle"></i>
                                    <span>All parameters normal</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="monitor-controls">
                            <h4>Interactive Controls</h4>
                            <p>Click the buttons below to simulate different patient conditions:</p>
                            <div class="control-buttons">
                                <button class="btn-scenario" onclick="simulateCondition('normal')">Normal</button>
                                <button class="btn-scenario" onclick="simulateCondition('tachycardia')">Tachycardia</button>
                                <button class="btn-scenario" onclick="simulateCondition('hypotension')">Hypotension</button>
                                <button class="btn-scenario" onclick="simulateCondition('hypoxia')">Hypoxia</button>
                                <button class="btn-scenario" onclick="simulateCondition('fever')">Fever</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="section-actions">
                        <button class="btn-secondary" onclick="previousSection()">
                            <i class="fas fa-arrow-left"></i>
                            Previous
                        </button>
                        <button class="btn-primary" onclick="nextSection()">
                            <i class="fas fa-arrow-right"></i>
                            Next Section
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../JS/main.js"></script>
    <script src="../JS/modules.js"></script>
    <script src="../JS/patient-monitoring.js"></script>
</body>
</html>
