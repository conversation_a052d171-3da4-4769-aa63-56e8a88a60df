<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Laboratory Simulation for Patient monitor and Ansethsia Machine with Ventilator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
        }

        #app-container {
            max-width: 1200px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 0;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        header {
            background-color: #005a9c; /* A professional blue */
            color: white;
            padding: 20px; /* Adjusted padding */
            text-align: center;
            /* font-size: 1.5em; Removed as h1 will control its size */
        }

        header h1 {
            margin: 0;
            font-size: 1.8em; /* New style for h1 */
        }

        #main-nav {
            background-color: #004080; /* Darker blue for nav */
            padding: 10px 0;
            text-align: center;
        }

        #main-nav a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
            font-size: 1.1em;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        #main-nav a:hover {
            background-color: #005a9c; /* Lighter blue on hover */
            text-decoration: none;
        }

        main {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }

        #monitor-container, #controls-condition-panel, #quiz-container {
            flex: 1 1 320px; /* Grow, shrink, basis 320px */
            min-width: 300px;
            padding: 20px;
            border: 1px solid #dce1e6;
            border-radius: 8px;
            background-color: #f9fafb;
        }
        
        h2, h3 {
            color: #005a9c;
            margin-top: 0;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        /* Monitor Screen */
        #monitor-screen {
            background-color: #001f3f; /* Dark blue, like a real monitor */
            color: #7fdbff; /* Light blue text, can be changed per vital */
            padding: 20px;
            border-radius: 6px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
            gap: 15px;
            min-height: 200px;
        }

        .vital-display {
            background-color: #002b4f; /* Slightly lighter than screen bg */
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #004080;
        }

        .vital-label {
            display: block;
            font-size: 0.9em;
            color: #a0cfff;
            font-weight: bold;
            cursor: help; /* Indicate tooltip presence */
        }

        .vital-value {
            display: block;
            font-size: 2.2em;
            font-weight: bold;
            margin: 8px 0;
            color: #2ecc40; /* Default healthy color (green) */
        }

        .vital-unit {
            font-size: 0.8em;
            color: #80bfff;
        }

        .vital-value.abnormal {
            color: #ff4136 !important; /* Red for abnormal */
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            50% { opacity: 0.6; }
        }

        /* Controls Panel */
        #patient-condition-selector {
            margin-bottom: 25px;
        }
        #patient-condition-selector label {
            font-weight: bold;
            margin-right: 10px;
        }
        #condition-select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ccc;
            min-width: 150px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .control-group input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        /* Quiz Section */
        #quiz-question-text {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        #quiz-options-area button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-align: left;
        }

        #quiz-options-area button:hover {
            background-color: #0056b3;
        }

        #quiz-feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        #quiz-feedback.correct {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        #quiz-feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        #next-question-btn {
            display: block;
            margin-top: 15px;
            padding: 10px 15px;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        #next-question-btn:hover {
            background-color: #5a6268;
        }

        footer {
            text-align: center;
            padding: 15px;
            background-color: #343a40;
            color: #f8f9fa;
            font-size: 0.9em;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            main {
                flex-direction: column;
            }
            #monitor-screen {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                padding: 15px;
            }
            .vital-value {
                font-size: 1.8em;
            }
            header {
                font-size: 1.2em;
            }
        }
         @media (max-width: 480px) {
            #monitor-screen {
                grid-template-columns: repeat(2, 1fr); /* Two vitals per row on very small screens */
            }
             .vital-display {
                padding: 10px;
            }
            .vital-value {
                font-size: 1.6em;
            }
        }
    </style>
</head>
<body>
    <div id="app-container">
        <header>
            <h1>Virtual Laboratory Simulation for Patient Monitor, Anesthesia Machine, and Ventilator</h1>
            <h1>Patient Monitor Simulator</h1>
        </header>
        <nav id="main-nav">
            <a href="HTML/index.html" style="background-color: #28a745; margin-right: 20px;">
                <i class="fas fa-home" style="margin-right: 5px;"></i>
                🏠 Return to Home
            </a>
            <a href="anesthesia_machine_landing.html">جهاز التخدير</a>
            <a href="ventilators_landing.html">أجهزة التنفس الصناعي</a>
            <a href="patient_monitor_landing.html">مراقبة المريض والعلامات الحيوية</a>
        </nav>
        <main>
            <section id="monitor-container">
                <h2>Patient Monitor</h2>
                <div id="monitor-screen">
                    <div class="vital-display" id="hr-vital-block">
                        <span class="vital-label" title="Heart Rate: The number of times your heart beats per minute.">HR</span>
                        <span class="vital-value" id="hr-value">70</span>
                        <span class="vital-unit">bpm</span>
                    </div>
                    <div class="vital-display" id="bp-vital-block">
                        <span class="vital-label" title="Blood Pressure: The force of blood pushing against artery walls. Measured as Systolic (pressure when heart beats) over Diastolic (pressure when heart rests between beats).">BP</span>
                        <span class="vital-value" id="bp-value">120/80</span>
                        <span class="vital-unit">mmHg</span>
                    </div>
                    <div class="vital-display" id="spo2-vital-block">
                        <span class="vital-label" title="Oxygen Saturation (SpO2): The percentage of oxygen-carrying hemoglobin in your blood relative to the total hemoglobin.">SpO2</span>
                        <span class="vital-value" id="spo2-value">98</span>
                        <span class="vital-unit">%</span>
                    </div>
                    <div class="vital-display" id="resp-vital-block">
                        <span class="vital-label" title="Respiration Rate: The number of breaths you take per minute.">RR</span>
                        <span class="vital-value" id="resp-value">16</span>
                        <span class="vital-unit">breaths/min</span>
                    </div>
                    <div class="vital-display" id="temp-vital-block">
                        <span class="vital-label" title="Temperature: The measure of your body's core heat.">Temp</span>
                        <span class="vital-value" id="temp-value">37.0</span>
                        <span class="vital-unit">°C</span>
                    </div>
                </div>
            </section>

            <section id="controls-condition-panel">
                <h2>Controls & Conditions</h2>
                <div id="patient-condition-selector">
                    <label for="condition-select">Patient Condition:</label>
                    <select id="condition-select">
                        <option value="healthy">Healthy</option>
                        <option value="fever">Fever</option>
                        <option value="hypoxia">Hypoxia</option>
                    </select>
                </div>

                <div id="controls-panel">
                    <h3>Adjust Vitals Manually</h3>
                    <div class="control-group">
                        <label for="hr-slider">Heart Rate (bpm): <span id="hr-slider-value">70</span></label>
                        <input type="range" id="hr-slider">
                    </div>
                    <div class="control-group">
                        <label for="bp-systolic-slider">Systolic BP (mmHg): <span id="bp-systolic-slider-value">120</span></label>
                        <input type="range" id="bp-systolic-slider">
                    </div>
                    <div class="control-group">
                        <label for="bp-diastolic-slider">Diastolic BP (mmHg): <span id="bp-diastolic-slider-value">80</span></label>
                        <input type="range" id="bp-diastolic-slider">
                    </div>
                    <div class="control-group">
                        <label for="spo2-slider">SpO2 (%): <span id="spo2-slider-value">98</span></label>
                        <input type="range" id="spo2-slider">
                    </div>
                    <div class="control-group">
                        <label for="resp-slider">Respiration (breaths/min): <span id="resp-slider-value">16</span></label>
                        <input type="range" id="resp-slider">
                    </div>
                    <div class="control-group">
                        <label for="temp-slider">Temperature (°C): <span id="temp-slider-value">37.0</span></label>
                        <input type="range" id="temp-slider" step="0.1">
                    </div>
                </div>
            </section>

            <section id="quiz-container">
                <h2>Vital Signs Quiz</h2>
                <div id="quiz-question-area">
                    <p id="quiz-question-text"></p>
                    <div id="quiz-options-area"></div>
                    <p id="quiz-feedback"></p>
                    <button id="next-question-btn">Next Question</button>
                </div>
            </section>
        </main>
        <footer>
            <p>&copy; 2023 Patient Monitoring Fundamentals Simulator</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const vitalSignsConfig = {
                hr: { 
                    sliderId: "hr-slider", sliderValueDisplayId: "hr-slider-value", monitorValueId: "hr-value",
                    min: 30, max: 220, normalMin: 60, normalMax: 100, decimals: 0
                },
                bpSystolic: {
                    sliderId: "bp-systolic-slider", sliderValueDisplayId: "bp-systolic-slider-value",
                    min: 50, max: 250, normalMin: 90, normalMax: 120, decimals: 0
                },
                bpDiastolic: {
                    sliderId: "bp-diastolic-slider", sliderValueDisplayId: "bp-diastolic-slider-value",
                    min: 30, max: 150, normalMin: 60, normalMax: 80, decimals: 0
                },
                spo2: {
                    sliderId: "spo2-slider", sliderValueDisplayId: "spo2-slider-value", monitorValueId: "spo2-value",
                    min: 70, max: 100, normalMin: 95, normalMax: 100, decimals: 0
                },
                resp: {
                    sliderId: "resp-slider", sliderValueDisplayId: "resp-slider-value", monitorValueId: "resp-value",
                    min: 5, max: 50, normalMin: 12, normalMax: 20, decimals: 0
                },
                temp: {
                    sliderId: "temp-slider", sliderValueDisplayId: "temp-slider-value", monitorValueId: "temp-value",
                    min: 34.0, max: 42.0, step: 0.1, normalMin: 36.5, normalMax: 37.5, decimals: 1
                }
            };

            const patientConditions = {
                healthy: { hr: 70, bpSystolic: 120, bpDiastolic: 80, spo2: 98, resp: 16, temp: 37.0 },
                fever:   { hr: 110, bpSystolic: 115, bpDiastolic: 75, spo2: 97, resp: 22, temp: 39.0 },
                hypoxia: { hr: 120, bpSystolic: 130, bpDiastolic: 85, spo2: 88, resp: 28, temp: 37.2 }
            };

            // DOM Elements
            const conditionSelect = document.getElementById('condition-select');
            const bpMonitorValueEl = document.getElementById('bp-value');

            // Initialize sliders and their displays
            function setupControls() {
                for (const key in vitalSignsConfig) {
                    const config = vitalSignsConfig[key];
                    const slider = document.getElementById(config.sliderId);
                    if (slider) {
                        slider.min = config.min;
                        slider.max = config.max;
                        if (config.step) slider.step = config.step;
                        
                        slider.addEventListener('input', function() {
                            document.getElementById(config.sliderValueDisplayId).textContent = parseFloat(this.value).toFixed(config.decimals);
                            updateMonitorAndStyling();
                        });
                    }
                }
            }

            function updateMonitorAndStyling() {
                // HR
                const hrSlider = document.getElementById(vitalSignsConfig.hr.sliderId);
                const hrValue = parseInt(hrSlider.value);
                const hrMonitorValEl = document.getElementById(vitalSignsConfig.hr.monitorValueId);
                hrMonitorValEl.textContent = hrValue;
                if (hrValue < vitalSignsConfig.hr.normalMin || hrValue > vitalSignsConfig.hr.normalMax) {
                    hrMonitorValEl.classList.add('abnormal');
                } else {
                    hrMonitorValEl.classList.remove('abnormal');
                }

                // BP
                const sysSlider = document.getElementById(vitalSignsConfig.bpSystolic.sliderId);
                const diaSlider = document.getElementById(vitalSignsConfig.bpDiastolic.sliderId);
                const sysVal = parseInt(sysSlider.value);
                const diaVal = parseInt(diaSlider.value);
                bpMonitorValueEl.textContent = `${sysVal}/${diaVal}`;
                if (sysVal < vitalSignsConfig.bpSystolic.normalMin || sysVal > vitalSignsConfig.bpSystolic.normalMax ||
                    diaVal < vitalSignsConfig.bpDiastolic.normalMin || diaVal > vitalSignsConfig.bpDiastolic.normalMax) {
                    bpMonitorValueEl.classList.add('abnormal');
                } else {
                    bpMonitorValueEl.classList.remove('abnormal');
                }

                // SpO2
                const spo2Slider = document.getElementById(vitalSignsConfig.spo2.sliderId);
                const spo2Value = parseInt(spo2Slider.value);
                const spo2MonitorValEl = document.getElementById(vitalSignsConfig.spo2.monitorValueId);
                spo2MonitorValEl.textContent = spo2Value;
                if (spo2Value < vitalSignsConfig.spo2.normalMin || spo2Value > vitalSignsConfig.spo2.normalMax) {
                    spo2MonitorValEl.classList.add('abnormal');
                } else {
                    spo2MonitorValEl.classList.remove('abnormal');
                }
                
                // Respiration
                const respSlider = document.getElementById(vitalSignsConfig.resp.sliderId);
                const respValue = parseInt(respSlider.value);
                const respMonitorValEl = document.getElementById(vitalSignsConfig.resp.monitorValueId);
                respMonitorValEl.textContent = respValue;
                if (respValue < vitalSignsConfig.resp.normalMin || respValue > vitalSignsConfig.resp.normalMax) {
                    respMonitorValEl.classList.add('abnormal');
                } else {
                    respMonitorValEl.classList.remove('abnormal');
                }

                // Temperature
                const tempSlider = document.getElementById(vitalSignsConfig.temp.sliderId);
                const tempValue = parseFloat(tempSlider.value);
                const tempMonitorValEl = document.getElementById(vitalSignsConfig.temp.monitorValueId);
                tempMonitorValEl.textContent = tempValue.toFixed(vitalSignsConfig.temp.decimals);
                if (tempValue < vitalSignsConfig.temp.normalMin || tempValue > vitalSignsConfig.temp.normalMax) {
                    tempMonitorValEl.classList.add('abnormal');
                } else {
                    tempMonitorValEl.classList.remove('abnormal');
                }
            }

            function loadPatientCondition(conditionName) {
                const condition = patientConditions[conditionName];
                if (!condition) return;

                for (const key in condition) {
                    const vitalKey = (key === 'bpSystolic' || key === 'bpDiastolic') ? key : key; // Handles bp parts correctly
                    if (vitalSignsConfig[vitalKey] && vitalSignsConfig[vitalKey].sliderId) {
                        const slider = document.getElementById(vitalSignsConfig[vitalKey].sliderId);
                        const sliderValueDisplay = document.getElementById(vitalSignsConfig[vitalKey].sliderValueDisplayId);
                        
                        slider.value = condition[key];
                        sliderValueDisplay.textContent = parseFloat(condition[key]).toFixed(vitalSignsConfig[vitalKey].decimals);
                    }
                }
                updateMonitorAndStyling();
            }

            conditionSelect.addEventListener('change', function() {
                loadPatientCondition(this.value);
            });
            
            // Quiz Logic
            const quizData = [
                {
                    question: "Which vital sign directly measures the oxygen saturation level in a patient's blood?",
                    options: ["Heart Rate (HR)", "Blood Pressure (BP)", "Oxygen Saturation (SpO2)", "Respiration Rate (RR)", "Temperature (Temp)"],
                    correctAnswer: "Oxygen Saturation (SpO2)"
                },
                {
                    question: "A patient has a high fever (e.g., 39°C). Which vital sign reading is the primary indicator of this?",
                    options: ["HR", "BP", "SpO2", "RR", "Temp"],
                    correctAnswer: "Temp"
                },
                {
                    question: "If a patient is experiencing significant difficulty breathing (dyspnea), which two vital signs are most likely to be immediately abnormal?",
                    options: ["HR and Temp", "BP and SpO2", "SpO2 and RR", "Temp and BP", "HR and BP"],
                    correctAnswer: "SpO2 and RR"
                }
            ];
            let currentQuizQuestionIndex = 0;
            const quizQuestionTextEl = document.getElementById('quiz-question-text');
            const quizOptionsAreaEl = document.getElementById('quiz-options-area');
            const quizFeedbackEl = document.getElementById('quiz-feedback');
            const nextQuestionBtn = document.getElementById('next-question-btn');

            function loadQuizQuestion(index) {
                const q = quizData[index];
                quizQuestionTextEl.textContent = q.question;
                quizOptionsAreaEl.innerHTML = ''; // Clear old options
                quizFeedbackEl.textContent = '';
                quizFeedbackEl.className = '';


                q.options.forEach(optionText => {
                    const button = document.createElement('button');
                    button.textContent = optionText;
                    button.addEventListener('click', function() {
                        checkQuizAnswer(optionText, q.correctAnswer);
                        // Disable options after an answer
                        Array.from(quizOptionsAreaEl.children).forEach(btn => btn.disabled = true);
                    });
                    quizOptionsAreaEl.appendChild(button);
                });
            }

            function checkQuizAnswer(selectedAnswer, correctAnswer) {
                if (selectedAnswer === correctAnswer) {
                    quizFeedbackEl.textContent = "Correct!";
                    quizFeedbackEl.className = 'correct';
                } else {
                    quizFeedbackEl.textContent = `Incorrect. The correct answer is: ${correctAnswer}`;
                    quizFeedbackEl.className = 'incorrect';
                }
            }

            nextQuestionBtn.addEventListener('click', function() {
                currentQuizQuestionIndex = (currentQuizQuestionIndex + 1) % quizData.length;
                loadQuizQuestion(currentQuizQuestionIndex);
                Array.from(quizOptionsAreaEl.children).forEach(btn => btn.disabled = false); // Re-enable options
            });

            // Initial App Setup
            setupControls();
            loadPatientCondition('healthy'); // Load healthy condition by default
            loadQuizQuestion(currentQuizQuestionIndex); // Load first quiz question
        });
    </script>
</body>
</html>
