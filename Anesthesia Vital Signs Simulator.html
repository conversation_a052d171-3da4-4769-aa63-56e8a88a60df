<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vital Signs Monitor Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 15px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 1.6;
        }

        .app-container {
            width: 100%;
            max-width: 1100px;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #1a2533;
            margin-top: 0;
            margin-bottom: 25px;
            font-size: 1.8em;
        }

        .monitor-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .vital-sign-panel {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .vital-label-unit {
            margin-bottom: 12px;
        }

        .vital-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #212529;
        }

        .vital-unit {
            font-size: 0.85em;
            color: #6c757d;
            margin-left: 5px;
        }

        .vital-value {
            font-size: 2.8em;
            font-weight: 700;
            margin-bottom: 18px;
            padding: 8px 0;
            border-radius: 6px;
            transition: color 0.3s ease, background-color 0.3s ease;
        }

        .vital-value.normal {
            color: #28a745; /* Green */
        }

        .vital-value.warning {
            color: #fd7e14; /* Orange */
            animation: pulse-warning 1.8s infinite ease-in-out;
        }

        .vital-value.critical {
            color: #dc3545; /* Red */
            animation: pulse-critical 1.2s infinite ease-in-out;
        }
        
        @keyframes pulse-warning {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.03); opacity: 0.8; }
        }
        @keyframes pulse-critical {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.7; }
        }

        .vital-sign-panel input[type="range"] {
            width: 100%;
            margin-top: 8px;
            cursor: pointer;
        }
        
        .bp-controls div {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .bp-controls label {
            font-size: 0.95em;
            color: #495057;
            margin-right: 8px;
        }
        .bp-controls input[type="range"] {
            flex-grow: 1;
            margin-right: 8px;
        }
        .bp-controls span {
            font-size: 0.95em;
            color: #495057;
            min-width: 30px; 
            text-align: right;
            background-color: #e9ecef;
            padding: 2px 5px;
            border-radius: 4px;
        }

        .messages-area {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 8px;
            padding: 18px;
            margin-bottom: 25px;
            min-height: 100px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
        }
        .messages-area p {
            margin: 8px 0;
            font-size: 0.95em;
        }
        .status-message { font-weight: 600; }
        .critical-message { color: #dc3545; }
        .warning-message { color: #fd7e14; }
        .alert-message { color: #007bff; } /* Info blue for general alerts */


        .challenge-area {
            text-align: center;
            padding: 18px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .challenge-area button {
            padding: 12px 24px;
            font-size: 1.05em;
            font-weight: 500;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            transition: background-color 0.2s ease, transform 0.1s ease;
        }
        .challenge-area button:active {
            transform: translateY(1px);
        }

        #challenge-button {
            background-color: #007bff; 
            color: white;
        }
        #challenge-button:hover {
            background-color: #0069d9;
        }
        #challenge-button:disabled {
            background-color: #adb5bd;
            cursor: not-allowed;
        }

        #oxygen-button {
            background-color: #28a745; 
            color: white;
        }
        #oxygen-button:hover {
            background-color: #218838;
        }

    </style>
</head>
<body>
    <div class="app-container">
        <h1>Anesthesia Vital Signs Simulator</h1>

        <div class="monitor-container">
            <!-- Heart Rate -->
            <div class="vital-sign-panel" id="hr-panel">
                <div class="vital-label-unit">
                    <span class="vital-name">HR</span>
                    <span class="vital-unit">(bpm)</span>
                </div>
                <div class="vital-value" id="hr-value">75</div>
                <input type="range" id="hr-slider" min="30" max="200" value="75">
            </div>

            <!-- SpO2 -->
            <div class="vital-sign-panel" id="spo2-panel">
                <div class="vital-label-unit">
                    <span class="vital-name">SpO2</span>
                    <span class="vital-unit">(%)</span>
                </div>
                <div class="vital-value" id="spo2-value">98</div>
                <input type="range" id="spo2-slider" min="60" max="100" value="98">
            </div>

            <!-- EtCO2 -->
            <div class="vital-sign-panel" id="etco2-panel">
                <div class="vital-label-unit">
                    <span class="vital-name">EtCO2</span>
                    <span class="vital-unit">(mmHg)</span>
                </div>
                <div class="vital-value" id="etco2-value">40</div>
                <input type="range" id="etco2-slider" min="10" max="80" value="40">
            </div>

            <!-- Blood Pressure -->
            <div class="vital-sign-panel" id="bp-panel">
                <div class="vital-label-unit">
                    <span class="vital-name">BP</span>
                    <span class="vital-unit">(mmHg)</span>
                </div>
                <div class="vital-value" id="bp-value">120/80</div>
                <div class="bp-controls">
                    <div>
                        <label for="bp-sys-slider">Sys:</label>
                        <input type="range" id="bp-sys-slider" min="50" max="250" value="120">
                        <span id="bp-sys-display">120</span>
                    </div>
                    <div>
                        <label for="bp-dias-slider">Dia:</label>
                        <input type="range" id="bp-dias-slider" min="30" max="150" value="80">
                        <span id="bp-dias-display">80</span>
                    </div>
                </div>
            </div>

            <!-- Temperature -->
            <div class="vital-sign-panel" id="temp-panel">
                <div class="vital-label-unit">
                    <span class="vital-name">Temp</span>
                    <span class="vital-unit">(°C)</span>
                </div>
                <div class="vital-value" id="temp-value">37.0</div>
                <input type="range" id="temp-slider" min="32.0" max="42.0" value="37.0" step="0.1">
            </div>
        </div>

        <div class="messages-area">
            <p id="info-message-placeholder">Adjust sliders to observe changes in vital signs and their potential implications. Click "Start SpO2 Challenge" for an interactive scenario.</p>
        </div>

        <div class="challenge-area">
            <button id="challenge-button">Start SpO2 Challenge</button>
            <button id="oxygen-button" style="display:none;">Administer Oxygen</button>
        </div>
    </div>

    <script>
        const VITAL_PARAMS_CONFIG = {
            hr: {
                sliderId: 'hr-slider', valueDisplayId: 'hr-value',
                normalMin: 60, normalMax: 100,
                lowMsg: "Low HR (Bradycardia) (&lt;60 bpm): Can indicate vagal stimulation, heart block, or medication effects. May lead to decreased cardiac output.",
                highMsg: "High HR (Tachycardia) (&gt;100 bpm): Can be caused by pain, anxiety, hypovolemia, fever, or cardiac issues. May increase myocardial oxygen demand."
            },
            spo2: {
                sliderId: 'spo2-slider', valueDisplayId: 'spo2-value',
                normalMin: 95, normalMax: 100,
                lowMsg: "Low SpO2 (&lt;95%): May indicate hypoxemia or inadequate ventilation. Requires immediate attention."
            },
            etco2: {
                sliderId: 'etco2-slider', valueDisplayId: 'etco2-value',
                normalMin: 35, normalMax: 45,
                lowMsg: "Low EtCO2 (&lt;35 mmHg): May indicate hyperventilation, decreased metabolism, or pulmonary embolism. Can lead to cerebral vasoconstriction.",
                highMsg: "High EtCO2 (&gt;45 mmHg): May indicate hypoventilation, increased CO2 production (e.g., malignant hyperthermia), or rebreathing. Can lead to respiratory acidosis."
            },
            temp: {
                sliderId: 'temp-slider', valueDisplayId: 'temp-value',
                normalMin: 36.0, normalMax: 38.0,
                lowMsg: "Low Temperature (&lt;36.0°C): Hypothermia can impair drug metabolism, increase bleeding risk, and cause patient discomfort.",
                highMsg: "High Temperature (&gt;38.0°C): Hyperthermia/Fever can indicate infection, inflammation, or rarely, malignant hyperthermia. Increases metabolic demand."
            },
            bpSys: {
                sliderId: 'bp-sys-slider', valueDisplayId: 'bp-sys-display',
                normalMin: 90, normalMax: 140,
                lowMsg: "Low Systolic BP (&lt;90 mmHg): Hypotension can lead to inadequate organ perfusion. Causes include hypovolemia, vasodilation, or cardiac dysfunction.",
                highMsg: "High Systolic BP (&gt;140 mmHg): Hypertension can indicate pain, anxiety, or underlying hypertensive disease. Increases cardiac workload."
            },
            bpDia: {
                sliderId: 'bp-dias-slider', valueDisplayId: 'bp-dias-display',
                normalMin: 60, normalMax: 90,
                lowMsg: "Low Diastolic BP (&lt;60 mmHg): Can impair coronary perfusion, especially if systolic is also low.",
                highMsg: "High Diastolic BP (&gt;90 mmHg): Contributes to overall hypertension and cardiac strain."
            }
        };

        let spo2ChallengeInterval = null;
        const SPO2_DECREMENT_INTERVAL_MS = 1500;
        const SPO2_NORMAL_VALUE = 98;

        const hrSlider = document.getElementById(VITAL_PARAMS_CONFIG.hr.sliderId);
        const hrValueDisplay = document.getElementById(VITAL_PARAMS_CONFIG.hr.valueDisplayId);
        const spo2Slider = document.getElementById(VITAL_PARAMS_CONFIG.spo2.sliderId);
        const spo2ValueDisplay = document.getElementById(VITAL_PARAMS_CONFIG.spo2.valueDisplayId);
        const etco2Slider = document.getElementById(VITAL_PARAMS_CONFIG.etco2.sliderId);
        const etco2ValueDisplay = document.getElementById(VITAL_PARAMS_CONFIG.etco2.valueDisplayId);
        const tempSlider = document.getElementById(VITAL_PARAMS_CONFIG.temp.sliderId);
        const tempValueDisplay = document.getElementById(VITAL_PARAMS_CONFIG.temp.valueDisplayId);
        const bpSysSlider = document.getElementById(VITAL_PARAMS_CONFIG.bpSys.sliderId);
        const bpSysDisplay = document.getElementById(VITAL_PARAMS_CONFIG.bpSys.valueDisplayId);
        const bpDiaSlider = document.getElementById(VITAL_PARAMS_CONFIG.bpDia.sliderId);
        const bpDiaDisplay = document.getElementById(VITAL_PARAMS_CONFIG.bpDia.valueDisplayId);
        const bpValueDisplay = document.getElementById('bp-value');
        const messagesArea = document.querySelector('.messages-area');
        const challengeButton = document.getElementById('challenge-button');
        const oxygenButton = document.getElementById('oxygen-button');

        function updateVitalSignsUI() {
            let alertMessages = [];
            let allVitalsEffectivelyNormal = true; // Tracks if any vital is in an alert state

            // Heart Rate
            const hrVal = parseInt(hrSlider.value);
            hrValueDisplay.textContent = hrVal;
            if (hrVal < VITAL_PARAMS_CONFIG.hr.normalMin) {
                alertMessages.push(VITAL_PARAMS_CONFIG.hr.lowMsg);
                hrValueDisplay.className = 'vital-value critical';
                allVitalsEffectivelyNormal = false;
            } else if (hrVal > VITAL_PARAMS_CONFIG.hr.normalMax) {
                alertMessages.push(VITAL_PARAMS_CONFIG.hr.highMsg);
                hrValueDisplay.className = (hrVal > 130) ? 'vital-value critical' : 'vital-value warning'; // More granular warning/critical
                allVitalsEffectivelyNormal = false;
            } else {
                hrValueDisplay.className = 'vital-value normal';
            }

            // SpO2
            const spo2Val = parseInt(spo2Slider.value);
            spo2ValueDisplay.textContent = spo2Val;
            if (spo2Val < VITAL_PARAMS_CONFIG.spo2.normalMin) {
                alertMessages.push(VITAL_PARAMS_CONFIG.spo2.lowMsg);
                spo2ValueDisplay.className = (spo2Val < 90) ? 'vital-value critical' : 'vital-value warning';
                allVitalsEffectivelyNormal = false;
            } else {
                spo2ValueDisplay.className = 'vital-value normal';
            }

            // EtCO2
            const etco2Val = parseInt(etco2Slider.value);
            etco2ValueDisplay.textContent = etco2Val;
            if (etco2Val < VITAL_PARAMS_CONFIG.etco2.normalMin) {
                alertMessages.push(VITAL_PARAMS_CONFIG.etco2.lowMsg);
                etco2ValueDisplay.className = (etco2Val < 30) ? 'vital-value critical' : 'vital-value warning';
                allVitalsEffectivelyNormal = false;
            } else if (etco2Val > VITAL_PARAMS_CONFIG.etco2.normalMax) {
                alertMessages.push(VITAL_PARAMS_CONFIG.etco2.highMsg);
                etco2ValueDisplay.className = (etco2Val > 50) ? 'vital-value critical' : 'vital-value warning';
                allVitalsEffectivelyNormal = false;
            } else {
                etco2ValueDisplay.className = 'vital-value normal';
            }
            
            // Temperature
            const tempVal = parseFloat(tempSlider.value);
            tempValueDisplay.textContent = tempVal.toFixed(1);
            if (tempVal < VITAL_PARAMS_CONFIG.temp.normalMin) {
                alertMessages.push(VITAL_PARAMS_CONFIG.temp.lowMsg);
                tempValueDisplay.className = (tempVal < 35.0) ? 'vital-value critical' : 'vital-value warning';
                allVitalsEffectivelyNormal = false;
            } else if (tempVal > VITAL_PARAMS_CONFIG.temp.normalMax) {
                alertMessages.push(VITAL_PARAMS_CONFIG.temp.highMsg);
                tempValueDisplay.className = (tempVal > 39.0) ? 'vital-value critical' : 'vital-value warning';
                allVitalsEffectivelyNormal = false;
            } else {
                tempValueDisplay.className = 'vital-value normal';
            }

            // Blood Pressure
            const bpSysVal = parseInt(bpSysSlider.value);
            const bpDiaVal = parseInt(bpDiaSlider.value);
            bpSysDisplay.textContent = bpSysVal;
            bpDiaDisplay.textContent = bpDiaVal;
            bpValueDisplay.textContent = `${bpSysVal}/${bpDiaVal}`;

            let bpIsAbnormal = false;
            let bpOverallStatus = 'normal';

            if (bpSysVal < VITAL_PARAMS_CONFIG.bpSys.normalMin) {
                alertMessages.push(VITAL_PARAMS_CONFIG.bpSys.lowMsg);
                bpIsAbnormal = true;
                bpOverallStatus = (bpSysVal < 70) ? 'critical' : 'warning';
            } else if (bpSysVal > VITAL_PARAMS_CONFIG.bpSys.normalMax) {
                alertMessages.push(VITAL_PARAMS_CONFIG.bpSys.highMsg);
                bpIsAbnormal = true;
                if (bpOverallStatus !== 'critical') bpOverallStatus = (bpSysVal > 180) ? 'critical' : 'warning';
            }
            if (bpDiaVal < VITAL_PARAMS_CONFIG.bpDia.normalMin) {
                alertMessages.push(VITAL_PARAMS_CONFIG.bpDia.lowMsg);
                bpIsAbnormal = true;
                if (bpOverallStatus !== 'critical') bpOverallStatus = (bpDiaVal < 40) ? 'critical' : 'warning';
            } else if (bpDiaVal > VITAL_PARAMS_CONFIG.bpDia.normalMax) {
                alertMessages.push(VITAL_PARAMS_CONFIG.bpDia.highMsg);
                bpIsAbnormal = true;
                if (bpOverallStatus !== 'critical') bpOverallStatus = (bpDiaVal > 110) ? 'critical' : 'warning';
            }
            if (bpDiaVal > bpSysVal && bpSysVal > 0 && bpDiaVal > 0) {
                alertMessages.push("Diastolic BP cannot be higher than Systolic BP. Please adjust sliders.");
                bpIsAbnormal = true;
                if (bpOverallStatus !== 'critical') bpOverallStatus = 'warning';
            }

            if (bpIsAbnormal) {
                bpValueDisplay.className = `vital-value ${bpOverallStatus}`;
                allVitalsEffectivelyNormal = false;
            } else {
                bpValueDisplay.className = 'vital-value normal';
            }

            // Update Message Area
            let htmlMessages = "";
            if (spo2ChallengeInterval) {
                const currentChallengeSpo2 = parseInt(spo2Slider.value);
                if (currentChallengeSpo2 < VITAL_PARAMS_CONFIG.spo2.normalMin) {
                     htmlMessages += `<p class="status-message ${currentChallengeSpo2 < 90 ? 'critical-message' : 'warning-message'}">CHALLENGE: SpO2 is ${currentChallengeSpo2 < 90 ? 'critically low' : 'low'} at ${currentChallengeSpo2}%! Administer Oxygen!</p>`;
                } else {
                    htmlMessages += `<p class="status-message warning-message">CHALLENGE: SpO2 is ${currentChallengeSpo2}% and decreasing. Monitor closely.</p>`;
                }
            }

            if (alertMessages.length > 0) {
                // Use a Set to avoid duplicate messages if BP sys and dia trigger similar texts (though current texts are distinct)
                htmlMessages += [...new Set(alertMessages)].map(msg => `<p class="alert-message">${msg}</p>`).join('');
            }
            
            if (htmlMessages === "") {
                if (allVitalsEffectivelyNormal) { // Check if any vital triggered an alert state
                     messagesArea.innerHTML = '<p>All vital signs are within normal ranges. Adjust sliders to explore.</p>';
                } else {
                     // This case means allVitalsEffectivelyNormal is false, but alertMessages is empty AND no challenge.
                     // Should be rare if logic is correct.
                    messagesArea.innerHTML = '<p>An issue is detected, but no specific message generated. Please review vitals.</p>';
                }
            } else {
                messagesArea.innerHTML = htmlMessages;
            }
        }

        function startSpo2Challenge() {
            if (spo2ChallengeInterval) return; 

            challengeButton.disabled = true;
            oxygenButton.style.display = 'inline-block';
            
            // Ensure SpO2 starts from a normal value for the challenge if user manually lowered it.
            if (parseInt(spo2Slider.value) < VITAL_PARAMS_CONFIG.spo2.normalMin) {
                 spo2Slider.value = SPO2_NORMAL_VALUE;
            }

            spo2ChallengeInterval = setInterval(() => {
                let currentSpo2 = parseInt(spo2Slider.value);
                if (currentSpo2 > parseInt(spo2Slider.min)) { 
                    currentSpo2--;
                    spo2Slider.value = currentSpo2;
                    updateVitalSignsUI();
                } else { // Reached minimum
                    updateVitalSignsUI(); 
                    // Optionally clear interval if it hits absolute min, though button is still needed
                    // clearInterval(spo2ChallengeInterval); 
                    // spo2ChallengeInterval = null;
                }
            }, SPO2_DECREMENT_INTERVAL_MS);
            updateVitalSignsUI(); 
        }

        function administerOxygen() {
            clearInterval(spo2ChallengeInterval);
            spo2ChallengeInterval = null;

            spo2Slider.value = SPO2_NORMAL_VALUE;
            
            oxygenButton.style.display = 'none';
            challengeButton.disabled = false;
            
            updateVitalSignsUI(); 
        }

        document.addEventListener('DOMContentLoaded', () => {
            [hrSlider, spo2Slider, etco2Slider, tempSlider, bpSysSlider, bpDiaSlider].forEach(slider => {
                slider.addEventListener('input', updateVitalSignsUI);
            });

            challengeButton.addEventListener('click', startSpo2Challenge);
            oxygenButton.addEventListener('click', administerOxygen);
            
            const placeholder = document.getElementById('info-message-placeholder');
            if (placeholder) {
                 messagesArea.innerHTML = placeholder.innerHTML;
                 placeholder.remove();
            } else {
                 updateVitalSignsUI(); // Fallback if placeholder somehow missing
            }
            
            updateVitalSignsUI(); // Initial call to set values and classes
        });
    </script>
</body>
</html>
