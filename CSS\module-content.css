/* ===== MODULE CONTENT STYLES ===== */

/* Module Header */
.module-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.module-header .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.module-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.back-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.back-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.module-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.module-title .module-icon {
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.module-title h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.module-progress-header {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
    min-width: 200px;
}

.progress-info {
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 0.9rem;
}

.progress-label {
    color: rgba(255, 255, 255, 0.8);
}

.progress-percentage {
    font-weight: 700;
    color: white;
}

.progress-bar-header {
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar-header .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Module Navigation */
.module-navigation {
    background: white;
    border-bottom: 1px solid #e8ecef;
    position: sticky;
    top: 80px;
    z-index: 99;
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.module-nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.module-nav-list::-webkit-scrollbar {
    display: none;
}

.module-nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: #7f8c8d;
    font-weight: 500;
    white-space: nowrap;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.module-nav-link:hover,
.module-nav-link.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.module-nav-link i {
    font-size: 1rem;
}

/* Content Sections */
.module-content {
    flex: 1;
    background: #f8f9fa;
}

.content-section {
    display: none;
    min-height: calc(100vh - 200px);
    padding: 3rem 0;
}

.content-section.active {
    display: block;
}

.section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.2rem;
    color: #7f8c8d;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.content-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8ecef;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.card-header i {
    font-size: 1.5rem;
    color: #667eea;
}

.card-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.objectives-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.objectives-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
    position: relative;
    padding-left: 2rem;
}

.objectives-list li:last-child {
    border-bottom: none;
}

.objectives-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
    font-size: 1.1rem;
}

.overview-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
}

.stat-item i {
    color: #667eea;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.stat-label {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

.stat-value {
    font-weight: 700;
    color: #667eea;
}

/* Video Container */
.video-container {
    margin: 3rem 0;
    text-align: center;
}

.video-placeholder {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border: 2px dashed #667eea;
    border-radius: 15px;
    padding: 3rem 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.video-placeholder i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1rem;
    display: block;
}

.video-placeholder h4 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.video-placeholder p {
    color: #7f8c8d;
    margin-bottom: 2rem;
}

/* Vital Signs Grid */
.vital-signs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.vital-sign-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8ecef;
    transition: all 0.3s ease;
}

.vital-sign-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.vital-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vital-icon i {
    font-size: 2rem;
    color: white;
}

.vital-sign-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.vital-info {
    margin-bottom: 1.5rem;
}

.normal-range {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(39, 174, 96, 0.1);
    border-radius: 8px;
}

.range-label {
    font-weight: 600;
    color: #27ae60;
}

.range-value {
    font-weight: 700;
    color: #27ae60;
}

.vital-description {
    color: #7f8c8d;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* Knowledge Check */
.knowledge-check {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 3rem 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border-left: 4px solid #f39c12;
}

.knowledge-check h3 {
    color: #f39c12;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.knowledge-check h3::before {
    content: '🧠';
    font-size: 1.2rem;
}

.quiz-question p {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.quiz-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.quiz-option {
    padding: 1rem;
    border: 2px solid #e8ecef;
    border-radius: 10px;
    background: white;
    color: #2c3e50;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quiz-option:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.quiz-option.correct {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.quiz-option.incorrect {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.quiz-feedback {
    padding: 1rem;
    border-radius: 8px;
    font-weight: 500;
    margin-top: 1rem;
    display: none;
}

.quiz-feedback.correct {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.quiz-feedback.incorrect {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Monitor Demo */
.monitor-demo {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin: 3rem 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.demo-monitor {
    max-width: 800px;
    margin: 0 auto 2rem;
}

.monitor-screen {
    background: #001f3f;
    color: #7fdbff;
    border-radius: 10px;
    padding: 1.5rem;
    font-family: 'Courier New', monospace;
}

.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #004080;
}

.patient-info {
    font-weight: bold;
    color: #a0cfff;
}

.monitor-time {
    color: #2ecc40;
    font-weight: bold;
}

.vitals-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.vital-display-item {
    text-align: center;
    padding: 1rem;
    background: #002b4f;
    border-radius: 8px;
    border: 1px solid #004080;
}

.vital-label {
    display: block;
    font-size: 0.9rem;
    color: #a0cfff;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.vital-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #2ecc40;
    margin-bottom: 0.25rem;
}

.vital-value.abnormal {
    color: #ff4136;
    animation: blink 1s infinite;
}

@keyframes blink {
    50% { opacity: 0.6; }
}

.vital-unit {
    font-size: 0.8rem;
    color: #80bfff;
}

.alarm-status {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(46, 204, 64, 0.2);
    border-radius: 8px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.alarm-status.alarm {
    background: rgba(255, 65, 54, 0.2);
    color: #ff4136;
}

.alarm-status i {
    font-size: 1.2rem;
}

.monitor-controls {
    text-align: center;
}

.monitor-controls h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.monitor-controls p {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
}

.control-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.btn-scenario {
    padding: 0.75rem 1.5rem;
    border: 2px solid #667eea;
    border-radius: 25px;
    background: white;
    color: #667eea;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-scenario:hover {
    background: #667eea;
    color: white;
}

/* Section Actions */
.section-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #e8ecef;
}

.section-actions .btn-primary,
.section-actions .btn-secondary {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Notifications */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-left: 4px solid #667eea;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 350px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left-color: #27ae60;
}

.notification-error {
    border-left-color: #e74c3c;
}

.notification-info {
    border-left-color: #3498db;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification-success .notification-content i {
    color: #27ae60;
}

.notification-error .notification-content i {
    color: #e74c3c;
}

.notification-info .notification-content i {
    color: #3498db;
}

.notification-content span {
    font-weight: 500;
    color: #2c3e50;
}

/* Progress Notification */
.progress-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border-radius: 50px;
    padding: 1rem 2rem;
    box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
    z-index: 10000;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.progress-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.progress-notification .notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-notification i {
    font-size: 1.2rem;
}

/* Animation Classes */
.animate-in {
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.scale-in {
    animation: scaleIn 0.3s ease forwards;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Mobile Navigation Styles */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mobile-nav {
    display: none;
}

.mobile-nav-open {
    display: block;
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.mobile-nav-open .module-nav-list {
    flex-direction: column;
    padding: 1rem 0;
}

.mobile-nav-open .module-nav-link {
    padding: 1rem 2rem;
    border-bottom: 1px solid #f1f3f4;
    border-left: none;
}

/* Breadcrumbs */
.breadcrumb-container {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 0;
    margin-top: 1rem;
}

.breadcrumbs {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: white;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb-current {
    color: white;
    font-weight: 500;
}

/* Scroll Progress Bar */
.scroll-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    z-index: 10001;
    transition: width 0.1s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .module-header .header-content {
        flex-direction: column;
        text-align: center;
    }

    .module-progress-header {
        align-items: center;
        width: 100%;
    }

    .progress-bar-header {
        width: 100%;
        max-width: 300px;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .module-nav-list {
        justify-content: flex-start;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .vital-signs-grid {
        grid-template-columns: 1fr;
    }

    .quiz-options {
        grid-template-columns: 1fr;
    }

    .control-buttons {
        flex-direction: column;
        align-items: center;
    }

    .section-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .section-actions .btn-primary,
    .section-actions .btn-secondary {
        width: 100%;
        max-width: 250px;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }

    .progress-notification {
        right: 10px;
        left: 10px;
        border-radius: 10px;
        text-align: center;
    }
}
