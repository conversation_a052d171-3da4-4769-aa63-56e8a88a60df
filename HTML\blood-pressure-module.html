<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blood Pressure Physiology Module - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/blood-pressure-module.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="../HTML/lectures.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Lectures
                </a>
                <div class="module-title">
                    <h1>Blood Pressure Physiology Module</h1>
                    <p>Cardiovascular Hemodynamics & Pressure Measurement</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Module Overview -->
    <section class="module-overview">
        <div class="overview-content">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-heart animated-heartbeat"></i>
                    </div>
                    <h3>Cardiovascular Physiology</h3>
                    <p>Master cardiac cycle, stroke volume, and hemodynamic principles</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%"></div>
                    </div>
                    <span class="progress-text">92% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-tachometer-alt animated-gauge"></i>
                    </div>
                    <h3>Pressure Measurement</h3>
                    <p>Oscillometric and auscultatory blood pressure measurement techniques</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 87%"></div>
                    </div>
                    <span class="progress-text">87% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-chart-line animated-pulse"></i>
                    </div>
                    <h3>Hemodynamics</h3>
                    <p>Vascular resistance, compliance, and pressure-flow relationships</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <span class="progress-text">85% Complete</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Blood Pressure Simulator -->
    <section class="learning-tools">
        <div class="tools-container">
            <h2 class="section-title">Virtual Blood Pressure System</h2>
            
            <!-- BP Measurement Simulator -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Interactive Blood Pressure Monitor</h3>
                    <p>Real-time oscillometric and auscultatory measurement simulation</p>
                </div>
                
                <div class="bp-measurement-system">
                    <!-- BP Monitor Display -->
                    <div class="bp-monitor-display">
                        <div class="monitor-screen">
                            <div class="screen-header">
                                <h4>Automated Blood Pressure Monitor</h4>
                                <div class="bp-status">
                                    <span class="status-light active" id="bpPowerStatus"></span>
                                    <span class="status-text">Ready</span>
                                    <div class="measurement-mode">
                                        <span class="mode-label">Mode:</span>
                                        <span class="mode-value" id="currentBPMode">Oscillometric</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pressure Display -->
                            <div class="pressure-display-section">
                                <div class="main-pressure-display">
                                    <div class="pressure-readings">
                                        <div class="systolic-reading">
                                            <span class="pressure-label">Systolic</span>
                                            <span class="pressure-value" id="systolicValue">120</span>
                                            <span class="pressure-unit">mmHg</span>
                                        </div>
                                        <div class="pressure-separator">/</div>
                                        <div class="diastolic-reading">
                                            <span class="pressure-label">Diastolic</span>
                                            <span class="pressure-value" id="diastolicValue">80</span>
                                            <span class="pressure-unit">mmHg</span>
                                        </div>
                                    </div>
                                    
                                    <div class="additional-readings">
                                        <div class="map-reading">
                                            <span class="reading-label">MAP:</span>
                                            <span class="reading-value" id="mapValue">93</span>
                                            <span class="reading-unit">mmHg</span>
                                        </div>
                                        <div class="pulse-reading">
                                            <span class="reading-label">Pulse:</span>
                                            <span class="reading-value" id="pulseValue">75</span>
                                            <span class="reading-unit">bpm</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Cuff Pressure Gauge -->
                                <div class="cuff-pressure-gauge">
                                    <div class="gauge-container">
                                        <svg class="pressure-gauge" viewBox="0 0 200 200">
                                            <!-- Gauge background -->
                                            <circle cx="100" cy="100" r="80" fill="none" stroke="#334155" stroke-width="8"/>
                                            
                                            <!-- Gauge scale -->
                                            <g class="gauge-scale">
                                                <text x="100" y="40" class="scale-label">300</text>
                                                <text x="160" y="70" class="scale-label">200</text>
                                                <text x="180" y="110" class="scale-label">100</text>
                                                <text x="160" y="150" class="scale-label">50</text>
                                                <text x="100" y="180" class="scale-label">0</text>
                                                <text x="40" y="150" class="scale-label">50</text>
                                                <text x="20" y="110" class="scale-label">100</text>
                                                <text x="40" y="70" class="scale-label">200</text>
                                            </g>
                                            
                                            <!-- Gauge needle -->
                                            <line x1="100" y1="100" x2="100" y2="30" class="gauge-needle" id="pressureNeedle" transform="rotate(0 100 100)"/>
                                            <circle cx="100" cy="100" r="8" class="gauge-center"/>
                                        </svg>
                                        <div class="gauge-label">Cuff Pressure (mmHg)</div>
                                        <div class="gauge-value" id="cuffPressureValue">0</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Oscillometric Waveform -->
                            <div class="waveform-section">
                                <div class="waveform-display">
                                    <div class="waveform-label">Oscillometric Waveform</div>
                                    <canvas id="oscillometricWaveform" width="600" height="150"></canvas>
                                    <div class="waveform-markers">
                                        <div class="marker systolic-marker" id="systolicMarker">
                                            <span>Systolic</span>
                                        </div>
                                        <div class="marker diastolic-marker" id="diastolicMarker">
                                            <span>Diastolic</span>
                                        </div>
                                        <div class="marker map-marker" id="mapMarker">
                                            <span>MAP</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="arterial-waveform-display">
                                    <div class="waveform-label">Arterial Pressure Waveform</div>
                                    <canvas id="arterialWaveform" width="600" height="120"></canvas>
                                    <div class="waveform-annotations">
                                        <span class="annotation systolic-annotation">Systolic Peak</span>
                                        <span class="annotation dicrotic-annotation">Dicrotic Notch</span>
                                        <span class="annotation diastolic-annotation">Diastolic Minimum</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Classification Display -->
                            <div class="classification-section">
                                <h5>AHA 2017 Blood Pressure Classification</h5>
                                <div class="bp-classification" id="bpClassification">
                                    <div class="classification-result normal">
                                        <i class="fas fa-check-circle"></i>
                                        <span class="classification-text">Normal Blood Pressure</span>
                                        <span class="classification-range">&lt;120/80 mmHg</span>
                                    </div>
                                </div>
                                
                                <div class="classification-table">
                                    <div class="table-header">
                                        <span>Category</span>
                                        <span>Systolic</span>
                                        <span>Diastolic</span>
                                    </div>
                                    <div class="table-row normal">
                                        <span>Normal</span>
                                        <span>&lt;120</span>
                                        <span>&lt;80</span>
                                    </div>
                                    <div class="table-row elevated">
                                        <span>Elevated</span>
                                        <span>120-129</span>
                                        <span>&lt;80</span>
                                    </div>
                                    <div class="table-row stage1">
                                        <span>Stage 1 HTN</span>
                                        <span>130-139</span>
                                        <span>80-89</span>
                                    </div>
                                    <div class="table-row stage2">
                                        <span>Stage 2 HTN</span>
                                        <span>≥140</span>
                                        <span>≥90</span>
                                    </div>
                                    <div class="table-row crisis">
                                        <span>Crisis</span>
                                        <span>&gt;180</span>
                                        <span>&gt;120</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- BP Controls -->
                        <div class="bp-controls">
                            <div class="control-section">
                                <h5>Measurement Control</h5>
                                <div class="measurement-controls">
                                    <button class="control-btn start-measurement" onclick="startBPMeasurement()">
                                        <i class="fas fa-play"></i>
                                        Start Measurement
                                    </button>
                                    <button class="control-btn stop-measurement" onclick="stopBPMeasurement()">
                                        <i class="fas fa-stop"></i>
                                        Stop
                                    </button>
                                    <button class="control-btn deflate-cuff" onclick="deflateCuff()">
                                        <i class="fas fa-arrow-down"></i>
                                        Deflate Cuff
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Patient Scenarios</h5>
                                <div class="scenario-controls">
                                    <button class="scenario-btn" onclick="loadBPScenario('normal')">
                                        <i class="fas fa-user"></i>
                                        Normal Adult
                                    </button>
                                    <button class="scenario-btn" onclick="loadBPScenario('hypertensive')">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Hypertensive
                                    </button>
                                    <button class="scenario-btn" onclick="loadBPScenario('hypotensive')">
                                        <i class="fas fa-arrow-down"></i>
                                        Hypotensive
                                    </button>
                                    <button class="scenario-btn" onclick="loadBPScenario('elderly')">
                                        <i class="fas fa-user-clock"></i>
                                        Elderly
                                    </button>
                                    <button class="scenario-btn" onclick="loadBPScenario('athlete')">
                                        <i class="fas fa-running"></i>
                                        Athlete
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Measurement Settings</h5>
                                <div class="settings-controls">
                                    <div class="setting-group">
                                        <label>Cuff Size</label>
                                        <select onchange="changeCuffSize(this.value)">
                                            <option value="adult">Adult (22-32 cm)</option>
                                            <option value="large">Large Adult (32-42 cm)</option>
                                            <option value="pediatric">Pediatric (16-22 cm)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="setting-group">
                                        <label>Measurement Mode</label>
                                        <select onchange="changeMeasurementMode(this.value)">
                                            <option value="oscillometric">Oscillometric</option>
                                            <option value="auscultatory">Auscultatory</option>
                                            <option value="invasive">Invasive (A-line)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="setting-group">
                                        <label>Inflation Pressure</label>
                                        <input type="range" min="160" max="220" value="180" onchange="setInflationPressure(this.value)">
                                        <span id="inflationPressureValue">180 mmHg</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cardiovascular System Diagram -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Cardiovascular System Dynamics</h3>
                    <p>Interactive visualization of cardiac cycle and hemodynamic principles</p>
                </div>
                
                <div class="diagram-container">
                    <div class="diagram-tabs">
                        <button class="tab-btn active" onclick="showCardiacDiagram('cycle')">Cardiac Cycle</button>
                        <button class="tab-btn" onclick="showCardiacDiagram('hemodynamics')">Hemodynamics</button>
                        <button class="tab-btn" onclick="showCardiacDiagram('regulation')">BP Regulation</button>
                    </div>
                    
                    <!-- Cardiac Cycle Diagram -->
                    <div id="cardiacCycleDiagram" class="diagram-panel active">
                        <div class="cardiac-visualization">
                            <div class="heart-animation">
                                <svg viewBox="0 0 400 300" class="animated-heart">
                                    <!-- Heart chambers -->
                                    <g class="heart-chambers">
                                        <!-- Left ventricle -->
                                        <path d="M 150 120 Q 200 100 250 120 Q 280 150 250 200 Q 200 240 150 200 Q 120 150 150 120 Z" 
                                              class="left-ventricle" id="leftVentricle"/>
                                        
                                        <!-- Right ventricle -->
                                        <path d="M 100 130 Q 140 110 180 130 Q 200 160 180 200 Q 140 230 100 200 Q 80 160 100 130 Z" 
                                              class="right-ventricle" id="rightVentricle"/>
                                        
                                        <!-- Left atrium -->
                                        <ellipse cx="200" cy="90" rx="40" ry="25" class="left-atrium" id="leftAtrium"/>
                                        
                                        <!-- Right atrium -->
                                        <ellipse cx="140" cy="95" rx="35" ry="20" class="right-atrium" id="rightAtrium"/>
                                    </g>
                                    
                                    <!-- Valves -->
                                    <g class="heart-valves">
                                        <line x1="170" y1="115" x2="190" y2="115" class="mitral-valve" id="mitralValve"/>
                                        <line x1="120" y1="120" x2="140" y2="120" class="tricuspid-valve" id="tricuspidValve"/>
                                        <line x1="200" y1="100" x2="220" y2="100" class="aortic-valve" id="aorticValve"/>
                                        <line x1="140" y1="105" x2="160" y2="105" class="pulmonary-valve" id="pulmonaryValve"/>
                                    </g>
                                    
                                    <!-- Blood flow arrows -->
                                    <g class="blood-flow">
                                        <path d="M 50 90 L 100 90" class="flow-arrow venous-return" marker-end="url(#arrowhead)"/>
                                        <path d="M 250 90 L 300 90" class="flow-arrow arterial-output" marker-end="url(#arrowhead)"/>
                                    </g>
                                    
                                    <!-- Labels -->
                                    <text x="200" y="50" class="heart-label">Cardiac Cycle Animation</text>
                                    <text x="50" y="80" class="flow-label">Venous Return</text>
                                    <text x="280" y="80" class="flow-label">Arterial Output</text>
                                </svg>
                            </div>
                            
                            <div class="pressure-volume-loop">
                                <canvas id="pvLoopCanvas" width="300" height="250"></canvas>
                                <div class="pv-loop-labels">
                                    <span class="x-label">Volume (mL)</span>
                                    <span class="y-label">Pressure (mmHg)</span>
                                </div>
                                <div class="pv-loop-phases">
                                    <div class="phase-indicator" id="isovolumetricContraction">1. Isovolumetric Contraction</div>
                                    <div class="phase-indicator" id="ejection">2. Ejection</div>
                                    <div class="phase-indicator" id="isovolumetricRelaxation">3. Isovolumetric Relaxation</div>
                                    <div class="phase-indicator" id="filling">4. Filling</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="cardiac-parameters">
                            <h6>Cardiac Parameters</h6>
                            <div class="parameter-display">
                                <div class="param-item">
                                    <span class="param-name">Stroke Volume</span>
                                    <span class="param-value" id="strokeVolume">70</span>
                                    <span class="param-unit">mL</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-name">Cardiac Output</span>
                                    <span class="param-value" id="cardiacOutput">5.25</span>
                                    <span class="param-unit">L/min</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-name">Ejection Fraction</span>
                                    <span class="param-value" id="ejectionFraction">60</span>
                                    <span class="param-unit">%</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-name">SVR</span>
                                    <span class="param-value" id="systemicVascularResistance">1200</span>
                                    <span class="param-unit">dynes·s/cm⁵</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Component Information Panel -->
                    <div class="component-info" id="cardiacComponentInfo">
                        <h4 id="cardiacComponentTitle">Cardiac Cycle Physiology</h4>
                        <p id="cardiacComponentDescription">The cardiac cycle consists of systole (contraction) and diastole (relaxation) phases. Blood pressure is generated by the heart's pumping action and regulated by multiple physiological mechanisms.</p>
                        <div id="cardiacComponentSpecs"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comprehensive BP Notes -->
    <section class="lecture-notes">
        <div class="notes-container">
            <h2 class="section-title">Blood Pressure Physiology Fundamentals</h2>
            
            <div class="notes-grid">
                <!-- Cardiovascular Physiology -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3>Cardiovascular Physiology</h3>
                    </div>
                    <div class="note-content">
                        <h4>Cardiac Cycle</h4>
                        <ul>
                            <li><strong>Systole:</strong> Ventricular contraction and ejection</li>
                            <li><strong>Diastole:</strong> Ventricular relaxation and filling</li>
                            <li><strong>Stroke Volume:</strong> Volume ejected per beat (70 mL)</li>
                            <li><strong>Cardiac Output:</strong> SV × HR (5-6 L/min)</li>
                        </ul>
                        
                        <h4>Hemodynamic Principles</h4>
                        <ul>
                            <li><strong>Blood Pressure:</strong> Force exerted by blood on vessel walls</li>
                            <li><strong>Systolic BP:</strong> Peak pressure during systole</li>
                            <li><strong>Diastolic BP:</strong> Minimum pressure during diastole</li>
                            <li><strong>Mean Arterial Pressure:</strong> DBP + 1/3(SBP-DBP)</li>
                        </ul>
                        
                        <h4>Determinants of Blood Pressure</h4>
                        <div class="reference-table">
                            <div class="table-row">
                                <span class="parameter">Cardiac Output</span>
                                <span class="range">SV × HR</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Systemic Vascular Resistance</span>
                                <span class="range">800-1200 dynes·s/cm⁵</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Blood Volume</span>
                                <span class="range">5-6 L (adult)</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Vessel Compliance</span>
                                <span class="range">Age-dependent</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Measurement Techniques -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3>Measurement Techniques</h3>
                    </div>
                    <div class="note-content">
                        <h4>Oscillometric Method</h4>
                        <ul>
                            <li><strong>Principle:</strong> Detects arterial wall oscillations</li>
                            <li><strong>Cuff inflation:</strong> Above systolic pressure</li>
                            <li><strong>Deflation:</strong> Gradual pressure release</li>
                            <li><strong>Algorithm:</strong> Maximum oscillation = MAP</li>
                        </ul>
                        
                        <h4>Auscultatory Method</h4>
                        <ul>
                            <li><strong>Korotkoff Sounds:</strong> Turbulent flow sounds</li>
                            <li><strong>Phase I:</strong> First sound = Systolic BP</li>
                            <li><strong>Phase V:</strong> Sound disappearance = Diastolic BP</li>
                            <li><strong>Accuracy:</strong> Gold standard for manual measurement</li>
                        </ul>
                        
                        <h4>AHA 2017 Classification</h4>
                        <ul>
                            <li><strong>Normal:</strong> &lt;120/80 mmHg</li>
                            <li><strong>Elevated:</strong> 120-129/&lt;80 mmHg</li>
                            <li><strong>Stage 1 HTN:</strong> 130-139/80-89 mmHg</li>
                            <li><strong>Stage 2 HTN:</strong> ≥140/≥90 mmHg</li>
                            <li><strong>Crisis:</strong> &gt;180/&gt;120 mmHg</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/blood-pressure-simulator.js"></script>
    <script src="../JS/cardiac-cycle.js"></script>
    <script>
        // Initialize module
        document.addEventListener('DOMContentLoaded', function() {
            initializeBloodPressureSimulator();
            initializeCardiacCycle();
            startBPAnimations();
        });
    </script>
</body>
</html>
