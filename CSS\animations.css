/* ===== LECTURE ANIMATIONS AND VISUAL AIDS ===== */

/* Slide Transition Animations */
.slide-enter {
    animation: slideEnter 0.6s ease-out forwards;
}

.slide-exit {
    animation: slideExit 0.6s ease-out forwards;
}

@keyframes slideEnter {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideExit {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-100px);
    }
}

/* Fade Animations */
.fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.fade-in-delay {
    animation: fadeIn 0.8s ease-out 0.3s forwards;
    opacity: 0;
}

.fade-in-delay-2 {
    animation: fadeIn 0.8s ease-out 0.6s forwards;
    opacity: 0;
}

.fade-in-delay-3 {
    animation: fadeIn 0.8s ease-out 0.9s forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scale Animations */
.scale-in {
    animation: scaleIn 0.5s ease-out forwards;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Bounce Animation */
.bounce-in {
    animation: bounceIn 0.8s ease-out forwards;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Typewriter Effect */
.typewriter {
    overflow: hidden;
    border-right: 2px solid #3498db;
    white-space: nowrap;
    animation: typing 2s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: #3498db; }
}

/* Highlight Animation */
.highlight {
    background: linear-gradient(120deg, #f1c40f 0%, #f1c40f 100%);
    background-repeat: no-repeat;
    background-size: 100% 0.2em;
    background-position: 0 88%;
    animation: highlight 0.8s ease-out forwards;
}

@keyframes highlight {
    from {
        background-size: 0% 0.2em;
    }
    to {
        background-size: 100% 0.2em;
    }
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

/* Slide-specific Visual Elements */

/* Heart Beat Animation for Vital Signs */
.heartbeat {
    animation: heartbeat 1.2s ease-in-out infinite;
    color: #e74c3c;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.3); }
    28% { transform: scale(1); }
    42% { transform: scale(1.3); }
    70% { transform: scale(1); }
}

/* ECG Wave Animation */
.ecg-wave {
    width: 100%;
    height: 100px;
    background: linear-gradient(to right, #2ecc71 0%, #2ecc71 100%);
    position: relative;
    overflow: hidden;
}

.ecg-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: #2ecc71;
    transform: translateY(-50%);
}

.ecg-pulse {
    position: absolute;
    top: 0;
    left: -50px;
    width: 50px;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        #2ecc71 20%, 
        #2ecc71 40%, 
        transparent 60%,
        #2ecc71 80%,
        transparent 100%);
    animation: ecgPulse 2s linear infinite;
}

@keyframes ecgPulse {
    0% { left: -50px; }
    100% { left: 100%; }
}

/* Blood Flow Animation */
.blood-flow {
    position: relative;
    width: 100%;
    height: 20px;
    background: #e74c3c;
    border-radius: 10px;
    overflow: hidden;
}

.blood-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #c0392b;
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    animation: bloodFlow 3s linear infinite;
}

.blood-particle:nth-child(2) { animation-delay: 0.5s; }
.blood-particle:nth-child(3) { animation-delay: 1s; }
.blood-particle:nth-child(4) { animation-delay: 1.5s; }
.blood-particle:nth-child(5) { animation-delay: 2s; }

@keyframes bloodFlow {
    0% { left: -10px; }
    100% { left: 100%; }
}

/* Breathing Animation */
.breathing {
    animation: breathing 4s ease-in-out infinite;
}

@keyframes breathing {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Gas Flow Animation */
.gas-flow {
    position: relative;
    width: 100%;
    height: 10px;
    background: #3498db;
    border-radius: 5px;
    overflow: hidden;
}

.gas-bubble {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #2980b9;
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    animation: gasFlow 2s linear infinite;
}

.gas-bubble:nth-child(2) { animation-delay: 0.3s; }
.gas-bubble:nth-child(3) { animation-delay: 0.6s; }
.gas-bubble:nth-child(4) { animation-delay: 0.9s; }

@keyframes gasFlow {
    0% { left: -10px; opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* Pressure Gauge Animation */
.pressure-gauge {
    position: relative;
    width: 120px;
    height: 120px;
    border: 8px solid #ecf0f1;
    border-radius: 50%;
    margin: 0 auto;
}

.pressure-needle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 40px;
    background: #e74c3c;
    transform-origin: bottom center;
    transform: translate(-50%, -100%) rotate(0deg);
    animation: pressureNeedle 3s ease-in-out infinite;
}

@keyframes pressureNeedle {
    0%, 100% { transform: translate(-50%, -100%) rotate(-45deg); }
    50% { transform: translate(-50%, -100%) rotate(45deg); }
}

/* Temperature Animation */
.temperature-rise {
    animation: temperatureRise 2s ease-in-out infinite alternate;
}

@keyframes temperatureRise {
    0% { 
        background: linear-gradient(to top, #3498db 0%, #3498db 30%, transparent 30%);
    }
    100% { 
        background: linear-gradient(to top, #e74c3c 0%, #e74c3c 80%, transparent 80%);
    }
}

/* Flowmeter Animation */
.flowmeter {
    position: relative;
    width: 40px;
    height: 200px;
    background: linear-gradient(to top, #ecf0f1, #bdc3c7);
    border-radius: 20px;
    margin: 0 auto;
    overflow: hidden;
}

.flowmeter-ball {
    position: absolute;
    bottom: 20px;
    left: 50%;
    width: 20px;
    height: 20px;
    background: #e74c3c;
    border-radius: 50%;
    transform: translateX(-50%);
    animation: flowmeterBall 3s ease-in-out infinite;
}

@keyframes flowmeterBall {
    0%, 100% { bottom: 20px; }
    50% { bottom: 160px; }
}

/* Lung Expansion Animation */
.lung-expansion {
    animation: lungExpansion 3s ease-in-out infinite;
}

@keyframes lungExpansion {
    0%, 100% { 
        transform: scale(1);
        opacity: 0.7;
    }
    50% { 
        transform: scale(1.2);
        opacity: 1;
    }
}

/* Circuit Flow Animation */
.circuit-flow {
    stroke-dasharray: 10, 5;
    animation: circuitFlow 2s linear infinite;
}

@keyframes circuitFlow {
    0% { stroke-dashoffset: 0; }
    100% { stroke-dashoffset: 15; }
}

/* Alarm Flash Animation */
.alarm-flash {
    animation: alarmFlash 0.5s ease-in-out infinite alternate;
}

@keyframes alarmFlash {
    0% { 
        background-color: #e74c3c;
        color: white;
    }
    100% { 
        background-color: #c0392b;
        color: #ecf0f1;
    }
}

/* Loading Spinner for Slides */
.slide-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #ecf0f1;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Interactive Elements */
.interactive-element {
    cursor: pointer;
    transition: all 0.3s ease;
}

.interactive-element:hover {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* Progress Indicators */
.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(#3498db 0deg, #ecf0f1 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.progress-text {
    position: relative;
    z-index: 1;
    font-weight: bold;
    color: #2c3e50;
}

/* Slide Content Animations */
.slide-content-animate .slide h1 {
    animation: slideInFromTop 0.8s ease-out;
}

.slide-content-animate .slide h2 {
    animation: slideInFromLeft 0.8s ease-out 0.2s both;
}

.slide-content-animate .slide p {
    animation: fadeIn 0.8s ease-out 0.4s both;
}

.slide-content-animate .slide ul li {
    animation: slideInFromRight 0.6s ease-out both;
}

.slide-content-animate .slide ul li:nth-child(1) { animation-delay: 0.6s; }
.slide-content-animate .slide ul li:nth-child(2) { animation-delay: 0.8s; }
.slide-content-animate .slide ul li:nth-child(3) { animation-delay: 1.0s; }
.slide-content-animate .slide ul li:nth-child(4) { animation-delay: 1.2s; }

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .typewriter {
        border-right: none;
        animation: none;
        white-space: normal;
    }
    
    .pulse,
    .heartbeat,
    .breathing,
    .ecg-pulse,
    .blood-particle,
    .gas-bubble {
        animation: none;
    }
}
