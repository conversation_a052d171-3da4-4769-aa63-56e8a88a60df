// ===== PATIENT MONITORING MODULE JAVASCRIPT =====

// Module State
const patientMonitoringState = {
    currentSection: 'introduction',
    sectionProgress: {
        'introduction': 0,
        'vital-signs': 0,
        'monitor-interface': 0,
        'emergency-scenarios': 0,
        'simulations': 0,
        'assessment': 0
    },
    totalProgress: 0,
    quizAnswered: false,
    monitorSimulation: {
        isRunning: false,
        currentCondition: 'normal',
        vitals: {
            hr: 75,
            bp: { systolic: 120, diastolic: 80 },
            spo2: 98,
            rr: 16,
            temp: 37.0
        }
    }
};

// Vital Signs Conditions
const vitalConditions = {
    normal: {
        hr: 75,
        bp: { systolic: 120, diastolic: 80 },
        spo2: 98,
        rr: 16,
        temp: 37.0,
        alarm: false,
        description: 'All parameters within normal limits'
    },
    tachycardia: {
        hr: 125,
        bp: { systolic: 130, diastolic: 85 },
        spo2: 97,
        rr: 20,
        temp: 37.2,
        alarm: true,
        description: 'Heart rate elevated - possible stress or cardiac issue'
    },
    hypotension: {
        hr: 95,
        bp: { systolic: 85, diastolic: 55 },
        spo2: 96,
        rr: 18,
        temp: 36.8,
        alarm: true,
        description: 'Blood pressure low - monitor for shock or dehydration'
    },
    hypoxia: {
        hr: 110,
        bp: { systolic: 140, diastolic: 90 },
        spo2: 88,
        rr: 28,
        temp: 37.1,
        alarm: true,
        description: 'Oxygen saturation critically low - immediate intervention required'
    },
    fever: {
        hr: 105,
        bp: { systolic: 115, diastolic: 75 },
        spo2: 97,
        rr: 22,
        temp: 39.2,
        alarm: true,
        description: 'Elevated temperature - possible infection or inflammatory response'
    }
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializePatientMonitoring();
    setupSectionNavigation();
    setupQuizInteraction();
    startMonitorSimulation();
    updateModuleProgress();
});

function initializePatientMonitoring() {
    console.log('Patient Monitoring Module - Initializing...');
    
    // Load saved progress
    loadModuleProgress();
    
    // Setup section observers
    setupSectionObserver();
    
    // Initialize monitor time
    updateMonitorTime();
    setInterval(updateMonitorTime, 1000);
    
    console.log('Patient Monitoring Module initialized');
}

// ===== SECTION NAVIGATION =====
function setupSectionNavigation() {
    const navLinks = document.querySelectorAll('.module-nav-link');
    const sections = document.querySelectorAll('.content-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetSection = link.dataset.section;
            navigateToSection(targetSection);
        });
    });
}

function navigateToSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update navigation
    updateActiveNavLink(sectionId);
    
    // Update state
    patientMonitoringState.currentSection = sectionId;
    
    // Track progress
    markSectionVisited(sectionId);
    
    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function updateActiveNavLink(sectionId) {
    const navLinks = document.querySelectorAll('.module-nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.dataset.section === sectionId) {
            link.classList.add('active');
        }
    });
}

function nextSection() {
    const sections = ['introduction', 'vital-signs', 'monitor-interface', 'emergency-scenarios', 'simulations', 'assessment'];
    const currentIndex = sections.indexOf(patientMonitoringState.currentSection);
    
    if (currentIndex < sections.length - 1) {
        const nextSectionId = sections[currentIndex + 1];
        navigateToSection(nextSectionId);
    }
}

function previousSection() {
    const sections = ['introduction', 'vital-signs', 'monitor-interface', 'emergency-scenarios', 'simulations', 'assessment'];
    const currentIndex = sections.indexOf(patientMonitoringState.currentSection);
    
    if (currentIndex > 0) {
        const prevSectionId = sections[currentIndex - 1];
        navigateToSection(prevSectionId);
    }
}

// ===== PROGRESS TRACKING =====
function markSectionVisited(sectionId) {
    if (patientMonitoringState.sectionProgress[sectionId] === 0) {
        patientMonitoringState.sectionProgress[sectionId] = 50; // 50% for visiting
        updateModuleProgress();
        saveModuleProgress();
    }
}

function markSectionCompleted(sectionId) {
    patientMonitoringState.sectionProgress[sectionId] = 100;
    updateModuleProgress();
    saveModuleProgress();
    
    // Show completion notification
    showSectionCompletionNotification(sectionId);
}

function updateModuleProgress() {
    const sections = Object.keys(patientMonitoringState.sectionProgress);
    const totalProgress = sections.reduce((sum, section) => {
        return sum + patientMonitoringState.sectionProgress[section];
    }, 0);
    
    const averageProgress = Math.round(totalProgress / sections.length);
    patientMonitoringState.totalProgress = averageProgress;
    
    // Update progress bar
    const progressBar = document.getElementById('moduleProgressBar');
    const progressText = document.getElementById('moduleProgressText');
    
    if (progressBar) {
        progressBar.style.width = `${averageProgress}%`;
    }
    
    if (progressText) {
        progressText.textContent = `${averageProgress}%`;
    }
    
    // Update global progress
    if (window.updateModuleProgress) {
        window.updateModuleProgress('patient-monitoring', averageProgress);
    }
}

function saveModuleProgress() {
    localStorage.setItem('patient_monitoring_progress', JSON.stringify(patientMonitoringState));
}

function loadModuleProgress() {
    const saved = localStorage.getItem('patient_monitoring_progress');
    if (saved) {
        const savedState = JSON.parse(saved);
        patientMonitoringState.sectionProgress = { ...patientMonitoringState.sectionProgress, ...savedState.sectionProgress };
        patientMonitoringState.totalProgress = savedState.totalProgress || 0;
    }
}

// ===== QUIZ INTERACTION =====
function setupQuizInteraction() {
    const quizOptions = document.querySelectorAll('.quiz-option');
    
    quizOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            if (patientMonitoringState.quizAnswered) return;
            
            handleQuizAnswer(e.target);
        });
    });
}

function handleQuizAnswer(selectedOption) {
    const isCorrect = selectedOption.dataset.answer === 'correct';
    const feedback = document.getElementById('quizFeedback');
    const allOptions = document.querySelectorAll('.quiz-option');
    
    // Disable all options
    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        if (option.dataset.answer === 'correct') {
            option.classList.add('correct');
        } else if (option === selectedOption && !isCorrect) {
            option.classList.add('incorrect');
        }
    });
    
    // Show feedback
    if (feedback) {
        feedback.style.display = 'block';
        if (isCorrect) {
            feedback.className = 'quiz-feedback correct';
            feedback.innerHTML = '<i class="fas fa-check-circle"></i> Correct! The normal resting heart rate for a healthy adult is 60-100 bpm.';
        } else {
            feedback.className = 'quiz-feedback incorrect';
            feedback.innerHTML = '<i class="fas fa-times-circle"></i> Incorrect. The correct answer is 60-100 bpm. This is the standard range for healthy adults at rest.';
        }
    }
    
    patientMonitoringState.quizAnswered = true;
    
    // Mark section as completed if quiz is answered
    if (patientMonitoringState.currentSection === 'vital-signs') {
        setTimeout(() => {
            markSectionCompleted('vital-signs');
        }, 2000);
    }
}

// ===== MONITOR SIMULATION =====
function startMonitorSimulation() {
    patientMonitoringState.monitorSimulation.isRunning = true;
    updateMonitorDisplay();
}

function simulateCondition(conditionName) {
    if (!vitalConditions[conditionName]) return;
    
    const condition = vitalConditions[conditionName];
    patientMonitoringState.monitorSimulation.currentCondition = conditionName;
    patientMonitoringState.monitorSimulation.vitals = { ...condition };
    
    // Animate the change
    animateVitalChange(condition);
    
    // Update alarm status
    updateAlarmStatus(condition);
    
    // Mark monitor interface section as completed if user tries different conditions
    if (patientMonitoringState.currentSection === 'monitor-interface') {
        setTimeout(() => {
            markSectionCompleted('monitor-interface');
        }, 1000);
    }
}

function animateVitalChange(targetVitals) {
    const duration = 2000; // 2 seconds
    const steps = 20;
    const stepDuration = duration / steps;
    
    const currentVitals = { ...patientMonitoringState.monitorSimulation.vitals };
    
    let step = 0;
    const interval = setInterval(() => {
        step++;
        const progress = step / steps;
        
        // Interpolate values
        const interpolatedVitals = {
            hr: Math.round(currentVitals.hr + (targetVitals.hr - currentVitals.hr) * progress),
            bp: {
                systolic: Math.round(currentVitals.bp.systolic + (targetVitals.bp.systolic - currentVitals.bp.systolic) * progress),
                diastolic: Math.round(currentVitals.bp.diastolic + (targetVitals.bp.diastolic - currentVitals.bp.diastolic) * progress)
            },
            spo2: Math.round(currentVitals.spo2 + (targetVitals.spo2 - currentVitals.spo2) * progress),
            rr: Math.round(currentVitals.rr + (targetVitals.rr - currentVitals.rr) * progress),
            temp: (currentVitals.temp + (targetVitals.temp - currentVitals.temp) * progress).toFixed(1)
        };
        
        updateMonitorDisplay(interpolatedVitals);
        
        if (step >= steps) {
            clearInterval(interval);
            patientMonitoringState.monitorSimulation.vitals = { ...targetVitals };
        }
    }, stepDuration);
}

function updateMonitorDisplay(vitals = null) {
    const currentVitals = vitals || patientMonitoringState.monitorSimulation.vitals;
    
    // Update display elements
    const hrElement = document.getElementById('demoHR');
    const bpElement = document.getElementById('demoBP');
    const spo2Element = document.getElementById('demoSpO2');
    const rrElement = document.getElementById('demoRR');
    const tempElement = document.getElementById('demoTemp');
    
    if (hrElement) {
        hrElement.textContent = currentVitals.hr;
        hrElement.className = 'vital-value' + (isAbnormal('hr', currentVitals.hr) ? ' abnormal' : '');
    }
    
    if (bpElement) {
        bpElement.textContent = `${currentVitals.bp.systolic}/${currentVitals.bp.diastolic}`;
        bpElement.className = 'vital-value' + (isAbnormal('bp', currentVitals.bp) ? ' abnormal' : '');
    }
    
    if (spo2Element) {
        spo2Element.textContent = currentVitals.spo2;
        spo2Element.className = 'vital-value' + (isAbnormal('spo2', currentVitals.spo2) ? ' abnormal' : '');
    }
    
    if (rrElement) {
        rrElement.textContent = currentVitals.rr;
        rrElement.className = 'vital-value' + (isAbnormal('rr', currentVitals.rr) ? ' abnormal' : '');
    }
    
    if (tempElement) {
        tempElement.textContent = currentVitals.temp;
        tempElement.className = 'vital-value' + (isAbnormal('temp', parseFloat(currentVitals.temp)) ? ' abnormal' : '');
    }
}

function isAbnormal(vitalType, value) {
    const normalRanges = {
        hr: { min: 60, max: 100 },
        spo2: { min: 95, max: 100 },
        rr: { min: 12, max: 20 },
        temp: { min: 36.5, max: 37.5 }
    };
    
    if (vitalType === 'bp') {
        return value.systolic < 90 || value.systolic > 120 || value.diastolic < 60 || value.diastolic > 80;
    }
    
    const range = normalRanges[vitalType];
    return range && (value < range.min || value > range.max);
}

function updateAlarmStatus(condition) {
    const alarmElement = document.getElementById('alarmStatus');
    if (!alarmElement) return;
    
    if (condition.alarm) {
        alarmElement.className = 'alarm-status alarm';
        alarmElement.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>ALARM: ${condition.description}</span>
        `;
    } else {
        alarmElement.className = 'alarm-status';
        alarmElement.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${condition.description}</span>
        `;
    }
}

function updateMonitorTime() {
    const timeElement = document.getElementById('monitorTime');
    if (timeElement) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', { hour12: false });
        timeElement.textContent = timeString;
    }
}

// ===== VITAL SIGNS EXPLORATION =====
function exploreVitalSign(vitalType) {
    // This would open a detailed exploration modal for the specific vital sign
    console.log(`Exploring vital sign: ${vitalType}`);
    
    // For now, just mark the vital signs section as more complete
    if (patientMonitoringState.currentSection === 'vital-signs') {
        patientMonitoringState.sectionProgress['vital-signs'] = Math.max(
            patientMonitoringState.sectionProgress['vital-signs'], 
            75
        );
        updateModuleProgress();
        saveModuleProgress();
    }
    
    // Show exploration notification
    showNotification(`Exploring ${vitalType.replace('-', ' ')} in detail...`, 'info');
}

// ===== VIDEO INTERACTION =====
function playIntroVideo() {
    // Simulate video playback
    showNotification('Playing introduction video...', 'info');
    
    // Mark introduction as completed
    setTimeout(() => {
        markSectionCompleted('introduction');
        showNotification('Introduction video completed!', 'success');
    }, 3000);
}

// ===== SECTION OBSERVER =====
function setupSectionObserver() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.id;
                if (sectionId && patientMonitoringState.currentSection !== sectionId) {
                    updateActiveNavLink(sectionId);
                    patientMonitoringState.currentSection = sectionId;
                    markSectionVisited(sectionId);
                }
            }
        });
    }, {
        threshold: 0.5,
        rootMargin: '-100px 0px -100px 0px'
    });
    
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        observer.observe(section);
    });
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function showSectionCompletionNotification(sectionId) {
    const sectionNames = {
        'introduction': 'Introduction',
        'vital-signs': 'Vital Signs Basics',
        'monitor-interface': 'Monitor Interface',
        'emergency-scenarios': 'Emergency Scenarios',
        'simulations': 'Interactive Simulations',
        'assessment': 'Assessment'
    };
    
    const sectionName = sectionNames[sectionId] || sectionId;
    showNotification(`${sectionName} section completed! 🎉`, 'success');
}

// ===== EXPORT FUNCTIONS =====
window.nextSection = nextSection;
window.previousSection = previousSection;
window.simulateCondition = simulateCondition;
window.exploreVitalSign = exploreVitalSign;
window.playIntroVideo = playIntroVideo;
