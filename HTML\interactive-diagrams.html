<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Diagrams Explorer - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/interactive-diagrams.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="../index.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Home
                </a>
                <div class="module-title">
                    <h1>Interactive Diagrams Explorer</h1>
                    <p>Detailed Block Diagrams, Circuit Schematics & Signal Flow Analysis</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="btn-control" onclick="exportDiagram()">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Diagram Categories Navigation -->
    <section class="diagram-categories">
        <div class="categories-container">
            <h2 class="section-title">Medical System Diagrams</h2>
            
            <div class="category-tabs">
                <button class="category-tab active" onclick="showCategory('patient-monitoring')" data-category="patient-monitoring">
                    <i class="fas fa-heartbeat"></i>
                    <span>Patient Monitoring</span>
                </button>
                <button class="category-tab" onclick="showCategory('anesthesia-machine')" data-category="anesthesia-machine">
                    <i class="fas fa-lungs"></i>
                    <span>Anesthesia Machine</span>
                </button>
                <button class="category-tab" onclick="showCategory('ecg-systems')" data-category="ecg-systems">
                    <i class="fas fa-chart-line"></i>
                    <span>ECG Systems</span>
                </button>
                <button class="category-tab" onclick="showCategory('ventilation')" data-category="ventilation">
                    <i class="fas fa-wind"></i>
                    <span>Mechanical Ventilation</span>
                </button>
                <button class="category-tab" onclick="showCategory('hemodynamics')" data-category="hemodynamics">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Hemodynamics</span>
                </button>
                <button class="category-tab" onclick="showCategory('gas-flow')" data-category="gas-flow">
                    <i class="fas fa-atom"></i>
                    <span>Gas Flow Systems</span>
                </button>
            </div>
        </div>
    </section>

    <!-- Diagram Type Selector -->
    <section class="diagram-type-selector">
        <div class="selector-container">
            <div class="type-buttons">
                <button class="type-btn active" onclick="showDiagramType('block')" data-type="block">
                    <i class="fas fa-cubes"></i>
                    <span>Block Diagrams</span>
                </button>
                <button class="type-btn" onclick="showDiagramType('schematic')" data-type="schematic">
                    <i class="fas fa-microchip"></i>
                    <span>Circuit Schematics</span>
                </button>
                <button class="type-btn" onclick="showDiagramType('signal-flow')" data-type="signal-flow">
                    <i class="fas fa-project-diagram"></i>
                    <span>Signal Flow Analysis</span>
                </button>
            </div>
            
            <div class="diagram-controls">
                <button class="control-btn" onclick="zoomIn()">
                    <i class="fas fa-search-plus"></i>
                    Zoom In
                </button>
                <button class="control-btn" onclick="zoomOut()">
                    <i class="fas fa-search-minus"></i>
                    Zoom Out
                </button>
                <button class="control-btn" onclick="resetZoom()">
                    <i class="fas fa-expand-arrows-alt"></i>
                    Reset
                </button>
                <button class="control-btn" onclick="toggleAnimation()">
                    <i class="fas fa-play" id="animationIcon"></i>
                    <span id="animationText">Start Animation</span>
                </button>
            </div>
        </div>
    </section>

    <!-- Main Diagram Display Area -->
    <section class="diagram-display-area">
        <div class="display-container">
            <!-- Patient Monitoring Diagrams -->
            <div id="patient-monitoring-diagrams" class="diagram-category-panel active">
                
                <!-- Block Diagrams -->
                <div class="diagram-type-panel block active" data-type="block">
                    <div class="diagram-header">
                        <h3>Patient Monitoring System - Block Diagram</h3>
                        <p>Complete system architecture showing signal acquisition, processing, and display components</p>
                    </div>
                    
                    <div class="interactive-diagram-container">
                        <svg id="patientMonitoringBlock" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Background -->
                            <defs>
                                <linearGradient id="blockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
                                </linearGradient>
                                
                                <filter id="glow">
                                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                    <feMerge> 
                                        <feMergeNode in="coloredBlur"/>
                                        <feMergeNode in="SourceGraphic"/>
                                    </feMerge>
                                </filter>
                                
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
                                </marker>
                            </defs>
                            
                            <!-- Patient Interface -->
                            <g class="block-component" id="patientInterface" data-component="patient-interface">
                                <rect x="50" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#3b82f6" stroke-width="2"/>
                                <text x="125" y="130" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">PATIENT</text>
                                <text x="125" y="150" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">INTERFACE</text>
                                <text x="125" y="165" text-anchor="middle" fill="#94a3b8" font-size="10">Electrodes, Sensors</text>
                            </g>
                            
                            <!-- Signal Acquisition -->
                            <g class="block-component" id="signalAcquisition" data-component="signal-acquisition">
                                <rect x="300" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#10b981" stroke-width="2"/>
                                <text x="375" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">SIGNAL</text>
                                <text x="375" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ACQUISITION</text>
                                <text x="375" y="155" text-anchor="middle" fill="#94a3b8" font-size="10">Amplification</text>
                                <text x="375" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">Filtering</text>
                            </g>
                            
                            <!-- ADC -->
                            <g class="block-component" id="adcBlock" data-component="adc">
                                <rect x="550" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#8b5cf6" stroke-width="2"/>
                                <text x="625" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ANALOG-TO-</text>
                                <text x="625" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DIGITAL</text>
                                <text x="625" y="155" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">CONVERTER</text>
                                <text x="625" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">12-bit, 1kHz</text>
                            </g>
                            
                            <!-- Digital Processing -->
                            <g class="block-component" id="digitalProcessing" data-component="digital-processing">
                                <rect x="800" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#f59e0b" stroke-width="2"/>
                                <text x="875" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DIGITAL</text>
                                <text x="875" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">PROCESSING</text>
                                <text x="875" y="155" text-anchor="middle" fill="#94a3b8" font-size="10">DSP Algorithm</text>
                                <text x="875" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">Feature Extraction</text>
                            </g>
                            
                            <!-- Display System -->
                            <g class="block-component" id="displaySystem" data-component="display-system">
                                <rect x="1000" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#ef4444" stroke-width="2"/>
                                <text x="1075" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DISPLAY</text>
                                <text x="1075" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">SYSTEM</text>
                                <text x="1075" y="155" text-anchor="middle" fill="#94a3b8" font-size="10">LCD Monitor</text>
                                <text x="1075" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">Waveforms</text>
                            </g>
                            
                            <!-- ECG Processing Branch -->
                            <g class="block-component" id="ecgProcessing" data-component="ecg-processing">
                                <rect x="300" y="250" width="150" height="60" rx="8" 
                                      fill="url(#blockGradient)" stroke="#06b6d4" stroke-width="2"/>
                                <text x="375" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">ECG PROCESSING</text>
                                <text x="375" y="285" text-anchor="middle" fill="#94a3b8" font-size="9">Lead Selection</text>
                                <text x="375" y="298" text-anchor="middle" fill="#94a3b8" font-size="9">QRS Detection</text>
                            </g>
                            
                            <!-- SpO2 Processing Branch -->
                            <g class="block-component" id="spo2Processing" data-component="spo2-processing">
                                <rect x="500" y="250" width="150" height="60" rx="8" 
                                      fill="url(#blockGradient)" stroke="#84cc16" stroke-width="2"/>
                                <text x="575" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">SpO₂ PROCESSING</text>
                                <text x="575" y="285" text-anchor="middle" fill="#94a3b8" font-size="9">Pulse Detection</text>
                                <text x="575" y="298" text-anchor="middle" fill="#94a3b8" font-size="9">Saturation Calc</text>
                            </g>
                            
                            <!-- NIBP Processing Branch -->
                            <g class="block-component" id="nibpProcessing" data-component="nibp-processing">
                                <rect x="700" y="250" width="150" height="60" rx="8" 
                                      fill="url(#blockGradient)" stroke="#f97316" stroke-width="2"/>
                                <text x="775" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">NIBP PROCESSING</text>
                                <text x="775" y="285" text-anchor="middle" fill="#94a3b8" font-size="9">Oscillometric</text>
                                <text x="775" y="298" text-anchor="middle" fill="#94a3b8" font-size="9">Algorithm</text>
                            </g>
                            
                            <!-- Alarm System -->
                            <g class="block-component" id="alarmSystem" data-component="alarm-system">
                                <rect x="550" y="400" width="200" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#dc2626" stroke-width="2"/>
                                <text x="650" y="425" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ALARM SYSTEM</text>
                                <text x="650" y="445" text-anchor="middle" fill="#94a3b8" font-size="10">Threshold Monitoring</text>
                                <text x="650" y="460" text-anchor="middle" fill="#94a3b8" font-size="10">Audio/Visual Alerts</text>
                                <text x="650" y="475" text-anchor="middle" fill="#94a3b8" font-size="10">Priority Classification</text>
                            </g>
                            
                            <!-- Data Storage -->
                            <g class="block-component" id="dataStorage" data-component="data-storage">
                                <rect x="900" y="400" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#7c3aed" stroke-width="2"/>
                                <text x="975" y="425" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DATA STORAGE</text>
                                <text x="975" y="445" text-anchor="middle" fill="#94a3b8" font-size="10">Trend Data</text>
                                <text x="975" y="460" text-anchor="middle" fill="#94a3b8" font-size="10">Event Logging</text>
                                <text x="975" y="475" text-anchor="middle" fill="#94a3b8" font-size="10">Report Generation</text>
                            </g>
                            
                            <!-- Signal Flow Arrows -->
                            <g class="signal-flow">
                                <!-- Main signal path -->
                                <line x1="200" y1="140" x2="300" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="450" y1="140" x2="550" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="700" y1="140" x2="800" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="950" y1="140" x2="1000" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                
                                <!-- Branch connections -->
                                <line x1="375" y1="180" x2="375" y2="250" stroke="#06b6d4" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="575" y1="180" x2="575" y2="250" stroke="#84cc16" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="775" y1="180" x2="775" y2="250" stroke="#f97316" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                
                                <!-- Alarm connections -->
                                <line x1="875" y1="180" x2="650" y2="400" stroke="#dc2626" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                
                                <!-- Storage connection -->
                                <line x1="875" y1="180" x2="975" y2="400" stroke="#7c3aed" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                            </g>
                            
                            <!-- Signal Labels -->
                            <g class="signal-labels">
                                <text x="250" y="130" text-anchor="middle" fill="#64748b" font-size="10">Analog Signals</text>
                                <text x="500" y="130" text-anchor="middle" fill="#64748b" font-size="10">Conditioned</text>
                                <text x="750" y="130" text-anchor="middle" fill="#64748b" font-size="10">Digital Data</text>
                                <text x="975" y="130" text-anchor="middle" fill="#64748b" font-size="10">Processed</text>
                            </g>
                            
                            <!-- Title -->
                            <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="18" font-weight="700">
                                Patient Monitoring System - Block Diagram
                            </text>
                            <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="12">
                                Signal Acquisition → Processing → Display Architecture
                            </text>
                        </svg>
                    </div>
                </div>
                
                <!-- Circuit Schematics -->
                <div class="diagram-type-panel schematic" data-type="schematic">
                    <div class="diagram-header">
                        <h3>Patient Monitoring - Circuit Schematic</h3>
                        <p>Detailed electronic circuit design for ECG signal acquisition and processing</p>
                    </div>
                    
                    <div class="interactive-diagram-container">
                        <svg id="patientMonitoringSchematic" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with circuit schematic -->
                        </svg>
                    </div>
                </div>
                
                <!-- Signal Flow Analysis -->
                <div class="diagram-type-panel signal-flow" data-type="signal-flow">
                    <div class="diagram-header">
                        <h3>Patient Monitoring - Signal Flow Analysis</h3>
                        <p>Real-time signal processing and data flow visualization</p>
                    </div>
                    
                    <div class="interactive-diagram-container">
                        <svg id="patientMonitoringSignalFlow" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with signal flow diagram -->
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Other category panels will be added here -->
        </div>
    </section>

    <!-- Component Information Panel -->
    <section class="component-info-panel">
        <div class="info-container">
            <div class="info-header">
                <h3 id="componentTitle">Select a Component</h3>
                <button class="close-info" onclick="closeComponentInfo()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="info-content">
                <div id="componentDescription">
                    <p>Click on any component in the diagram to view detailed specifications and functionality.</p>
                </div>
                <div id="componentSpecs"></div>
                <div id="componentConnections"></div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/interactive-diagrams.js"></script>
    <script>
        // Initialize diagrams
        document.addEventListener('DOMContentLoaded', function() {
            initializeInteractiveDiagrams();
        });
    </script>
</body>
</html>
