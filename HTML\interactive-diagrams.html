<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Diagrams Explorer - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/interactive-diagrams.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="index.html" class="nav-link" onclick="window.location.href='index.html'; return false;">
                    <i class="fas fa-arrow-left"></i>
                    Back to Home
                </a>
                <div class="module-title">
                    <h1>Interactive Diagrams Explorer</h1>
                    <p>Detailed Block Diagrams, Circuit Schematics & Signal Flow Analysis</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="btn-control" onclick="exportDiagram()">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Diagram Categories Navigation -->
    <section class="diagram-categories">
        <div class="categories-container">
            <h2 class="section-title">Medical System Diagrams</h2>
            
            <div class="category-tabs">
                <button class="category-tab active" onclick="showCategory('patient-monitoring')" data-category="patient-monitoring">
                    <i class="fas fa-heartbeat"></i>
                    <span>Patient Monitoring</span>
                </button>
                <button class="category-tab" onclick="showCategory('anesthesia-machine')" data-category="anesthesia-machine">
                    <i class="fas fa-lungs"></i>
                    <span>Anesthesia Machine</span>
                </button>
                <button class="category-tab" onclick="showCategory('ecg-systems')" data-category="ecg-systems">
                    <i class="fas fa-chart-line"></i>
                    <span>ECG Systems</span>
                </button>
                <button class="category-tab" onclick="showCategory('ventilation')" data-category="ventilation">
                    <i class="fas fa-wind"></i>
                    <span>Mechanical Ventilation</span>
                </button>
                <button class="category-tab" onclick="showCategory('hemodynamics')" data-category="hemodynamics">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Hemodynamics</span>
                </button>
                <button class="category-tab" onclick="showCategory('gas-flow')" data-category="gas-flow">
                    <i class="fas fa-atom"></i>
                    <span>Gas Flow Systems</span>
                </button>
            </div>
        </div>
    </section>

    <!-- Diagram Type Selector -->
    <section class="diagram-type-selector">
        <div class="selector-container">
            <div class="type-buttons">
                <button class="type-btn active" onclick="showDiagramType('block')" data-type="block">
                    <i class="fas fa-cubes"></i>
                    <span>Block Diagrams</span>
                </button>
                <button class="type-btn" onclick="showDiagramType('schematic')" data-type="schematic">
                    <i class="fas fa-microchip"></i>
                    <span>Circuit Schematics</span>
                </button>
                <button class="type-btn" onclick="showDiagramType('signal-flow')" data-type="signal-flow">
                    <i class="fas fa-project-diagram"></i>
                    <span>Signal Flow Analysis</span>
                </button>
            </div>
            
            <div class="diagram-controls">
                <button class="control-btn" onclick="zoomIn()">
                    <i class="fas fa-search-plus"></i>
                    Zoom In
                </button>
                <button class="control-btn" onclick="zoomOut()">
                    <i class="fas fa-search-minus"></i>
                    Zoom Out
                </button>
                <button class="control-btn" onclick="resetZoom()">
                    <i class="fas fa-expand-arrows-alt"></i>
                    Reset
                </button>
                <button class="control-btn" onclick="toggleAnimation()">
                    <i class="fas fa-play" id="animationIcon"></i>
                    <span id="animationText">Start Animation</span>
                </button>
            </div>
        </div>
    </section>

    <!-- Main Diagram Display Area -->
    <section class="diagram-display-area">
        <div class="display-container">
            <!-- Patient Monitoring Diagrams -->
            <div id="patient-monitoring-diagrams" class="diagram-category-panel active">
                
                <!-- Block Diagrams -->
                <div class="diagram-type-panel block active" data-type="block">
                    <div class="diagram-header">
                        <h3>Patient Monitoring System - Block Diagram</h3>
                        <p>Complete system architecture showing signal acquisition, processing, and display components</p>
                    </div>
                    
                    <div class="interactive-diagram-container">
                        <svg id="patientMonitoringBlock" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Background -->
                            <defs>
                                <linearGradient id="blockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#1e293b;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
                                </linearGradient>
                                
                                <filter id="glow">
                                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                    <feMerge> 
                                        <feMergeNode in="coloredBlur"/>
                                        <feMergeNode in="SourceGraphic"/>
                                    </feMerge>
                                </filter>
                                
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
                                </marker>
                            </defs>
                            
                            <!-- Patient Interface -->
                            <g class="block-component" id="patientInterface" data-component="patient-interface">
                                <rect x="50" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#3b82f6" stroke-width="2"/>
                                <text x="125" y="130" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">PATIENT</text>
                                <text x="125" y="150" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">INTERFACE</text>
                                <text x="125" y="165" text-anchor="middle" fill="#94a3b8" font-size="10">Electrodes, Sensors</text>
                            </g>
                            
                            <!-- Signal Acquisition -->
                            <g class="block-component" id="signalAcquisition" data-component="signal-acquisition">
                                <rect x="300" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#10b981" stroke-width="2"/>
                                <text x="375" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">SIGNAL</text>
                                <text x="375" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ACQUISITION</text>
                                <text x="375" y="155" text-anchor="middle" fill="#94a3b8" font-size="10">Amplification</text>
                                <text x="375" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">Filtering</text>
                            </g>
                            
                            <!-- ADC -->
                            <g class="block-component" id="adcBlock" data-component="adc">
                                <rect x="550" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#8b5cf6" stroke-width="2"/>
                                <text x="625" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ANALOG-TO-</text>
                                <text x="625" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DIGITAL</text>
                                <text x="625" y="155" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">CONVERTER</text>
                                <text x="625" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">12-bit, 1kHz</text>
                            </g>
                            
                            <!-- Digital Processing -->
                            <g class="block-component" id="digitalProcessing" data-component="digital-processing">
                                <rect x="800" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#f59e0b" stroke-width="2"/>
                                <text x="875" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DIGITAL</text>
                                <text x="875" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">PROCESSING</text>
                                <text x="875" y="155" text-anchor="middle" fill="#94a3b8" font-size="10">DSP Algorithm</text>
                                <text x="875" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">Feature Extraction</text>
                            </g>
                            
                            <!-- Display System -->
                            <g class="block-component" id="displaySystem" data-component="display-system">
                                <rect x="1000" y="100" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#ef4444" stroke-width="2"/>
                                <text x="1075" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DISPLAY</text>
                                <text x="1075" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">SYSTEM</text>
                                <text x="1075" y="155" text-anchor="middle" fill="#94a3b8" font-size="10">LCD Monitor</text>
                                <text x="1075" y="170" text-anchor="middle" fill="#94a3b8" font-size="10">Waveforms</text>
                            </g>
                            
                            <!-- ECG Processing Branch -->
                            <g class="block-component" id="ecgProcessing" data-component="ecg-processing">
                                <rect x="300" y="250" width="150" height="60" rx="8" 
                                      fill="url(#blockGradient)" stroke="#06b6d4" stroke-width="2"/>
                                <text x="375" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">ECG PROCESSING</text>
                                <text x="375" y="285" text-anchor="middle" fill="#94a3b8" font-size="9">Lead Selection</text>
                                <text x="375" y="298" text-anchor="middle" fill="#94a3b8" font-size="9">QRS Detection</text>
                            </g>
                            
                            <!-- SpO2 Processing Branch -->
                            <g class="block-component" id="spo2Processing" data-component="spo2-processing">
                                <rect x="500" y="250" width="150" height="60" rx="8" 
                                      fill="url(#blockGradient)" stroke="#84cc16" stroke-width="2"/>
                                <text x="575" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">SpO₂ PROCESSING</text>
                                <text x="575" y="285" text-anchor="middle" fill="#94a3b8" font-size="9">Pulse Detection</text>
                                <text x="575" y="298" text-anchor="middle" fill="#94a3b8" font-size="9">Saturation Calc</text>
                            </g>
                            
                            <!-- NIBP Processing Branch -->
                            <g class="block-component" id="nibpProcessing" data-component="nibp-processing">
                                <rect x="700" y="250" width="150" height="60" rx="8" 
                                      fill="url(#blockGradient)" stroke="#f97316" stroke-width="2"/>
                                <text x="775" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">NIBP PROCESSING</text>
                                <text x="775" y="285" text-anchor="middle" fill="#94a3b8" font-size="9">Oscillometric</text>
                                <text x="775" y="298" text-anchor="middle" fill="#94a3b8" font-size="9">Algorithm</text>
                            </g>
                            
                            <!-- Alarm System -->
                            <g class="block-component" id="alarmSystem" data-component="alarm-system">
                                <rect x="550" y="400" width="200" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#dc2626" stroke-width="2"/>
                                <text x="650" y="425" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ALARM SYSTEM</text>
                                <text x="650" y="445" text-anchor="middle" fill="#94a3b8" font-size="10">Threshold Monitoring</text>
                                <text x="650" y="460" text-anchor="middle" fill="#94a3b8" font-size="10">Audio/Visual Alerts</text>
                                <text x="650" y="475" text-anchor="middle" fill="#94a3b8" font-size="10">Priority Classification</text>
                            </g>
                            
                            <!-- Data Storage -->
                            <g class="block-component" id="dataStorage" data-component="data-storage">
                                <rect x="900" y="400" width="150" height="80" rx="10" 
                                      fill="url(#blockGradient)" stroke="#7c3aed" stroke-width="2"/>
                                <text x="975" y="425" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">DATA STORAGE</text>
                                <text x="975" y="445" text-anchor="middle" fill="#94a3b8" font-size="10">Trend Data</text>
                                <text x="975" y="460" text-anchor="middle" fill="#94a3b8" font-size="10">Event Logging</text>
                                <text x="975" y="475" text-anchor="middle" fill="#94a3b8" font-size="10">Report Generation</text>
                            </g>
                            
                            <!-- Signal Flow Arrows -->
                            <g class="signal-flow">
                                <!-- Main signal path -->
                                <line x1="200" y1="140" x2="300" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="450" y1="140" x2="550" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="700" y1="140" x2="800" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="950" y1="140" x2="1000" y2="140" stroke="#3b82f6" stroke-width="3" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                
                                <!-- Branch connections -->
                                <line x1="375" y1="180" x2="375" y2="250" stroke="#06b6d4" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="575" y1="180" x2="575" y2="250" stroke="#84cc16" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                <line x1="775" y1="180" x2="775" y2="250" stroke="#f97316" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                
                                <!-- Alarm connections -->
                                <line x1="875" y1="180" x2="650" y2="400" stroke="#dc2626" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                                
                                <!-- Storage connection -->
                                <line x1="875" y1="180" x2="975" y2="400" stroke="#7c3aed" stroke-width="2" 
                                      marker-end="url(#arrowhead)" class="flow-line"/>
                            </g>
                            
                            <!-- Signal Labels -->
                            <g class="signal-labels">
                                <text x="250" y="130" text-anchor="middle" fill="#64748b" font-size="10">Analog Signals</text>
                                <text x="500" y="130" text-anchor="middle" fill="#64748b" font-size="10">Conditioned</text>
                                <text x="750" y="130" text-anchor="middle" fill="#64748b" font-size="10">Digital Data</text>
                                <text x="975" y="130" text-anchor="middle" fill="#64748b" font-size="10">Processed</text>
                            </g>
                            
                            <!-- Title -->
                            <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="18" font-weight="700">
                                Patient Monitoring System - Block Diagram
                            </text>
                            <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="12">
                                Signal Acquisition → Processing → Display Architecture
                            </text>
                        </svg>
                    </div>
                </div>
                
                <!-- Circuit Schematics -->
                <div class="diagram-type-panel schematic" data-type="schematic">
                    <div class="diagram-header">
                        <h3>Patient Monitoring - Circuit Schematic</h3>
                        <p>Detailed electronic circuit design for ECG signal acquisition and processing</p>
                    </div>
                    
                    <div class="interactive-diagram-container">
                        <svg id="patientMonitoringSchematic" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with circuit schematic -->
                        </svg>
                    </div>
                </div>
                
                <!-- Signal Flow Analysis -->
                <div class="diagram-type-panel signal-flow" data-type="signal-flow">
                    <div class="diagram-header">
                        <h3>Patient Monitoring - Signal Flow Analysis</h3>
                        <p>Real-time signal processing and data flow visualization</p>
                    </div>
                    
                    <div class="interactive-diagram-container">
                        <svg id="patientMonitoringSignalFlow" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with signal flow diagram -->
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Anesthesia Machine Diagrams -->
            <div id="anesthesia-machine-diagrams" class="diagram-category-panel">

                <!-- Block Diagrams -->
                <div class="diagram-type-panel block active" data-type="block">
                    <div class="diagram-header">
                        <h3>Anesthesia Machine - SPDD System Block Diagram</h3>
                        <p>Source, Processing, Delivery, and Disposal system architecture</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="anesthesiaMachineBlock" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Background -->
                            <defs>
                                <linearGradient id="spddGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#1e293b;stop-opacity:1" />
                                </linearGradient>

                                <marker id="spddArrow" markerWidth="10" markerHeight="7"
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
                                </marker>
                            </defs>

                            <!-- SOURCE SECTION -->
                            <g class="spdd-section" id="sourceSection">
                                <rect x="50" y="80" width="250" height="200" rx="15"
                                      fill="url(#spddGradient)" stroke="#3b82f6" stroke-width="3"/>
                                <text x="175" y="110" text-anchor="middle" fill="#3b82f6" font-size="16" font-weight="700">SOURCE</text>

                                <!-- Gas Cylinders -->
                                <g class="block-component" id="gasCylinders" data-component="gas-cylinders">
                                    <rect x="70" y="130" width="60" height="40" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="100" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">GAS</text>
                                    <text x="100" y="158" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">CYLINDERS</text>
                                </g>

                                <!-- Pipeline Supply -->
                                <g class="block-component" id="pipelineSupply" data-component="pipeline-supply">
                                    <rect x="150" y="130" width="60" height="40" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="180" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">PIPELINE</text>
                                    <text x="180" y="158" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">SUPPLY</text>
                                </g>

                                <!-- Pressure Regulators -->
                                <g class="block-component" id="pressureRegulators" data-component="pressure-regulators">
                                    <rect x="70" y="190" width="140" height="40" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="140" y="205" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">PRESSURE REGULATORS</text>
                                    <text x="140" y="218" text-anchor="middle" fill="#94a3b8" font-size="8">50 PSI Output</text>
                                </g>

                                <!-- Safety Systems -->
                                <g class="block-component" id="sourceSafety" data-component="source-safety">
                                    <rect x="70" y="240" width="140" height="30" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="140" y="258" text-anchor="middle" fill="#ef4444" font-size="9" font-weight="600">SAFETY SYSTEMS</text>
                                </g>
                            </g>

                            <!-- PROCESSING SECTION -->
                            <g class="spdd-section" id="processingSection">
                                <rect x="350" y="80" width="250" height="200" rx="15"
                                      fill="url(#spddGradient)" stroke="#10b981" stroke-width="3"/>
                                <text x="475" y="110" text-anchor="middle" fill="#10b981" font-size="16" font-weight="700">PROCESSING</text>

                                <!-- Flowmeters -->
                                <g class="block-component" id="flowmeters" data-component="flowmeters">
                                    <rect x="370" y="130" width="80" height="40" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="410" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">FLOWMETERS</text>
                                    <text x="410" y="158" text-anchor="middle" fill="#94a3b8" font-size="8">Variable Orifice</text>
                                </g>

                                <!-- Vaporizers -->
                                <g class="block-component" id="vaporizers" data-component="vaporizers">
                                    <rect x="470" y="130" width="80" height="40" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="510" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">VAPORIZERS</text>
                                    <text x="510" y="158" text-anchor="middle" fill="#94a3b8" font-size="8">Agent Delivery</text>
                                </g>

                                <!-- Gas Mixing -->
                                <g class="block-component" id="gasMixing" data-component="gas-mixing">
                                    <rect x="370" y="190" width="180" height="40" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="460" y="205" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">GAS MIXING CHAMBER</text>
                                    <text x="460" y="218" text-anchor="middle" fill="#94a3b8" font-size="8">Fresh Gas Flow</text>
                                </g>

                                <!-- Safety Interlocks -->
                                <g class="block-component" id="processingSafety" data-component="processing-safety">
                                    <rect x="370" y="240" width="180" height="30" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="460" y="258" text-anchor="middle" fill="#ef4444" font-size="9" font-weight="600">HYPOXIC GUARD & INTERLOCKS</text>
                                </g>
                            </g>

                            <!-- DELIVERY SECTION -->
                            <g class="spdd-section" id="deliverySection">
                                <rect x="650" y="80" width="250" height="200" rx="15"
                                      fill="url(#spddGradient)" stroke="#8b5cf6" stroke-width="3"/>
                                <text x="775" y="110" text-anchor="middle" fill="#8b5cf6" font-size="16" font-weight="700">DELIVERY</text>

                                <!-- Breathing Circuit -->
                                <g class="block-component" id="breathingCircuit" data-component="breathing-circuit">
                                    <rect x="670" y="130" width="80" height="40" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="710" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">BREATHING</text>
                                    <text x="710" y="158" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">CIRCUIT</text>
                                </g>

                                <!-- Ventilator -->
                                <g class="block-component" id="ventilator" data-component="ventilator">
                                    <rect x="770" y="130" width="80" height="40" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="810" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">VENTILATOR</text>
                                    <text x="810" y="158" text-anchor="middle" fill="#94a3b8" font-size="8">Bellows/Piston</text>
                                </g>

                                <!-- Patient Interface -->
                                <g class="block-component" id="patientInterface" data-component="patient-interface-anesthesia">
                                    <rect x="670" y="190" width="180" height="40" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="760" y="205" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">PATIENT INTERFACE</text>
                                    <text x="760" y="218" text-anchor="middle" fill="#94a3b8" font-size="8">Mask / ETT / LMA</text>
                                </g>

                                <!-- Monitoring -->
                                <g class="block-component" id="deliveryMonitoring" data-component="delivery-monitoring">
                                    <rect x="670" y="240" width="180" height="30" rx="5"
                                          fill="rgba(6, 182, 212, 0.3)" stroke="#06b6d4" stroke-width="2"/>
                                    <text x="760" y="258" text-anchor="middle" fill="#06b6d4" font-size="9" font-weight="600">GAS MONITORING & ALARMS</text>
                                </g>
                            </g>

                            <!-- DISPOSAL SECTION -->
                            <g class="spdd-section" id="disposalSection">
                                <rect x="950" y="80" width="200" height="200" rx="15"
                                      fill="url(#spddGradient)" stroke="#f59e0b" stroke-width="3"/>
                                <text x="1050" y="110" text-anchor="middle" fill="#f59e0b" font-size="16" font-weight="700">DISPOSAL</text>

                                <!-- Scavenging System -->
                                <g class="block-component" id="scavengingSystem" data-component="scavenging-system">
                                    <rect x="970" y="130" width="160" height="40" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="1050" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">SCAVENGING</text>
                                    <text x="1050" y="158" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">SYSTEM</text>
                                </g>

                                <!-- Waste Gas Evacuation -->
                                <g class="block-component" id="wasteGasEvacuation" data-component="waste-gas-evacuation">
                                    <rect x="970" y="190" width="160" height="40" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="1050" y="205" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">WASTE GAS</text>
                                    <text x="1050" y="218" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">EVACUATION</text>
                                </g>

                                <!-- Environmental Safety -->
                                <g class="block-component" id="environmentalSafety" data-component="environmental-safety">
                                    <rect x="970" y="240" width="160" height="30" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="1050" y="258" text-anchor="middle" fill="#ef4444" font-size="9" font-weight="600">ENVIRONMENTAL SAFETY</text>
                                </g>
                            </g>

                            <!-- Gas Flow Arrows -->
                            <g class="gas-flow-arrows">
                                <!-- Source to Processing -->
                                <line x1="300" y1="180" x2="350" y2="180" stroke="#10b981" stroke-width="4"
                                      marker-end="url(#spddArrow)" class="flow-line"/>
                                <text x="325" y="170" text-anchor="middle" fill="#10b981" font-size="10" font-weight="600">Fresh Gas</text>

                                <!-- Processing to Delivery -->
                                <line x1="600" y1="180" x2="650" y2="180" stroke="#10b981" stroke-width="4"
                                      marker-end="url(#spddArrow)" class="flow-line"/>
                                <text x="625" y="170" text-anchor="middle" fill="#10b981" font-size="10" font-weight="600">Mixed Gas</text>

                                <!-- Delivery to Disposal -->
                                <line x1="900" y1="180" x2="950" y2="180" stroke="#f59e0b" stroke-width="4"
                                      marker-end="url(#spddArrow)" class="flow-line"/>
                                <text x="925" y="170" text-anchor="middle" fill="#f59e0b" font-size="10" font-weight="600">Waste Gas</text>

                                <!-- Patient Circuit Loop -->
                                <path d="M 760 190 Q 760 320 760 350 Q 900 350 900 320 Q 900 190 900 190"
                                      stroke="#8b5cf6" stroke-width="3" fill="none" stroke-dasharray="5,5" class="flow-line"/>
                                <text x="830" y="340" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">Patient Circuit</text>
                            </g>

                            <!-- Control Systems -->
                            <g class="control-systems">
                                <rect x="400" y="400" width="400" height="100" rx="10"
                                      fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="2"/>
                                <text x="600" y="425" text-anchor="middle" fill="#06b6d4" font-size="14" font-weight="700">CONTROL & MONITORING SYSTEMS</text>

                                <!-- Control Modules -->
                                <rect x="420" y="440" width="80" height="30" rx="5"
                                      fill="rgba(6, 182, 212, 0.2)" stroke="#06b6d4" stroke-width="1"/>
                                <text x="460" y="458" text-anchor="middle" fill="#06b6d4" font-size="9" font-weight="600">ELECTRONIC</text>

                                <rect x="520" y="440" width="80" height="30" rx="5"
                                      fill="rgba(6, 182, 212, 0.2)" stroke="#06b6d4" stroke-width="1"/>
                                <text x="560" y="458" text-anchor="middle" fill="#06b6d4" font-size="9" font-weight="600">PNEUMATIC</text>

                                <rect x="620" y="440" width="80" height="30" rx="5"
                                      fill="rgba(6, 182, 212, 0.2)" stroke="#06b6d4" stroke-width="1"/>
                                <text x="660" y="458" text-anchor="middle" fill="#06b6d4" font-size="9" font-weight="600">MECHANICAL</text>

                                <rect x="720" y="440" width="80" height="30" rx="5"
                                      fill="rgba(6, 182, 212, 0.2)" stroke="#06b6d4" stroke-width="1"/>
                                <text x="760" y="458" text-anchor="middle" fill="#06b6d4" font-size="9" font-weight="600">SOFTWARE</text>
                            </g>

                            <!-- Title -->
                            <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
                                Anesthesia Machine - SPDD System Architecture
                            </text>
                            <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
                                Source → Processing → Delivery → Disposal
                            </text>
                        </svg>
                    </div>
                </div>

                <!-- Circuit Schematics -->
                <div class="diagram-type-panel schematic" data-type="schematic">
                    <div class="diagram-header">
                        <h3>Anesthesia Machine - Pneumatic Circuit Schematic</h3>
                        <p>Detailed pneumatic and gas flow circuit design</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="anesthesiaMachineSchematic" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with circuit schematic -->
                        </svg>
                    </div>
                </div>

                <!-- Signal Flow Analysis -->
                <div class="diagram-type-panel signal-flow" data-type="signal-flow">
                    <div class="diagram-header">
                        <h3>Anesthesia Machine - Gas Flow Analysis</h3>
                        <p>Real-time gas flow dynamics and control system visualization</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="anesthesiaMachineSignalFlow" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with signal flow diagram -->
                        </svg>
                    </div>
                </div>
            </div>

            <!-- ECG Systems Diagrams -->
            <div id="ecg-systems-diagrams" class="diagram-category-panel">

                <!-- Block Diagrams -->
                <div class="diagram-type-panel block active" data-type="block">
                    <div class="diagram-header">
                        <h3>ECG Systems - 12-Lead Acquisition Block Diagram</h3>
                        <p>Complete electrocardiogram acquisition, processing, and analysis system</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="ecgSystemsBlock" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Background -->
                            <defs>
                                <linearGradient id="ecgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#0c4a6e;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
                                </linearGradient>

                                <marker id="ecgArrow" markerWidth="10" markerHeight="7"
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
                                </marker>
                            </defs>

                            <!-- Patient Electrode System -->
                            <g class="ecg-section" id="electrodeSystem">
                                <rect x="50" y="100" width="200" height="150" rx="15"
                                      fill="url(#ecgGradient)" stroke="#3b82f6" stroke-width="3"/>
                                <text x="150" y="130" text-anchor="middle" fill="#3b82f6" font-size="16" font-weight="700">ELECTRODE SYSTEM</text>

                                <!-- Limb Leads -->
                                <g class="block-component" id="limbLeads" data-component="limb-leads">
                                    <rect x="70" y="150" width="70" height="30" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="105" y="168" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">LIMB LEADS</text>
                                    <text x="105" y="178" text-anchor="middle" fill="#94a3b8" font-size="8">RA, LA, LL, RL</text>
                                </g>

                                <!-- Precordial Leads -->
                                <g class="block-component" id="precordialLeads" data-component="precordial-leads">
                                    <rect x="160" y="150" width="70" height="30" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="195" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PRECORDIAL</text>
                                    <text x="195" y="175" text-anchor="middle" fill="#94a3b8" font-size="8">V1-V6</text>
                                </g>

                                <!-- Lead Selection -->
                                <g class="block-component" id="leadSelection" data-component="lead-selection">
                                    <rect x="70" y="200" width="160" height="30" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="150" y="218" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">LEAD SELECTION MATRIX</text>
                                </g>
                            </g>

                            <!-- Signal Conditioning -->
                            <g class="ecg-section" id="signalConditioning">
                                <rect x="300" y="100" width="200" height="150" rx="15"
                                      fill="url(#ecgGradient)" stroke="#10b981" stroke-width="3"/>
                                <text x="400" y="130" text-anchor="middle" fill="#10b981" font-size="16" font-weight="700">SIGNAL CONDITIONING</text>

                                <!-- Differential Amplifier -->
                                <g class="block-component" id="diffAmplifier" data-component="differential-amplifier">
                                    <rect x="320" y="150" width="80" height="30" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="360" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">DIFFERENTIAL</text>
                                    <text x="360" y="175" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">AMPLIFIER</text>
                                </g>

                                <!-- Isolation Amplifier -->
                                <g class="block-component" id="isolationAmplifier" data-component="isolation-amplifier">
                                    <rect x="420" y="150" width="60" height="30" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="450" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">ISOLATION</text>
                                    <text x="450" y="175" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">AMP</text>
                                </g>

                                <!-- Filters -->
                                <g class="block-component" id="ecgFilters" data-component="ecg-filters">
                                    <rect x="320" y="200" width="160" height="30" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="400" y="218" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">FILTERS (0.05-150 Hz)</text>
                                </g>
                            </g>

                            <!-- Digital Processing -->
                            <g class="ecg-section" id="digitalProcessing">
                                <rect x="550" y="100" width="200" height="150" rx="15"
                                      fill="url(#ecgGradient)" stroke="#8b5cf6" stroke-width="3"/>
                                <text x="650" y="130" text-anchor="middle" fill="#8b5cf6" font-size="16" font-weight="700">DIGITAL PROCESSING</text>

                                <!-- ADC -->
                                <g class="block-component" id="ecgADC" data-component="ecg-adc">
                                    <rect x="570" y="150" width="60" height="30" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="600" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">ADC</text>
                                    <text x="600" y="175" text-anchor="middle" fill="#94a3b8" font-size="8">16-bit</text>
                                </g>

                                <!-- DSP -->
                                <g class="block-component" id="ecgDSP" data-component="ecg-dsp">
                                    <rect x="650" y="150" width="80" height="30" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="690" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">DSP ENGINE</text>
                                    <text x="690" y="175" text-anchor="middle" fill="#94a3b8" font-size="8">QRS Detection</text>
                                </g>

                                <!-- Analysis Algorithms -->
                                <g class="block-component" id="analysisAlgorithms" data-component="analysis-algorithms">
                                    <rect x="570" y="200" width="160" height="30" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="650" y="218" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">ANALYSIS ALGORITHMS</text>
                                </g>
                            </g>

                            <!-- Display & Output -->
                            <g class="ecg-section" id="displayOutput">
                                <rect x="800" y="100" width="200" height="150" rx="15"
                                      fill="url(#ecgGradient)" stroke="#ef4444" stroke-width="3"/>
                                <text x="900" y="130" text-anchor="middle" fill="#ef4444" font-size="16" font-weight="700">DISPLAY & OUTPUT</text>

                                <!-- Waveform Display -->
                                <g class="block-component" id="waveformDisplay" data-component="waveform-display">
                                    <rect x="820" y="150" width="80" height="30" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="860" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">WAVEFORM</text>
                                    <text x="860" y="175" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">DISPLAY</text>
                                </g>

                                <!-- Measurements -->
                                <g class="block-component" id="measurements" data-component="measurements">
                                    <rect x="920" y="150" width="60" height="30" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="950" y="165" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">MEASURE</text>
                                    <text x="950" y="175" text-anchor="middle" fill="#94a3b8" font-size="8">HR, Intervals</text>
                                </g>

                                <!-- Interpretation -->
                                <g class="block-component" id="interpretation" data-component="interpretation">
                                    <rect x="820" y="200" width="160" height="30" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="900" y="218" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">INTERPRETATION</text>
                                </g>
                            </g>

                            <!-- 12-Lead Configuration -->
                            <g class="lead-configuration">
                                <rect x="100" y="350" width="1000" height="200" rx="15"
                                      fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="2"/>
                                <text x="600" y="380" text-anchor="middle" fill="#06b6d4" font-size="16" font-weight="700">12-LEAD ECG CONFIGURATION</text>

                                <!-- Lead Groups -->
                                <g class="lead-group" id="limbLeadGroup">
                                    <rect x="150" y="400" width="200" height="80" rx="10"
                                          fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="250" y="425" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="600">LIMB LEADS</text>
                                    <text x="180" y="445" fill="#e2e8f0" font-size="12" font-weight="600">I, II, III</text>
                                    <text x="180" y="460" fill="#94a3b8" font-size="10">Frontal Plane</text>
                                    <text x="280" y="445" fill="#e2e8f0" font-size="12" font-weight="600">aVR, aVL, aVF</text>
                                    <text x="280" y="460" fill="#94a3b8" font-size="10">Augmented</text>
                                </g>

                                <g class="lead-group" id="precordialGroup">
                                    <rect x="400" y="400" width="200" height="80" rx="10"
                                          fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
                                    <text x="500" y="425" text-anchor="middle" fill="#10b981" font-size="14" font-weight="600">PRECORDIAL LEADS</text>
                                    <text x="430" y="445" fill="#e2e8f0" font-size="12" font-weight="600">V1, V2, V3</text>
                                    <text x="430" y="460" fill="#94a3b8" font-size="10">Septal/Anterior</text>
                                    <text x="530" y="445" fill="#e2e8f0" font-size="12" font-weight="600">V4, V5, V6</text>
                                    <text x="530" y="460" fill="#94a3b8" font-size="10">Lateral</text>
                                </g>

                                <g class="lead-group" id="anatomicalViews">
                                    <rect x="650" y="400" width="200" height="80" rx="10"
                                          fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="750" y="425" text-anchor="middle" fill="#8b5cf6" font-size="14" font-weight="600">ANATOMICAL VIEWS</text>
                                    <text x="680" y="445" fill="#e2e8f0" font-size="11" font-weight="600">Inferior: II, III, aVF</text>
                                    <text x="680" y="460" fill="#e2e8f0" font-size="11" font-weight="600">Lateral: I, aVL, V5, V6</text>
                                    <text x="680" y="475" fill="#e2e8f0" font-size="11" font-weight="600">Anterior: V1-V4</text>
                                </g>

                                <g class="lead-group" id="diagnosticCapabilities">
                                    <rect x="900" y="400" width="150" height="80" rx="10"
                                          fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="975" y="425" text-anchor="middle" fill="#f59e0b" font-size="14" font-weight="600">DIAGNOSTICS</text>
                                    <text x="920" y="445" fill="#e2e8f0" font-size="10" font-weight="600">• Arrhythmia Detection</text>
                                    <text x="920" y="458" fill="#e2e8f0" font-size="10" font-weight="600">• ST Segment Analysis</text>
                                    <text x="920" y="471" fill="#e2e8f0" font-size="10" font-weight="600">• Axis Determination</text>
                                </g>
                            </g>

                            <!-- Signal Flow Arrows -->
                            <g class="signal-flow-arrows">
                                <line x1="250" y1="175" x2="300" y2="175" stroke="#3b82f6" stroke-width="4"
                                      marker-end="url(#ecgArrow)" class="flow-line"/>
                                <line x1="500" y1="175" x2="550" y2="175" stroke="#10b981" stroke-width="4"
                                      marker-end="url(#ecgArrow)" class="flow-line"/>
                                <line x1="750" y1="175" x2="800" y2="175" stroke="#8b5cf6" stroke-width="4"
                                      marker-end="url(#ecgArrow)" class="flow-line"/>

                                <!-- Labels -->
                                <text x="275" y="165" text-anchor="middle" fill="#3b82f6" font-size="10" font-weight="600">Raw ECG</text>
                                <text x="525" y="165" text-anchor="middle" fill="#10b981" font-size="10" font-weight="600">Conditioned</text>
                                <text x="775" y="165" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">Digital</text>
                            </g>

                            <!-- Title -->
                            <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
                                ECG Systems - 12-Lead Acquisition & Analysis
                            </text>
                            <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
                                Electrodes → Conditioning → Processing → Display
                            </text>
                        </svg>
                    </div>
                </div>

                <!-- Circuit Schematics -->
                <div class="diagram-type-panel schematic" data-type="schematic">
                    <div class="diagram-header">
                        <h3>ECG Systems - Lead Configuration Schematic</h3>
                        <p>Detailed electrode placement and lead vector analysis</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="ecgSystemsSchematic" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with ECG schematic -->
                        </svg>
                    </div>
                </div>

                <!-- Signal Flow Analysis -->
                <div class="diagram-type-panel signal-flow" data-type="signal-flow">
                    <div class="diagram-header">
                        <h3>ECG Systems - Signal Processing Flow</h3>
                        <p>Real-time ECG signal processing and arrhythmia detection</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="ecgSystemsSignalFlow" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with signal flow diagram -->
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Mechanical Ventilation Diagrams -->
            <div id="mechanical-ventilation-diagrams" class="diagram-category-panel">

                <!-- Block Diagrams -->
                <div class="diagram-type-panel block active" data-type="block">
                    <div class="diagram-header">
                        <h3>Mechanical Ventilation - Respiratory Support Block Diagram</h3>
                        <p>Complete ventilator system architecture for respiratory support and monitoring</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="mechanicalVentilationBlock" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Background -->
                            <defs>
                                <linearGradient id="ventGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#065f46;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
                                </linearGradient>

                                <marker id="ventArrow" markerWidth="10" markerHeight="7"
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
                                </marker>
                            </defs>

                            <!-- Gas Supply System -->
                            <g class="vent-section" id="gasSupplySystem">
                                <rect x="50" y="80" width="200" height="180" rx="15"
                                      fill="url(#ventGradient)" stroke="#10b981" stroke-width="3"/>
                                <text x="150" y="110" text-anchor="middle" fill="#10b981" font-size="16" font-weight="700">GAS SUPPLY</text>

                                <!-- Oxygen Source -->
                                <g class="block-component" id="oxygenSource" data-component="oxygen-source">
                                    <rect x="70" y="130" width="70" height="35" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="105" y="148" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">OXYGEN</text>
                                    <text x="105" y="158" text-anchor="middle" fill="#94a3b8" font-size="8">21-100%</text>
                                </g>

                                <!-- Air Source -->
                                <g class="block-component" id="airSource" data-component="air-source">
                                    <rect x="160" y="130" width="70" height="35" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="195" y="148" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">AIR</text>
                                    <text x="195" y="158" text-anchor="middle" fill="#94a3b8" font-size="8">Medical Grade</text>
                                </g>

                                <!-- Gas Blender -->
                                <g class="block-component" id="gasBlender" data-component="gas-blender">
                                    <rect x="70" y="180" width="160" height="35" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="150" y="198" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">GAS BLENDER</text>
                                    <text x="150" y="208" text-anchor="middle" fill="#94a3b8" font-size="8">FiO₂ Control</text>
                                </g>

                                <!-- Pressure Regulation -->
                                <g class="block-component" id="pressureRegulation" data-component="pressure-regulation">
                                    <rect x="70" y="225" width="160" height="25" rx="5"
                                          fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
                                    <text x="150" y="240" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PRESSURE REGULATION</text>
                                </g>
                            </g>

                            <!-- Ventilator Control System -->
                            <g class="vent-section" id="ventilatorControl">
                                <rect x="300" y="80" width="200" height="180" rx="15"
                                      fill="url(#ventGradient)" stroke="#3b82f6" stroke-width="3"/>
                                <text x="400" y="110" text-anchor="middle" fill="#3b82f6" font-size="16" font-weight="700">VENTILATOR CONTROL</text>

                                <!-- Microprocessor -->
                                <g class="block-component" id="microprocessor" data-component="microprocessor">
                                    <rect x="320" y="130" width="80" height="35" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="360" y="148" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">CPU</text>
                                    <text x="360" y="158" text-anchor="middle" fill="#94a3b8" font-size="8">Control Logic</text>
                                </g>

                                <!-- Mode Selection -->
                                <g class="block-component" id="modeSelection" data-component="mode-selection">
                                    <rect x="420" y="130" width="60" height="35" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="450" y="145" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">MODES</text>
                                    <text x="450" y="155" text-anchor="middle" fill="#94a3b8" font-size="8">VCV/PCV</text>
                                </g>

                                <!-- Flow Control -->
                                <g class="block-component" id="flowControl" data-component="flow-control">
                                    <rect x="320" y="180" width="80" height="35" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="360" y="198" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">FLOW CTRL</text>
                                    <text x="360" y="208" text-anchor="middle" fill="#94a3b8" font-size="8">Servo Valve</text>
                                </g>

                                <!-- PEEP Control -->
                                <g class="block-component" id="peepControl" data-component="peep-control">
                                    <rect x="420" y="180" width="60" height="35" rx="5"
                                          fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="450" y="195" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PEEP</text>
                                    <text x="450" y="205" text-anchor="middle" fill="#94a3b8" font-size="8">0-30 cmH₂O</text>
                                </g>

                                <!-- Safety Systems -->
                                <g class="block-component" id="ventSafety" data-component="vent-safety">
                                    <rect x="320" y="225" width="160" height="25" rx="5"
                                          fill="rgba(239, 68, 68, 0.3)" stroke="#ef4444" stroke-width="2"/>
                                    <text x="400" y="240" text-anchor="middle" fill="#ef4444" font-size="9" font-weight="600">SAFETY & ALARMS</text>
                                </g>
                            </g>

                            <!-- Breathing Circuit -->
                            <g class="vent-section" id="breathingCircuitVent">
                                <rect x="550" y="80" width="200" height="180" rx="15"
                                      fill="url(#ventGradient)" stroke="#8b5cf6" stroke-width="3"/>
                                <text x="650" y="110" text-anchor="middle" fill="#8b5cf6" font-size="16" font-weight="700">BREATHING CIRCUIT</text>

                                <!-- Inspiratory Limb -->
                                <g class="block-component" id="inspiratoryLimb" data-component="inspiratory-limb">
                                    <rect x="570" y="130" width="70" height="35" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="605" y="145" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">INSPIRATORY</text>
                                    <text x="605" y="155" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">LIMB</text>
                                </g>

                                <!-- Expiratory Limb -->
                                <g class="block-component" id="expiratoryLimb" data-component="expiratory-limb">
                                    <rect x="660" y="130" width="70" height="35" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="695" y="145" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">EXPIRATORY</text>
                                    <text x="695" y="155" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">LIMB</text>
                                </g>

                                <!-- Humidifier -->
                                <g class="block-component" id="humidifier" data-component="humidifier">
                                    <rect x="570" y="180" width="70" height="35" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="605" y="198" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">HUMIDIFIER</text>
                                    <text x="605" y="208" text-anchor="middle" fill="#94a3b8" font-size="8">37°C, 100% RH</text>
                                </g>

                                <!-- Filters -->
                                <g class="block-component" id="ventFilters" data-component="vent-filters">
                                    <rect x="660" y="180" width="70" height="35" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="695" y="198" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">FILTERS</text>
                                    <text x="695" y="208" text-anchor="middle" fill="#94a3b8" font-size="8">HEPA/Bacterial</text>
                                </g>

                                <!-- Patient Connection -->
                                <g class="block-component" id="patientConnection" data-component="patient-connection">
                                    <rect x="570" y="225" width="160" height="25" rx="5"
                                          fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="650" y="240" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PATIENT CONNECTION</text>
                                </g>
                            </g>

                            <!-- Monitoring & Sensors -->
                            <g class="vent-section" id="ventMonitoring">
                                <rect x="800" y="80" width="200" height="180" rx="15"
                                      fill="url(#ventGradient)" stroke="#f59e0b" stroke-width="3"/>
                                <text x="900" y="110" text-anchor="middle" fill="#f59e0b" font-size="16" font-weight="700">MONITORING</text>

                                <!-- Pressure Sensors -->
                                <g class="block-component" id="pressureSensors" data-component="pressure-sensors">
                                    <rect x="820" y="130" width="70" height="35" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="855" y="145" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PRESSURE</text>
                                    <text x="855" y="155" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">SENSORS</text>
                                </g>

                                <!-- Flow Sensors -->
                                <g class="block-component" id="flowSensors" data-component="flow-sensors">
                                    <rect x="910" y="130" width="70" height="35" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="945" y="145" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">FLOW</text>
                                    <text x="945" y="155" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">SENSORS</text>
                                </g>

                                <!-- Volume Measurement -->
                                <g class="block-component" id="volumeMeasurement" data-component="volume-measurement">
                                    <rect x="820" y="180" width="70" height="35" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="855" y="195" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">VOLUME</text>
                                    <text x="855" y="205" text-anchor="middle" fill="#94a3b8" font-size="8">Tidal/Minute</text>
                                </g>

                                <!-- Gas Analysis -->
                                <g class="block-component" id="gasAnalysis" data-component="gas-analysis">
                                    <rect x="910" y="180" width="70" height="35" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="945" y="195" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">GAS</text>
                                    <text x="945" y="205" text-anchor="middle" fill="#94a3b8" font-size="8">O₂/CO₂</text>
                                </g>

                                <!-- Display System -->
                                <g class="block-component" id="ventDisplay" data-component="vent-display">
                                    <rect x="820" y="225" width="160" height="25" rx="5"
                                          fill="rgba(245, 158, 11, 0.3)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="900" y="240" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">DISPLAY & ALARMS</text>
                                </g>
                            </g>

                            <!-- Ventilation Modes -->
                            <g class="ventilation-modes">
                                <rect x="100" y="350" width="1000" height="200" rx="15"
                                      fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="2"/>
                                <text x="600" y="380" text-anchor="middle" fill="#06b6d4" font-size="16" font-weight="700">VENTILATION MODES & PARAMETERS</text>

                                <!-- Volume Control Modes -->
                                <g class="mode-group" id="volumeControlModes">
                                    <rect x="150" y="400" width="200" height="80" rx="10"
                                          fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
                                    <text x="250" y="425" text-anchor="middle" fill="#10b981" font-size="14" font-weight="600">VOLUME CONTROL</text>
                                    <text x="170" y="445" fill="#e2e8f0" font-size="11" font-weight="600">• VCV (Volume Control)</text>
                                    <text x="170" y="460" fill="#e2e8f0" font-size="11" font-weight="600">• SIMV (Synchronized)</text>
                                    <text x="170" y="475" fill="#e2e8f0" font-size="11" font-weight="600">• A/C (Assist/Control)</text>
                                </g>

                                <!-- Pressure Control Modes -->
                                <g class="mode-group" id="pressureControlModes">
                                    <rect x="400" y="400" width="200" height="80" rx="10"
                                          fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
                                    <text x="500" y="425" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="600">PRESSURE CONTROL</text>
                                    <text x="420" y="445" fill="#e2e8f0" font-size="11" font-weight="600">• PCV (Pressure Control)</text>
                                    <text x="420" y="460" fill="#e2e8f0" font-size="11" font-weight="600">• PSV (Pressure Support)</text>
                                    <text x="420" y="475" fill="#e2e8f0" font-size="11" font-weight="600">• CPAP (Continuous PAP)</text>
                                </g>

                                <!-- Advanced Modes -->
                                <g class="mode-group" id="advancedModes">
                                    <rect x="650" y="400" width="200" height="80" rx="10"
                                          fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
                                    <text x="750" y="425" text-anchor="middle" fill="#8b5cf6" font-size="14" font-weight="600">ADVANCED MODES</text>
                                    <text x="670" y="445" fill="#e2e8f0" font-size="11" font-weight="600">• PRVC (Pressure Regulated)</text>
                                    <text x="670" y="460" fill="#e2e8f0" font-size="11" font-weight="600">• APRV (Airway Pressure)</text>
                                    <text x="670" y="475" fill="#e2e8f0" font-size="11" font-weight="600">• BiPAP (Bilevel PAP)</text>
                                </g>

                                <!-- Parameters -->
                                <g class="mode-group" id="ventParameters">
                                    <rect x="900" y="400" width="150" height="80" rx="10"
                                          fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
                                    <text x="975" y="425" text-anchor="middle" fill="#f59e0b" font-size="14" font-weight="600">PARAMETERS</text>
                                    <text x="920" y="445" fill="#e2e8f0" font-size="10" font-weight="600">• Tidal Volume: 300-800mL</text>
                                    <text x="920" y="458" fill="#e2e8f0" font-size="10" font-weight="600">• Rate: 5-60 bpm</text>
                                    <text x="920" y="471" fill="#e2e8f0" font-size="10" font-weight="600">• PEEP: 0-30 cmH₂O</text>
                                </g>
                            </g>

                            <!-- Gas Flow Arrows -->
                            <g class="gas-flow-arrows">
                                <!-- Supply to Control -->
                                <line x1="250" y1="170" x2="300" y2="170" stroke="#10b981" stroke-width="4"
                                      marker-end="url(#ventArrow)" class="flow-line"/>
                                <text x="275" y="160" text-anchor="middle" fill="#10b981" font-size="10" font-weight="600">Mixed Gas</text>

                                <!-- Control to Circuit -->
                                <line x1="500" y1="170" x2="550" y2="170" stroke="#3b82f6" stroke-width="4"
                                      marker-end="url(#ventArrow)" class="flow-line"/>
                                <text x="525" y="160" text-anchor="middle" fill="#3b82f6" font-size="10" font-weight="600">Controlled</text>

                                <!-- Circuit to Monitoring -->
                                <line x1="750" y1="170" x2="800" y2="170" stroke="#8b5cf6" stroke-width="4"
                                      marker-end="url(#ventArrow)" class="flow-line"/>
                                <text x="775" y="160" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">Monitored</text>

                                <!-- Feedback Loop -->
                                <path d="M 900 260 Q 900 300 600 300 Q 300 300 300 260"
                                      stroke="#f59e0b" stroke-width="3" fill="none" stroke-dasharray="5,5" class="flow-line"/>
                                <text x="600" y="315" text-anchor="middle" fill="#f59e0b" font-size="10" font-weight="600">Feedback Control Loop</text>
                            </g>

                            <!-- Title -->
                            <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
                                Mechanical Ventilation - Respiratory Support System
                            </text>
                            <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
                                Gas Supply → Control → Breathing Circuit → Monitoring
                            </text>
                        </svg>
                    </div>
                </div>

                <!-- Circuit Schematics -->
                <div class="diagram-type-panel schematic" data-type="schematic">
                    <div class="diagram-header">
                        <h3>Mechanical Ventilation - Pneumatic Circuit Schematic</h3>
                        <p>Detailed pneumatic control and gas flow circuit design</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="mechanicalVentilationSchematic" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with ventilation schematic -->
                        </svg>
                    </div>
                </div>

                <!-- Signal Flow Analysis -->
                <div class="diagram-type-panel signal-flow" data-type="signal-flow">
                    <div class="diagram-header">
                        <h3>Mechanical Ventilation - Respiratory Mechanics Flow</h3>
                        <p>Real-time respiratory mechanics and ventilation parameter analysis</p>
                    </div>

                    <div class="interactive-diagram-container">
                        <svg id="mechanicalVentilationSignalFlow" class="interactive-diagram" viewBox="0 0 1200 800">
                            <!-- Will be populated with signal flow diagram -->
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Other category panels will be added here -->
        </div>
    </section>

    <!-- Component Information Panel -->
    <section class="component-info-panel">
        <div class="info-container">
            <div class="info-header">
                <h3 id="componentTitle">Select a Component</h3>
                <button class="close-info" onclick="closeComponentInfo()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="info-content">
                <div id="componentDescription">
                    <p>Click on any component in the diagram to view detailed specifications and functionality.</p>
                </div>
                <div id="componentSpecs"></div>
                <div id="componentConnections"></div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/interactive-diagrams.js"></script>
    <script>
        // Initialize diagrams
        document.addEventListener('DOMContentLoaded', function() {
            initializeInteractiveDiagrams();
        });
    </script>
</body>
</html>
