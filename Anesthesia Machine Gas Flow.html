<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 1.6;
        }
        .app-container {
            width: 100%;
            max-width: 900px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin: 10px;
            padding: 15px;
            box-sizing: border-box;
            border-radius: 8px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 1.8em;
            margin-bottom: 20px;
        }
        .diagram-container {
            width: 100%;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            background-color: #fdfdfd;
            border-radius: 5px;
            overflow: hidden; /* Ensures SVG fits nicely */
        }
        #anesthesiaDiagram {
            width: 100%;
            height: auto;
            display: block;
        }
        .controls-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            align-items: center;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .flow-control {
            margin: 10px;
            display: flex;
            flex-direction: column;
        }
        .flow-control label {
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #34495e;
        }
        .flow-control input[type="number"] {
            width: 120px; /* Increased width slightly */
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            font-size: 1em;
            box-sizing: border-box;
        }
        #oxygenFlushButton {
            padding: 10px 15px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: background-color 0.2s;
            margin-top: 10px; /* Added margin for spacing when wrapped */
        }
        #oxygenFlushButton:hover {
            background-color: #c0392b;
        }
        #oxygenFlushButton:active, #oxygenFlushButton.active { /* Class for active state */
            background-color: #a52a1a;
        }
        .info-panel {
            padding: 10px 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 0.9em;
        }
        .info-panel p {
            margin: 8px 0;
        }
        .error-message {
            color: #e74c3c;
            font-weight: bold;
            margin-top: 10px;
            padding: 8px;
            background-color: #fdd;
            border: 1px solid #e74c3c;
            border-radius: 3px;
        }

        /* SVG text styling */
        .gas-label { font-size: 12px; font-weight: bold; text-anchor: middle; fill: white; }
        .flow-meter-value { font-size: 12px; text-anchor: middle; fill: #000; font-weight: bold;}
        .component-label { font-size: 10px; text-anchor: middle; fill: #333; }
        .cgo-label {font-size: 12px; text-anchor: middle; fill: #333; font-weight: bold;}
        .vaporizer-label {font-size: 14px; text-anchor: middle; fill: white; font-weight: bold;}


        @media (max-width: 700px) {
            .controls-container { 
                flex-direction: column; 
                align-items: stretch; 
            }
            .flow-control { width: 100%; margin: 8px 0; }
            .flow-control input[type="number"] { width: 100%; }
            #oxygenFlushButton { width: 100%; margin-top: 15px; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>Anesthesia Machine Gas Flow</h1>

        <div class="diagram-container">
            <svg id="anesthesiaDiagram" viewBox="0 0 700 350"> <!-- Adjusted viewBox height -->
                <!-- Gas Cylinders -->
                <rect x="20" y="50" width="40" height="80" rx="5" ry="5" fill="#27ae60" /> <!-- O2 Green -->
                <text x="40" y="95" class="gas-label">O₂</text>
                <rect x="20" y="180" width="40" height="80" rx="5" ry="5" fill="#2980b9" /> <!-- N2O Blue -->
                <text x="40" y="225" class="gas-label">N₂O</text>

                <!-- Pressure Regulators -->
                <rect x="70" y="80" width="30" height="20" rx="2" ry="2" fill="#7f8c8d" />
                <text x="85" y="75" class="component-label">Reg</text>
                <rect x="70" y="210" width="30" height="20" rx="2" ry="2" fill="#7f8c8d" />
                <text x="85" y="205" class="component-label">Reg</text>

                <!-- Flow Meters -->
                <rect x="140" y="50" width="30" height="150" fill="rgba(255,255,255,0.5)" stroke="#34495e" stroke-width="2"/>
                <text x="155" y="40" class="component-label">O₂ Flow</text>
                <text id="o2FlowMeterValue" x="155" y="215" class="flow-meter-value">0.0</text> <!-- Below FM -->

                <rect x="190" y="50" width="30" height="150" fill="rgba(255,255,255,0.5)" stroke="#34495e" stroke-width="2"/>
                <text x="205" y="40" class="component-label">N₂O Flow</text>
                <text id="n2oFlowMeterValue" x="205" y="215" class="flow-meter-value">0.0</text> <!-- Below FM -->

                <!-- Gas Paths (these are the visible pipes for animation) -->
                <!-- O2 Path: Cyl -> Reg -> FM_Top -> FM_Bottom -> Merge -->
                <path id="o2Path" d="M60,90 H140 L140,50 L155,50 L155,200 L280,200" stroke="#2ecc71" stroke-width="6" fill="none"/>
                <!-- N2O Path: Cyl -> Reg -> FM_Top -> FM_Bottom -> Merge -->
                <path id="n2oPath" d="M60,220 H190 L190,50 L205,50 L205,200 L280,200" stroke="#3498db" stroke-width="6" fill="none"/>
                <!-- Common Path: Merge -> Through Vaporizer -> CGO -->
                <path id="commonPath" d="M280,200 H550" stroke="#95a5a6" stroke-width="8" fill="none"/>
                
                <!-- Vaporizer -->
                <rect x="330" y="175" width="140" height="50" rx="5" ry="5" fill="#8e44ad" /> <!-- Purple -->
                <text x="400" y="203" class="vaporizer-label">Vaporizer(s)</text>

                <!-- Common Gas Outlet -->
                <circle cx="550" cy="200" r="12" fill="#34495e" />
                <text x="550" y="230" class="cgo-label">CGO</text>
            </svg>
        </div>

        <div class="controls-container">
            <div class="flow-control">
                <label for="o2FlowInput">Oxygen Flow (L/min):</label>
                <input type="number" id="o2FlowInput" min="0" max="15" step="0.1" value="1.0">
            </div>
            <div class="flow-control">
                <label for="n2oFlowInput">Nitrous Oxide Flow (L/min):</label>
                <input type="number" id="n2oFlowInput" min="0" max="15" step="0.1" value="0.0">
            </div>
            <button id="oxygenFlushButton">Oxygen Flush (Hold)</button>
        </div>

        <div class="info-panel">
            <p>Total Flow: <span id="totalFlowDisplay">1.0</span> L/min</p>
            <p>O₂ Concentration: <span id="o2ConcentrationDisplay">100.0</span> %</p>
            <div id="errorMessage" class="error-message" style="display:none;"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const o2FlowInput = document.getElementById('o2FlowInput');
            const n2oFlowInput = document.getElementById('n2oFlowInput');
            const oxygenFlushButton = document.getElementById('oxygenFlushButton');

            const o2FlowMeterValueText = document.getElementById('o2FlowMeterValue');
            const n2oFlowMeterValueText = document.getElementById('n2oFlowMeterValue');

            const totalFlowDisplay = document.getElementById('totalFlowDisplay');
            const o2ConcentrationDisplay = document.getElementById('o2ConcentrationDisplay');
            const errorMessage = document.getElementById('errorMessage');

            const o2Path = document.getElementById('o2Path');
            const n2oPath = document.getElementById('n2oPath');
            const commonPath = document.getElementById('commonPath');

            let currentO2Flow = parseFloat(o2FlowInput.value);
            let currentN2OFlow = parseFloat(n2oFlowInput.value);
            
            let isFlushing = false;
            let storedO2Flow = currentO2Flow;
            let storedN2OFlow = currentN2OFlow;

            let animationFrameId;
            let o2Offset = 0;
            let n2oOffset = 0;
            let commonOffset = 0;
            const animationSpeedFactor = 2.5; 
            const dashArrayFlowing = "10, 7"; // Dash length, gap length
            const dashArrayNone = "none";

            function updateDisplays() {
                o2FlowInput.value = currentO2Flow.toFixed(1);
                n2oFlowInput.value = currentN2OFlow.toFixed(1);

                o2FlowMeterValueText.textContent = currentO2Flow.toFixed(1);
                n2oFlowMeterValueText.textContent = currentN2OFlow.toFixed(1);

                const totalFlow = currentO2Flow + currentN2OFlow;
                totalFlowDisplay.textContent = totalFlow.toFixed(1);

                let o2Concentration = 0;
                if (totalFlow > 0.001) { // Avoid division by zero with small tolerance
                    o2Concentration = (currentO2Flow / totalFlow) * 100;
                } else if (currentO2Flow > 0.001 && totalFlow < 0.001) { 
                    // If only O2 is flowing (N2O is 0, totalFlow is effectively O2 flow)
                    o2Concentration = 100;
                }
                o2ConcentrationDisplay.textContent = o2Concentration.toFixed(1);
            }

            function checkHypoxicMixture() {
                errorMessage.style.display = 'none';
                const totalFlow = currentO2Flow + currentN2OFlow;

                // Check only if N2O is used and there's any flow
                if (currentN2OFlow > 0.001 && totalFlow > 0.001) { 
                    const o2Fraction = currentO2Flow / totalFlow;
                    if (o2Fraction < 0.2499) { // Use 0.2499 for float precision
                        const minRequiredO2 = currentN2OFlow / 3.0;
                        
                        if (currentO2Flow < minRequiredO2) {
                            currentO2Flow = Math.min(parseFloat(o2FlowInput.max), minRequiredO2);
                            errorMessage.textContent = 'Hypoxic mixture! O₂ flow automatically increased to ensure min. 25% O₂.';
                            errorMessage.style.display = 'block';
                        }
                    }
                }
            }
            
            function handleFlowChange() {
                if (isFlushing) return;

                let newO2Flow = parseFloat(o2FlowInput.value) || 0;
                let newN2OFlow = parseFloat(n2oFlowInput.value) || 0;

                // Clamp values to min/max
                currentO2Flow = Math.max(0, Math.min(parseFloat(o2FlowInput.max), newO2Flow));
                currentN2OFlow = Math.max(0, Math.min(parseFloat(n2oFlowInput.max), newN2OFlow));
                
                checkHypoxicMixture(); // This might adjust currentO2Flow
                updateDisplays();
            }

            function animateGasFlow() {
                o2Offset -= currentO2Flow * animationSpeedFactor;
                n2oOffset -= currentN2OFlow * animationSpeedFactor;
                
                const totalFlowForAnimation = currentO2Flow + currentN2OFlow;
                commonOffset -= totalFlowForAnimation * animationSpeedFactor;

                o2Path.style.strokeDasharray = currentO2Flow > 0.001 ? dashArrayFlowing : dashArrayNone;
                o2Path.style.strokeDashoffset = o2Offset;

                n2oPath.style.strokeDasharray = currentN2OFlow > 0.001 ? dashArrayFlowing : dashArrayNone;
                n2oPath.style.strokeDashoffset = n2oOffset;
                
                commonPath.style.strokeDasharray = totalFlowForAnimation > 0.001 ? dashArrayFlowing : dashArrayNone;
                commonPath.style.strokeDashoffset = commonOffset;

                animationFrameId = requestAnimationFrame(animateGasFlow);
            }

            function startOxygenFlush() {
                if (isFlushing) return;
                isFlushing = true;

                storedO2Flow = parseFloat(o2FlowInput.value) || 0; // Store actual input value
                storedN2OFlow = parseFloat(n2oFlowInput.value) || 0;

                currentO2Flow = 50.0; 
                currentN2OFlow = 0.0;

                o2FlowInput.disabled = true;
                n2oFlowInput.disabled = true;
                oxygenFlushButton.classList.add('active');

                errorMessage.style.display = 'none'; 
                updateDisplays();
            }

            function stopOxygenFlush() {
                if (!isFlishing) return; // Guard against multiple calls
                isFlushing = false;

                currentO2Flow = storedO2Flow;
                currentN2OFlow = storedN2OFlow;
                
                o2FlowInput.disabled = false;
                n2oFlowInput.disabled = false;
                oxygenFlushButton.classList.remove('active');
                
                checkHypoxicMixture(); 
                updateDisplays();
            }

            o2FlowInput.addEventListener('input', handleFlowChange);
            n2oFlowInput.addEventListener('input', handleFlowChange);

            oxygenFlushButton.addEventListener('mousedown', startOxygenFlush);
            oxygenFlushButton.addEventListener('mouseup', stopOxygenFlush);
            oxygenFlushButton.addEventListener('mouseleave', () => { 
                if (isFlushing) stopOxygenFlush();
            });
            
            // Touch events for mobile
            oxygenFlushButton.addEventListener('touchstart', (e) => {
                e.preventDefault(); 
                startOxygenFlush();
            }, { passive: false }); // passive:false to allow preventDefault
            oxygenFlushButton.addEventListener('touchend', (e) => {
                e.preventDefault();
                stopOxygenFlush();
            });
            
            // Initial setup
            handleFlowChange(); 
            animateGasFlow(); 
        });
    </script>
</body>
</html>
