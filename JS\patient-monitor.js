// ===== PATIENT MONITOR SIMULATION ===== //

// Monitor State
const monitorState = {
    isRunning: false,
    parameters: {
        ecg: { enabled: true, rate: 75, rhythm: 'normal' },
        bp: { enabled: true, systolic: 120, diastolic: 80 },
        spo2: { enabled: true, value: 98, pulse: 75 },
        temp: { enabled: true, value: 36.8 },
        resp: { enabled: true, rate: 16 }
    },
    alarms: {
        enabled: true,
        limits: {
            hr: { low: 60, high: 100 },
            bp: { systolic: { low: 90, high: 140 }, diastolic: { low: 60, high: 90 } },
            spo2: { low: 95, high: 100 },
            temp: { low: 36.0, high: 38.0 },
            resp: { low: 12, high: 20 }
        }
    }
};

// Canvas contexts
let ecgCanvas, bpCanvas, spo2Canvas;
let ecgCtx, bpCtx, spo2Ctx;

// Animation variables
let animationId;
let timeOffset = 0;

// Initialize Patient Monitor
function initializePatientMonitor() {
    console.log('Initializing Patient Monitor...');
    
    // Get canvas elements
    ecgCanvas = document.getElementById('ecgCanvas');
    bpCanvas = document.getElementById('bpCanvas');
    spo2Canvas = document.getElementById('spo2Canvas');
    
    if (ecgCanvas) ecgCtx = ecgCanvas.getContext('2d');
    if (bpCanvas) bpCtx = bpCanvas.getContext('2d');
    if (spo2Canvas) spo2Ctx = spo2Canvas.getContext('2d');
    
    // Start monitor
    startMonitor();
    
    console.log('Patient Monitor initialized');
}

// Start Monitor
function startMonitor() {
    monitorState.isRunning = true;
    updateDisplay();
    animate();
}

// Stop Monitor
function stopMonitor() {
    monitorState.isRunning = false;
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
}

// Toggle Parameter
function toggleParameter(param) {
    monitorState.parameters[param].enabled = !monitorState.parameters[param].enabled;
    
    // Update button appearance
    const button = document.querySelector(`[onclick="toggleParameter('${param}')"]`);
    if (button) {
        button.classList.toggle('active', monitorState.parameters[param].enabled);
    }
    
    // Update display
    updateDisplay();
}

// Update Display Values
function updateDisplay() {
    // Update ECG heart rate
    const hrValue = document.querySelector('.ecg-section .parameter-value .value');
    if (hrValue) {
        hrValue.textContent = monitorState.parameters.ecg.rate;
    }
    
    // Update Blood Pressure
    const systolicValue = document.querySelector('.bp-values .systolic');
    const diastolicValue = document.querySelector('.bp-values .diastolic');
    if (systolicValue) systolicValue.textContent = monitorState.parameters.bp.systolic;
    if (diastolicValue) diastolicValue.textContent = monitorState.parameters.bp.diastolic;
    
    // Update SpO2
    const spo2Value = document.querySelector('.spo2-section .parameter-value .value');
    if (spo2Value) {
        spo2Value.textContent = monitorState.parameters.spo2.value;
    }
    
    // Update Temperature
    const tempValue = document.querySelector('.temp-section .parameter-value .value');
    if (tempValue) {
        tempValue.textContent = monitorState.parameters.temp.value.toFixed(1);
    }
    
    // Update Respiratory Rate
    const respValue = document.querySelector('.resp-section .parameter-value .value');
    if (respValue) {
        respValue.textContent = monitorState.parameters.resp.rate;
    }
}

// Animation Loop
function animate() {
    if (!monitorState.isRunning) return;
    
    timeOffset += 0.02;
    
    // Draw waveforms
    if (monitorState.parameters.ecg.enabled) drawECG();
    if (monitorState.parameters.bp.enabled) drawBloodPressure();
    if (monitorState.parameters.spo2.enabled) drawSpO2();
    
    // Add some realistic variation
    addPhysiologicalVariation();
    
    animationId = requestAnimationFrame(animate);
}

// Draw ECG Waveform
function drawECG() {
    if (!ecgCtx || !ecgCanvas) return;
    
    const width = ecgCanvas.width;
    const height = ecgCanvas.height;
    
    // Clear canvas
    ecgCtx.clearRect(0, 0, width, height);
    
    // Set up drawing
    ecgCtx.strokeStyle = '#00ff88';
    ecgCtx.lineWidth = 2;
    ecgCtx.beginPath();
    
    const heartRate = monitorState.parameters.ecg.rate;
    const beatInterval = 60 / heartRate; // seconds per beat
    const pixelsPerSecond = width / 10; // 10 seconds visible
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + timeOffset;
        const beatPhase = (time % beatInterval) / beatInterval;
        
        let y = height / 2; // baseline
        
        // Generate ECG waveform
        if (beatPhase < 0.1) {
            // P wave
            y += Math.sin(beatPhase * Math.PI * 10) * 8;
        } else if (beatPhase >= 0.15 && beatPhase < 0.25) {
            // QRS complex
            const qrsPhase = (beatPhase - 0.15) / 0.1;
            if (qrsPhase < 0.3) {
                y -= qrsPhase * 40; // Q wave
            } else if (qrsPhase < 0.7) {
                y += (qrsPhase - 0.3) * 100; // R wave
            } else {
                y -= (qrsPhase - 0.7) * 60; // S wave
            }
        } else if (beatPhase >= 0.4 && beatPhase < 0.7) {
            // T wave
            const tPhase = (beatPhase - 0.4) / 0.3;
            y += Math.sin(tPhase * Math.PI) * 15;
        }
        
        if (x === 0) {
            ecgCtx.moveTo(x, y);
        } else {
            ecgCtx.lineTo(x, y);
        }
    }
    
    ecgCtx.stroke();
    
    // Draw grid
    drawGrid(ecgCtx, width, height, '#00ff88', 0.1);
}

// Draw Blood Pressure Waveform
function drawBloodPressure() {
    if (!bpCtx || !bpCanvas) return;
    
    const width = bpCanvas.width;
    const height = bpCanvas.height;
    
    // Clear canvas
    bpCtx.clearRect(0, 0, width, height);
    
    // Set up drawing
    bpCtx.strokeStyle = '#3b82f6';
    bpCtx.lineWidth = 2;
    bpCtx.beginPath();
    
    const heartRate = monitorState.parameters.ecg.rate;
    const beatInterval = 60 / heartRate;
    const pixelsPerSecond = width / 8; // 8 seconds visible
    
    const systolic = monitorState.parameters.bp.systolic;
    const diastolic = monitorState.parameters.bp.diastolic;
    const pressureRange = systolic - diastolic;
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + timeOffset;
        const beatPhase = (time % beatInterval) / beatInterval;
        
        // Generate arterial pressure waveform
        let pressure;
        if (beatPhase < 0.3) {
            // Systolic rise
            pressure = diastolic + pressureRange * Math.sin(beatPhase * Math.PI / 0.3);
        } else if (beatPhase < 0.4) {
            // Dicrotic notch
            pressure = systolic - pressureRange * 0.1 * Math.sin((beatPhase - 0.3) * Math.PI / 0.1);
        } else {
            // Diastolic decay
            pressure = diastolic + pressureRange * 0.3 * Math.exp(-(beatPhase - 0.4) * 5);
        }
        
        // Convert pressure to y coordinate
        const y = height - (pressure - 60) / 80 * height;
        
        if (x === 0) {
            bpCtx.moveTo(x, y);
        } else {
            bpCtx.lineTo(x, y);
        }
    }
    
    bpCtx.stroke();
    
    // Draw grid
    drawGrid(bpCtx, width, height, '#3b82f6', 0.1);
}

// Draw SpO2 Pulse Wave
function drawSpO2() {
    if (!spo2Ctx || !spo2Canvas) return;
    
    const width = spo2Canvas.width;
    const height = spo2Canvas.height;
    
    // Clear canvas
    spo2Ctx.clearRect(0, 0, width, height);
    
    // Set up drawing
    spo2Ctx.strokeStyle = '#ef4444';
    spo2Ctx.lineWidth = 2;
    spo2Ctx.beginPath();
    
    const pulseRate = monitorState.parameters.spo2.pulse;
    const beatInterval = 60 / pulseRate;
    const pixelsPerSecond = width / 6; // 6 seconds visible
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + timeOffset;
        const beatPhase = (time % beatInterval) / beatInterval;
        
        // Generate pulse oximetry waveform
        let amplitude;
        if (beatPhase < 0.4) {
            amplitude = Math.sin(beatPhase * Math.PI / 0.4);
        } else {
            amplitude = Math.exp(-(beatPhase - 0.4) * 8) * 0.3;
        }
        
        const y = height / 2 - amplitude * height * 0.3;
        
        if (x === 0) {
            spo2Ctx.moveTo(x, y);
        } else {
            spo2Ctx.lineTo(x, y);
        }
    }
    
    spo2Ctx.stroke();
    
    // Draw grid
    drawGrid(spo2Ctx, width, height, '#ef4444', 0.1);
}

// Draw Grid
function drawGrid(ctx, width, height, color, opacity) {
    ctx.strokeStyle = color;
    ctx.globalAlpha = opacity;
    ctx.lineWidth = 0.5;
    
    // Vertical lines
    for (let x = 0; x < width; x += 20) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = 0; y < height; y += 20) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
    
    ctx.globalAlpha = 1;
}

// Add Physiological Variation
function addPhysiologicalVariation() {
    // Add small random variations to simulate real patient
    const variation = Math.sin(timeOffset * 0.1) * 2;
    
    // Heart rate variation
    const baseHR = 75;
    monitorState.parameters.ecg.rate = Math.round(baseHR + variation);
    monitorState.parameters.spo2.pulse = monitorState.parameters.ecg.rate;
    
    // Blood pressure variation
    const baseSystolic = 120;
    const baseDiastolic = 80;
    monitorState.parameters.bp.systolic = Math.round(baseSystolic + variation);
    monitorState.parameters.bp.diastolic = Math.round(baseDiastolic + variation * 0.5);
    
    // SpO2 variation (minimal)
    const baseSpo2 = 98;
    monitorState.parameters.spo2.value = Math.round(baseSpo2 + Math.sin(timeOffset * 0.05));
    
    // Temperature variation (very minimal)
    const baseTemp = 36.8;
    monitorState.parameters.temp.value = baseTemp + Math.sin(timeOffset * 0.02) * 0.1;
    
    // Respiratory rate variation
    const baseResp = 16;
    monitorState.parameters.resp.rate = Math.round(baseResp + Math.sin(timeOffset * 0.03) * 2);
    
    // Update display with new values
    updateDisplay();
}

// Start Vital Signs Animation
function startVitalSignsAnimation() {
    // Animate breathing
    const lungs = document.querySelectorAll('.lung');
    lungs.forEach((lung, index) => {
        lung.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Animate heartbeat icons
    const heartIcons = document.querySelectorAll('.animated-heartbeat');
    heartIcons.forEach(icon => {
        icon.style.animationPlayState = 'running';
    });
    
    // Animate pulse icons
    const pulseIcons = document.querySelectorAll('.animated-pulse');
    pulseIcons.forEach(icon => {
        icon.style.animationPlayState = 'running';
    });
    
    // Animate gauge icons
    const gaugeIcons = document.querySelectorAll('.animated-gauge');
    gaugeIcons.forEach(icon => {
        icon.style.animationPlayState = 'running';
    });
}

// Simulate Patient Scenarios
function simulateScenario(scenario) {
    switch (scenario) {
        case 'normal':
            monitorState.parameters.ecg.rate = 75;
            monitorState.parameters.bp.systolic = 120;
            monitorState.parameters.bp.diastolic = 80;
            monitorState.parameters.spo2.value = 98;
            monitorState.parameters.temp.value = 36.8;
            monitorState.parameters.resp.rate = 16;
            break;
            
        case 'tachycardia':
            monitorState.parameters.ecg.rate = 110;
            monitorState.parameters.spo2.pulse = 110;
            break;
            
        case 'bradycardia':
            monitorState.parameters.ecg.rate = 45;
            monitorState.parameters.spo2.pulse = 45;
            break;
            
        case 'hypertension':
            monitorState.parameters.bp.systolic = 160;
            monitorState.parameters.bp.diastolic = 95;
            break;
            
        case 'hypotension':
            monitorState.parameters.bp.systolic = 85;
            monitorState.parameters.bp.diastolic = 50;
            break;
            
        case 'hypoxia':
            monitorState.parameters.spo2.value = 88;
            break;
            
        case 'fever':
            monitorState.parameters.temp.value = 38.5;
            break;
            
        case 'hypothermia':
            monitorState.parameters.temp.value = 35.2;
            break;
    }
    
    updateDisplay();
}

// Export functions for global access
window.initializePatientMonitor = initializePatientMonitor;
window.toggleParameter = toggleParameter;
window.startVitalSignsAnimation = startVitalSignsAnimation;
window.simulateScenario = simulateScenario;
