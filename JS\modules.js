// ===== MODULES MANAGEMENT JAVASCRIPT =====

// Module Modal Management
let currentModal = null;

// ===== MODAL FUNCTIONS =====
function showModuleModal(moduleData) {
    if (appState.modalOpen) {
        closeModal();
    }
    
    const modal = createModuleModal(moduleData);
    document.body.appendChild(modal);
    
    // Show modal with animation
    setTimeout(() => {
        modal.style.display = 'flex';
        appState.modalOpen = true;
        currentModal = modal;
    }, 10);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function createModuleModal(moduleData) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
                <i class="fas fa-${getModuleIcon(moduleData.id)} module-icon"></i>
                <h3>${moduleData.title}</h3>
                <span class="module-level">${moduleData.level}</span>
            </div>
            <div class="modal-body">
                <p>${moduleData.description}</p>
                
                <div class="module-stats">
                    <div class="stat-box">
                        <span class="stat-value">${moduleData.duration}</span>
                        <span class="stat-label">Duration</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">${moduleData.simulations}</span>
                        <span class="stat-label">Simulations</span>
                    </div>
                    <div class="stat-box">
                        <span class="stat-value">${moduleData.assessments}</span>
                        <span class="stat-label">Questions</span>
                    </div>
                </div>
                
                <div class="learning-objectives">
                    <h4>Learning Objectives</h4>
                    <ul>
                        ${moduleData.objectives.map(obj => `<li>${obj}</li>`).join('')}
                    </ul>
                </div>
                
                <div class="assessment-preview">
                    <h4>Assessment & Certification</h4>
                    <p><strong>Prerequisites:</strong> ${moduleData.prerequisites}</p>
                    <p><strong>Certification:</strong> ${moduleData.certification}</p>
                    <div class="assessment-types">
                        <span class="assessment-type">Multiple Choice</span>
                        <span class="assessment-type">Interactive Scenarios</span>
                        <span class="assessment-type">Practical Simulations</span>
                    </div>
                </div>
                
                <div class="module-actions">
                    <button class="btn-primary" onclick="startModule('${moduleData.id}')">
                        <i class="fas fa-play"></i>
                        Start Module
                    </button>
                    <button class="btn-secondary" onclick="previewModule('${moduleData.id}')">
                        <i class="fas fa-eye"></i>
                        Preview Content
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    return modal;
}

function closeModal() {
    if (currentModal) {
        currentModal.style.display = 'none';
        document.body.removeChild(currentModal);
        currentModal = null;
        appState.modalOpen = false;
        document.body.style.overflow = 'auto';
    }
}

function getModuleIcon(moduleId) {
    const icons = {
        'patient-monitoring': 'heartbeat',
        'anesthesia-machine': 'lungs',
        'ventilator-systems': 'wind'
    };
    return icons[moduleId] || 'graduation-cap';
}

// ===== MODULE NAVIGATION =====
function startModule(moduleId) {
    closeModal();
    openModule(moduleId);
}

function previewModule(moduleId) {
    closeModal();
    showLoading('Loading Preview...');
    
    setTimeout(() => {
        hideLoading();
        showModulePreview(moduleId);
    }, 1000);
}

function showModulePreview(moduleId) {
    const previewData = getModulePreviewData(moduleId);
    if (previewData) {
        const previewModal = createPreviewModal(previewData);
        document.body.appendChild(previewModal);
        
        setTimeout(() => {
            previewModal.style.display = 'flex';
            appState.modalOpen = true;
            currentModal = previewModal;
        }, 10);
        
        document.body.style.overflow = 'hidden';
    }
}

function getModulePreviewData(moduleId) {
    const previews = {
        'patient-monitoring': {
            id: 'patient-monitoring',
            title: 'Patient Monitoring & Vital Signs - Preview',
            sections: [
                {
                    title: 'Introduction to Vital Signs',
                    content: 'Learn the fundamental vital signs: heart rate, blood pressure, respiratory rate, temperature, and oxygen saturation.',
                    duration: '30 min',
                    type: 'theory'
                },
                {
                    title: 'Patient Monitor Interface',
                    content: 'Interactive exploration of patient monitor displays, alarms, and control systems.',
                    duration: '45 min',
                    type: 'simulation'
                },
                {
                    title: 'Emergency Scenarios',
                    content: 'Practice responding to critical situations with realistic patient scenarios.',
                    duration: '60 min',
                    type: 'scenario'
                },
                {
                    title: 'Assessment & Certification',
                    content: 'Comprehensive assessment covering all module topics with practical evaluations.',
                    duration: '45 min',
                    type: 'assessment'
                }
            ]
        },
        'anesthesia-machine': {
            id: 'anesthesia-machine',
            title: 'Anesthesia Machine Systems - Preview',
            sections: [
                {
                    title: 'SPDD Model Overview',
                    content: 'Comprehensive understanding of Supply, Processing, Delivery, and Disposal systems.',
                    duration: '45 min',
                    type: 'theory'
                },
                {
                    title: 'Gas Flow Dynamics',
                    content: 'Interactive simulation of gas flow through flowmeters and breathing circuits.',
                    duration: '60 min',
                    type: 'simulation'
                },
                {
                    title: 'Component Identification',
                    content: 'Hands-on practice identifying and understanding machine components.',
                    duration: '50 min',
                    type: 'interactive'
                },
                {
                    title: 'Safety Protocols',
                    content: 'Critical safety procedures and emergency response protocols.',
                    duration: '40 min',
                    type: 'theory'
                },
                {
                    title: 'Practical Assessment',
                    content: 'Real-world scenarios testing machine operation and troubleshooting skills.',
                    duration: '75 min',
                    type: 'assessment'
                }
            ]
        },
        'ventilator-systems': {
            id: 'ventilator-systems',
            title: 'Ventilator Systems & Respiratory Care - Preview',
            sections: [
                {
                    title: 'Ventilation Principles',
                    content: 'Fundamental concepts of mechanical ventilation and respiratory physiology.',
                    duration: '40 min',
                    type: 'theory'
                },
                {
                    title: 'Ventilator Settings',
                    content: 'Interactive practice with ventilator parameters and mode selection.',
                    duration: '55 min',
                    type: 'simulation'
                },
                {
                    title: 'Breathing Mechanics',
                    content: 'Visual simulation of lung mechanics and ventilator-patient interaction.',
                    duration: '45 min',
                    type: 'simulation'
                },
                {
                    title: 'Clinical Scenarios',
                    content: 'Practice with different patient conditions and ventilation strategies.',
                    duration: '70 min',
                    type: 'scenario'
                },
                {
                    title: 'Competency Evaluation',
                    content: 'Comprehensive assessment of ventilator operation and patient care skills.',
                    duration: '60 min',
                    type: 'assessment'
                }
            ]
        }
    };
    
    return previews[moduleId] || null;
}

function createPreviewModal(previewData) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
                <h3>${previewData.title}</h3>
            </div>
            <div class="modal-body">
                <div class="preview-sections">
                    ${previewData.sections.map((section, index) => `
                        <div class="preview-section">
                            <div class="section-header">
                                <div class="section-number">${index + 1}</div>
                                <div class="section-info">
                                    <h4>${section.title}</h4>
                                    <div class="section-meta">
                                        <span class="section-duration">
                                            <i class="fas fa-clock"></i>
                                            ${section.duration}
                                        </span>
                                        <span class="section-type ${section.type}">
                                            <i class="fas fa-${getSectionIcon(section.type)}"></i>
                                            ${section.type.charAt(0).toUpperCase() + section.type.slice(1)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <p class="section-content">${section.content}</p>
                        </div>
                    `).join('')}
                </div>
                
                <div class="preview-actions">
                    <button class="btn-primary" onclick="startModule('${previewData.id}')">
                        <i class="fas fa-play"></i>
                        Start This Module
                    </button>
                    <button class="btn-secondary" onclick="closeModal()">
                        <i class="fas fa-arrow-left"></i>
                        Back to Modules
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    return modal;
}

function getSectionIcon(type) {
    const icons = {
        'theory': 'book',
        'simulation': 'desktop',
        'interactive': 'hand-pointer',
        'scenario': 'users',
        'assessment': 'clipboard-check'
    };
    return icons[type] || 'circle';
}

// ===== MODULE PROGRESS TRACKING =====
function trackModuleProgress(moduleId, sectionId, progress) {
    const currentProgress = currentUser.progress[moduleId] || 0;
    const newProgress = Math.max(currentProgress, progress);
    
    updateModuleProgress(moduleId, newProgress);
    
    // Show progress notification
    showProgressNotification(moduleId, newProgress);
}

function showProgressNotification(moduleId, progress) {
    const notification = document.createElement('div');
    notification.className = 'progress-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span>Progress Updated: ${progress}% Complete</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// ===== MODULE COMPLETION =====
function completeModule(moduleId) {
    updateModuleProgress(moduleId, 100);
    
    // Show completion modal
    showCompletionModal(moduleId);
    
    // Update user achievements
    if (!currentUser.completedModules) {
        currentUser.completedModules = [];
    }
    
    if (!currentUser.completedModules.includes(moduleId)) {
        currentUser.completedModules.push(moduleId);
        saveUserData();
    }
}

function showCompletionModal(moduleId) {
    const moduleData = getModuleData(moduleId);
    const completionModal = document.createElement('div');
    completionModal.className = 'modal-overlay completion-modal';
    completionModal.innerHTML = `
        <div class="modal-content">
            <div class="completion-header">
                <i class="fas fa-trophy completion-icon"></i>
                <h3>Congratulations!</h3>
                <p>You have successfully completed the ${moduleData.title} module</p>
            </div>
            <div class="completion-body">
                <div class="completion-stats">
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>Time Invested</span>
                        <strong>${moduleData.duration}</strong>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-tasks"></i>
                        <span>Simulations Completed</span>
                        <strong>${moduleData.simulations}</strong>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-award"></i>
                        <span>Certificate Earned</span>
                        <strong>Yes</strong>
                    </div>
                </div>
                
                <div class="next-steps">
                    <h4>What's Next?</h4>
                    <p>Continue your learning journey with our other modules or practice with advanced simulations.</p>
                </div>
                
                <div class="completion-actions">
                    <button class="btn-primary" onclick="downloadCertificate('${moduleId}')">
                        <i class="fas fa-download"></i>
                        Download Certificate
                    </button>
                    <button class="btn-secondary" onclick="closeModal()">
                        <i class="fas fa-home"></i>
                        Return to Dashboard
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(completionModal);
    
    setTimeout(() => {
        completionModal.style.display = 'flex';
        appState.modalOpen = true;
        currentModal = completionModal;
    }, 10);
    
    document.body.style.overflow = 'hidden';
}

function downloadCertificate(moduleId) {
    const moduleData = getModuleData(moduleId);
    
    // Create certificate data
    const certificateData = {
        studentName: currentUser.name,
        moduleName: moduleData.title,
        completionDate: new Date().toLocaleDateString(),
        certificateId: `VMS-${moduleId.toUpperCase()}-${Date.now()}`
    };
    
    // Generate and download certificate (placeholder)
    console.log('Generating certificate:', certificateData);
    
    // Show download notification
    showProgressNotification(moduleId, 'Certificate downloaded successfully!');
}

// ===== EXPORT FUNCTIONS =====
window.closeModal = closeModal;
window.startModule = startModule;
window.previewModule = previewModule;
window.trackModuleProgress = trackModuleProgress;
window.completeModule = completeModule;
window.downloadCertificate = downloadCertificate;
