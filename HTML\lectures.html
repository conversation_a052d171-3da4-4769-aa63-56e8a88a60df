<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Lectures - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/lectures.css">
    <link rel="stylesheet" href="../CSS/animations.css">
    <link rel="stylesheet" href="../CSS/responsive.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app-container">
        <!-- Header Section -->
        <header class="lecture-header">
            <div class="header-content">
                <div class="lecture-nav">
                    <a href="index.html" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                    <div class="lecture-title">
                        <i class="fas fa-chalkboard-teacher lecture-icon"></i>
                        <h1>Interactive Lectures</h1>
                    </div>
                </div>
                <div class="lecture-controls">
                    <button class="btn-control" id="fullscreenBtn" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                        Fullscreen
                    </button>
                    <button class="btn-control" id="settingsBtn" onclick="openSettings()">
                        <i class="fas fa-cog"></i>
                        Settings
                    </button>
                </div>
            </div>
        </header>

        <!-- Lecture Selection -->
        <section id="lecture-selection" class="lecture-selection">
            <div class="section-container">
                <div class="section-header">
                    <h2>Available Lectures</h2>
                    <p>Interactive presentations with animated visual aids and comprehensive content</p>
                </div>

                <div class="lectures-grid">
                    <!-- Patient Monitoring Lectures -->
                    <div class="lecture-category">
                        <h3><i class="fas fa-heartbeat"></i> Patient Monitoring & Vital Signs</h3>
                        <div class="lecture-cards">
                            <div class="lecture-card" data-lecture="vital-signs-intro">
                                <div class="lecture-thumbnail">
                                    <i class="fas fa-heart"></i>
                                    <div class="duration-badge">15 min</div>
                                </div>
                                <div class="lecture-info">
                                    <h4>Introduction to Vital Signs</h4>
                                    <p>Fundamental concepts of vital signs monitoring and normal physiological ranges</p>
                                    <div class="lecture-meta">
                                        <span class="slides-count"><i class="fas fa-images"></i> 12 slides</span>
                                        <span class="difficulty beginner">Beginner</span>
                                    </div>
                                </div>
                                <button class="btn-lecture" onclick="startLecture('vital-signs-intro')">
                                    <i class="fas fa-play"></i>
                                    Start Lecture
                                </button>
                            </div>

                            <div class="lecture-card" data-lecture="ecg-interpretation">
                                <div class="lecture-thumbnail">
                                    <i class="fas fa-chart-line"></i>
                                    <div class="duration-badge">25 min</div>
                                </div>
                                <div class="lecture-info">
                                    <h4>ECG Interpretation Basics</h4>
                                    <p>Understanding electrocardiogram patterns and cardiac rhythm analysis</p>
                                    <div class="lecture-meta">
                                        <span class="slides-count"><i class="fas fa-images"></i> 18 slides</span>
                                        <span class="difficulty intermediate">Intermediate</span>
                                    </div>
                                </div>
                                <button class="btn-lecture" onclick="startLecture('ecg-interpretation')">
                                    <i class="fas fa-play"></i>
                                    Start Lecture
                                </button>
                            </div>

                            <div class="lecture-card" data-lecture="blood-pressure-physiology">
                                <div class="lecture-thumbnail">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <div class="duration-badge">20 min</div>
                                </div>
                                <div class="lecture-info">
                                    <h4>Blood Pressure Physiology</h4>
                                    <p>Cardiovascular physiology and blood pressure regulation mechanisms</p>
                                    <div class="lecture-meta">
                                        <span class="slides-count"><i class="fas fa-images"></i> 15 slides</span>
                                        <span class="difficulty intermediate">Intermediate</span>
                                    </div>
                                </div>
                                <button class="btn-lecture" onclick="startLecture('blood-pressure-physiology')">
                                    <i class="fas fa-play"></i>
                                    Start Lecture
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Anesthesia Machine Lectures -->
                    <div class="lecture-category">
                        <h3><i class="fas fa-lungs"></i> Anesthesia Machine Systems</h3>
                        <div class="lecture-cards">
                            <div class="lecture-card" data-lecture="anesthesia-machine-overview">
                                <div class="lecture-thumbnail">
                                    <i class="fas fa-cogs"></i>
                                    <div class="duration-badge">30 min</div>
                                </div>
                                <div class="lecture-info">
                                    <h4>Anesthesia Machine Overview</h4>
                                    <p>Complete system overview and SPDD model explanation</p>
                                    <div class="lecture-meta">
                                        <span class="slides-count"><i class="fas fa-images"></i> 22 slides</span>
                                        <span class="difficulty beginner">Beginner</span>
                                    </div>
                                </div>
                                <button class="btn-lecture" onclick="startLecture('anesthesia-machine-overview')">
                                    <i class="fas fa-play"></i>
                                    Start Lecture
                                </button>
                            </div>

                            <div class="lecture-card" data-lecture="gas-flow-dynamics">
                                <div class="lecture-thumbnail">
                                    <i class="fas fa-wind"></i>
                                    <div class="duration-badge">35 min</div>
                                </div>
                                <div class="lecture-info">
                                    <h4>Gas Flow Dynamics</h4>
                                    <p>Understanding gas flow principles and flowmeter operations</p>
                                    <div class="lecture-meta">
                                        <span class="slides-count"><i class="fas fa-images"></i> 25 slides</span>
                                        <span class="difficulty advanced">Advanced</span>
                                    </div>
                                </div>
                                <button class="btn-lecture" onclick="startLecture('gas-flow-dynamics')">
                                    <i class="fas fa-play"></i>
                                    Start Lecture
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Ventilator Lectures -->
                    <div class="lecture-category">
                        <h3><i class="fas fa-wind"></i> Ventilator Systems</h3>
                        <div class="lecture-cards">
                            <div class="lecture-card" data-lecture="mechanical-ventilation">
                                <div class="lecture-thumbnail">
                                    <i class="fas fa-lungs"></i>
                                    <div class="duration-badge">28 min</div>
                                </div>
                                <div class="lecture-info">
                                    <h4>Mechanical Ventilation Principles</h4>
                                    <p>Fundamentals of mechanical ventilation and respiratory support</p>
                                    <div class="lecture-meta">
                                        <span class="slides-count"><i class="fas fa-images"></i> 20 slides</span>
                                        <span class="difficulty intermediate">Intermediate</span>
                                    </div>
                                </div>
                                <button class="btn-lecture" onclick="startLecture('mechanical-ventilation')">
                                    <i class="fas fa-play"></i>
                                    Start Lecture
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lecture Viewer -->
        <section id="lecture-viewer" class="lecture-viewer" style="display: none;">
            <div class="lecture-container">
                <!-- Lecture Header -->
                <div class="lecture-viewer-header">
                    <div class="lecture-info-bar">
                        <h3 id="currentLectureTitle">Lecture Title</h3>
                        <div class="lecture-progress">
                            <span id="slideCounter">1 / 12</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="lectureProgress"></div>
                            </div>
                        </div>
                    </div>
                    <div class="lecture-controls-bar">
                        <button class="btn-control" onclick="toggleNotes()">
                            <i class="fas fa-sticky-note"></i>
                            Notes
                        </button>
                        <button class="btn-control" onclick="toggleAutoplay()">
                            <i class="fas fa-play" id="autoplayIcon"></i>
                            Auto
                        </button>
                        <button class="btn-control" onclick="closeLecture()">
                            <i class="fas fa-times"></i>
                            Close
                        </button>
                    </div>
                </div>

                <!-- Slide Display Area -->
                <div class="slide-container">
                    <div class="slide-content" id="slideContent">
                        <!-- Slides will be dynamically loaded here -->
                    </div>
                    
                    <!-- Slide Navigation -->
                    <div class="slide-navigation">
                        <button class="nav-btn prev-btn" onclick="previousSlide()" id="prevBtn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="nav-btn next-btn" onclick="nextSlide()" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Slide Thumbnails -->
                <div class="slide-thumbnails" id="slideThumbnails">
                    <!-- Thumbnail navigation will be generated here -->
                </div>

                <!-- Notes Panel -->
                <div class="notes-panel" id="notesPanel" style="display: none;">
                    <div class="notes-header">
                        <h4><i class="fas fa-sticky-note"></i> Lecture Notes</h4>
                        <button class="btn-close" onclick="toggleNotes()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="notes-content">
                        <textarea id="notesTextarea" placeholder="Take notes during the lecture..."></textarea>
                        <div class="notes-actions">
                            <button class="btn-secondary" onclick="saveNotes()">
                                <i class="fas fa-save"></i>
                                Save Notes
                            </button>
                            <button class="btn-secondary" onclick="exportNotes()">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Modal -->
        <div id="settingsModal" class="modal-overlay" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-cog"></i> Lecture Settings</h3>
                    <button class="modal-close" onclick="closeSettings()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="autoplaySpeed">Autoplay Speed (seconds per slide)</label>
                        <input type="range" id="autoplaySpeed" min="3" max="15" value="8" step="1">
                        <span id="speedValue">8s</span>
                    </div>
                    <div class="setting-group">
                        <label for="animationSpeed">Animation Speed</label>
                        <select id="animationSpeed">
                            <option value="slow">Slow</option>
                            <option value="normal" selected>Normal</option>
                            <option value="fast">Fast</option>
                        </select>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="showAnimations" checked>
                            Enable Animations
                        </label>
                    </div>
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="showNotes" checked>
                            Show Notes Panel
                        </label>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-primary" onclick="saveSettings()">
                        <i class="fas fa-save"></i>
                        Save Settings
                    </button>
                    <button class="btn-secondary" onclick="resetSettings()">
                        <i class="fas fa-undo"></i>
                        Reset to Default
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../JS/main.js"></script>
    <script src="../JS/lectures.js"></script>
    <script src="../JS/slide-animations.js"></script>
    <script src="../JS/lecture-data.js"></script>
</body>
</html>
