<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine Module - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="../HTML/lectures.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Lectures
                </a>
                <div class="module-title">
                    <h1>Anesthesia Machine Module</h1>
                    <p>Complete SPDD System & Gas Flow Dynamics</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Module Overview -->
    <section class="module-overview">
        <div class="overview-content">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-lungs animated-pulse"></i>
                    </div>
                    <h3>SPDD System</h3>
                    <p>Source, Processing, Delivery, and Disposal components of anesthesia machines</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <span class="progress-text">75% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-wind animated-gauge"></i>
                    </div>
                    <h3>Gas Flow Dynamics</h3>
                    <p>Fluid mechanics principles governing anesthetic gas delivery systems</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%"></div>
                    </div>
                    <span class="progress-text">65% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-cogs animated-heartbeat"></i>
                    </div>
                    <h3>Mechanical Ventilation</h3>
                    <p>Advanced ventilator modes and respiratory support mechanisms</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 80%"></div>
                    </div>
                    <span class="progress-text">80% Complete</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Anesthesia Machine -->
    <section class="learning-tools">
        <div class="tools-container">
            <h2 class="section-title">Virtual Anesthesia Machine</h2>
            
            <!-- Machine Simulator -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Interactive Machine Simulation</h3>
                    <p>Explore the complete anesthesia delivery system with real-time controls</p>
                </div>
                
                <div class="anesthesia-machine">
                    <!-- Machine Display -->
                    <div class="machine-display">
                        <div class="display-screen">
                            <div class="screen-header">
                                <h4>Anesthesia Workstation</h4>
                                <div class="status-indicators">
                                    <span class="status-light active" id="powerStatus"></span>
                                    <span class="status-text">System Ready</span>
                                </div>
                            </div>
                            
                            <!-- Gas Flow Monitoring -->
                            <div class="gas-monitoring">
                                <div class="gas-panel">
                                    <h5>Fresh Gas Flow</h5>
                                    <div class="flowmeter-bank">
                                        <div class="flowmeter" data-gas="o2">
                                            <div class="flowmeter-tube">
                                                <div class="flow-indicator" style="bottom: 60%"></div>
                                            </div>
                                            <div class="flow-scale">
                                                <span>10</span>
                                                <span>5</span>
                                                <span>1</span>
                                                <span>0</span>
                                            </div>
                                            <div class="gas-label">O₂</div>
                                            <div class="flow-value">3.0 L/min</div>
                                        </div>
                                        
                                        <div class="flowmeter" data-gas="n2o">
                                            <div class="flowmeter-tube">
                                                <div class="flow-indicator" style="bottom: 40%"></div>
                                            </div>
                                            <div class="flow-scale">
                                                <span>10</span>
                                                <span>5</span>
                                                <span>1</span>
                                                <span>0</span>
                                            </div>
                                            <div class="gas-label">N₂O</div>
                                            <div class="flow-value">2.0 L/min</div>
                                        </div>
                                        
                                        <div class="flowmeter" data-gas="air">
                                            <div class="flowmeter-tube">
                                                <div class="flow-indicator" style="bottom: 30%"></div>
                                            </div>
                                            <div class="flow-scale">
                                                <span>10</span>
                                                <span>5</span>
                                                <span>1</span>
                                                <span>0</span>
                                            </div>
                                            <div class="gas-label">Air</div>
                                            <div class="flow-value">1.5 L/min</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Vaporizer Section -->
                                <div class="vaporizer-panel">
                                    <h5>Vaporizer</h5>
                                    <div class="vaporizer-controls">
                                        <div class="vaporizer-dial">
                                            <div class="dial-face">
                                                <div class="dial-pointer" style="transform: rotate(45deg)"></div>
                                                <div class="dial-markings">
                                                    <span class="marking" style="transform: rotate(0deg)">0</span>
                                                    <span class="marking" style="transform: rotate(30deg)">1</span>
                                                    <span class="marking" style="transform: rotate(60deg)">2</span>
                                                    <span class="marking" style="transform: rotate(90deg)">3</span>
                                                    <span class="marking" style="transform: rotate(120deg)">4</span>
                                                    <span class="marking" style="transform: rotate(150deg)">5</span>
                                                </div>
                                            </div>
                                            <div class="vaporizer-label">Sevoflurane</div>
                                            <div class="concentration-display">1.5%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Ventilator Parameters -->
                            <div class="ventilator-panel">
                                <h5>Ventilator Settings</h5>
                                <div class="vent-parameters">
                                    <div class="parameter-group">
                                        <div class="parameter">
                                            <label>Tidal Volume</label>
                                            <div class="value">500 mL</div>
                                        </div>
                                        <div class="parameter">
                                            <label>Respiratory Rate</label>
                                            <div class="value">12 /min</div>
                                        </div>
                                        <div class="parameter">
                                            <label>PEEP</label>
                                            <div class="value">5 cmH₂O</div>
                                        </div>
                                        <div class="parameter">
                                            <label>FiO₂</label>
                                            <div class="value">50%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pressure Waveforms -->
                            <div class="waveform-panel">
                                <h5>Respiratory Waveforms</h5>
                                <div class="waveform-display">
                                    <canvas id="pressureWaveform" width="400" height="100"></canvas>
                                    <div class="waveform-label">Airway Pressure (cmH₂O)</div>
                                </div>
                                <div class="waveform-display">
                                    <canvas id="flowWaveform" width="400" height="80"></canvas>
                                    <div class="waveform-label">Flow (L/min)</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Machine Controls -->
                        <div class="machine-controls">
                            <div class="control-section">
                                <h5>Gas Controls</h5>
                                <div class="gas-controls">
                                    <button class="control-btn" onclick="adjustFlow('o2', 'up')">
                                        <i class="fas fa-plus"></i>
                                        O₂ +
                                    </button>
                                    <button class="control-btn" onclick="adjustFlow('o2', 'down')">
                                        <i class="fas fa-minus"></i>
                                        O₂ -
                                    </button>
                                    <button class="control-btn" onclick="adjustFlow('n2o', 'up')">
                                        <i class="fas fa-plus"></i>
                                        N₂O +
                                    </button>
                                    <button class="control-btn" onclick="adjustFlow('n2o', 'down')">
                                        <i class="fas fa-minus"></i>
                                        N₂O -
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Ventilator</h5>
                                <div class="vent-controls">
                                    <button class="control-btn" onclick="adjustVentilator('tv', 'up')">
                                        <i class="fas fa-arrow-up"></i>
                                        TV +
                                    </button>
                                    <button class="control-btn" onclick="adjustVentilator('tv', 'down')">
                                        <i class="fas fa-arrow-down"></i>
                                        TV -
                                    </button>
                                    <button class="control-btn" onclick="adjustVentilator('rr', 'up')">
                                        <i class="fas fa-arrow-up"></i>
                                        RR +
                                    </button>
                                    <button class="control-btn" onclick="adjustVentilator('rr', 'down')">
                                        <i class="fas fa-arrow-down"></i>
                                        RR -
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Emergency</h5>
                                <div class="emergency-controls">
                                    <button class="emergency-btn" onclick="emergencyO2()">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Emergency O₂
                                    </button>
                                    <button class="emergency-btn" onclick="gasShutoff()">
                                        <i class="fas fa-stop"></i>
                                        Gas Shutoff
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SPDD System Diagram -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>SPDD System Architecture</h3>
                    <p>Source, Processing, Delivery, and Disposal components</p>
                </div>
                
                <div class="diagram-container">
                    <div class="diagram-tabs">
                        <button class="tab-btn active" onclick="showSPDDDiagram('source')">Source</button>
                        <button class="tab-btn" onclick="showSPDDDiagram('processing')">Processing</button>
                        <button class="tab-btn" onclick="showSPDDDiagram('delivery')">Delivery</button>
                        <button class="tab-btn" onclick="showSPDDDiagram('disposal')">Disposal</button>
                    </div>
                    
                    <!-- Source Diagram -->
                    <div id="sourceDiagram" class="diagram-panel active">
                        <svg class="interactive-diagram" viewBox="0 0 800 500">
                            <!-- Gas Sources -->
                            <g class="diagram-component gas-source" onclick="showSPDDInfo('o2-source')">
                                <rect x="50" y="100" width="120" height="80" rx="10" class="component-box"/>
                                <text x="110" y="130" class="component-label">Oxygen Source</text>
                                <text x="110" y="145" class="component-sublabel">Pipeline/Cylinder</text>
                                <text x="110" y="160" class="component-sublabel">50 PSI</text>
                            </g>
                            
                            <g class="diagram-component gas-source" onclick="showSPDDInfo('n2o-source')">
                                <rect x="50" y="200" width="120" height="80" rx="10" class="component-box"/>
                                <text x="110" y="230" class="component-label">N₂O Source</text>
                                <text x="110" y="245" class="component-sublabel">Pipeline/Cylinder</text>
                                <text x="110" y="260" class="component-sublabel">50 PSI</text>
                            </g>
                            
                            <g class="diagram-component gas-source" onclick="showSPDDInfo('air-source')">
                                <rect x="50" y="300" width="120" height="80" rx="10" class="component-box"/>
                                <text x="110" y="330" class="component-label">Air Source</text>
                                <text x="110" y="345" class="component-sublabel">Pipeline/Compressor</text>
                                <text x="110" y="360" class="component-sublabel">50 PSI</text>
                            </g>
                            
                            <!-- Pressure Regulators -->
                            <g class="diagram-component regulator" onclick="showSPDDInfo('regulators')">
                                <rect x="250" y="180" width="100" height="120" rx="8" class="component-box"/>
                                <text x="300" y="210" class="component-label">Pressure</text>
                                <text x="300" y="225" class="component-label">Regulators</text>
                                <text x="300" y="250" class="component-sublabel">50 PSI → 15 PSI</text>
                                <text x="300" y="265" class="component-sublabel">Safety Relief</text>
                                <text x="300" y="280" class="component-sublabel">Pressure Gauges</text>
                            </g>
                            
                            <!-- Safety Systems -->
                            <g class="diagram-component safety" onclick="showSPDDInfo('safety')">
                                <rect x="450" y="150" width="120" height="180" rx="10" class="component-box"/>
                                <text x="510" y="180" class="component-label">Safety Systems</text>
                                <text x="510" y="205" class="component-sublabel">• Oxygen Analyzer</text>
                                <text x="510" y="220" class="component-sublabel">• Hypoxic Guard</text>
                                <text x="510" y="235" class="component-sublabel">• Pressure Alarms</text>
                                <text x="510" y="250" class="component-sublabel">• Flow Alarms</text>
                                <text x="510" y="265" class="component-sublabel">• Emergency O₂</text>
                                <text x="510" y="280" class="component-sublabel">• Gas Shutoff</text>
                                <text x="510" y="295" class="component-sublabel">• Check Valves</text>
                                <text x="510" y="310" class="component-sublabel">• Relief Valves</text>
                            </g>
                            
                            <!-- Connections -->
                            <line x1="170" y1="140" x2="250" y2="200" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="170" y1="240" x2="250" y2="240" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="170" y1="340" x2="250" y2="280" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="350" y1="240" x2="450" y2="240" class="connection-line" marker-end="url(#arrowhead)"/>
                            
                            <!-- Arrow marker -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" class="arrow-fill"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>
                    
                    <!-- Component Information Panel -->
                    <div class="component-info" id="spddComponentInfo">
                        <h4 id="spddComponentTitle">Gas Source Systems</h4>
                        <p id="spddComponentDescription">Medical gas sources provide the foundation for anesthesia delivery. Click on any component to learn more about its function and specifications.</p>
                        <div id="spddComponentSpecs"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comprehensive Notes -->
    <section class="lecture-notes">
        <div class="notes-container">
            <h2 class="section-title">Anesthesia Machine Fundamentals</h2>
            
            <div class="notes-grid">
                <!-- SPDD Notes -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-lungs"></i>
                        </div>
                        <h3>SPDD System Components</h3>
                    </div>
                    <div class="note-content">
                        <h4>Source (S)</h4>
                        <ul>
                            <li><strong>Oxygen:</strong> Pipeline (50 PSI) or E-cylinders (2000 PSI)</li>
                            <li><strong>Nitrous Oxide:</strong> Pipeline (50 PSI) or E-cylinders (745 PSI)</li>
                            <li><strong>Air:</strong> Pipeline (50 PSI) or compressor</li>
                            <li><strong>Safety:</strong> Pressure alarms, emergency oxygen, shutoff valves</li>
                        </ul>
                        
                        <h4>Processing (P)</h4>
                        <ul>
                            <li><strong>Pressure Reduction:</strong> 50 PSI → 15 PSI regulators</li>
                            <li><strong>Flow Control:</strong> Needle valves and flowmeters</li>
                            <li><strong>Mixing:</strong> Proportional gas mixing systems</li>
                            <li><strong>Vaporization:</strong> Precision vaporizers for volatile agents</li>
                        </ul>
                        
                        <h4>Delivery (D)</h4>
                        <ul>
                            <li><strong>Fresh Gas:</strong> Common gas outlet to breathing circuit</li>
                            <li><strong>Ventilator:</strong> Mechanical ventilation with multiple modes</li>
                            <li><strong>Monitoring:</strong> Gas analysis, pressure, flow, volume</li>
                            <li><strong>Alarms:</strong> Comprehensive alarm systems</li>
                        </ul>
                        
                        <h4>Disposal (D)</h4>
                        <ul>
                            <li><strong>Scavenging:</strong> Waste gas removal system</li>
                            <li><strong>APL Valve:</strong> Adjustable pressure limiting</li>
                            <li><strong>Evacuation:</strong> Active or passive scavenging</li>
                            <li><strong>Environmental:</strong> Pollution prevention</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Gas Flow Notes -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <h3>Gas Flow Dynamics</h3>
                    </div>
                    <div class="note-content">
                        <h4>Flow Principles</h4>
                        <ul>
                            <li><strong>Laminar Flow:</strong> Smooth, predictable flow patterns</li>
                            <li><strong>Turbulent Flow:</strong> Chaotic flow with increased resistance</li>
                            <li><strong>Reynolds Number:</strong> Predicts flow characteristics</li>
                            <li><strong>Poiseuille's Law:</strong> Flow through tubes</li>
                        </ul>
                        
                        <h4>Flowmeter Operation</h4>
                        <ul>
                            <li><strong>Variable Orifice:</strong> Tapered tube design</li>
                            <li><strong>Constant Pressure:</strong> Pressure drop across indicator</li>
                            <li><strong>Gas Specific:</strong> Calibrated for specific gases</li>
                            <li><strong>Temperature/Pressure:</strong> STPD corrections</li>
                        </ul>
                        
                        <h4>Safety Features</h4>
                        <ul>
                            <li><strong>Sequence:</strong> O₂ downstream of other gases</li>
                            <li><strong>Proportioning:</strong> Minimum O₂ concentration</li>
                            <li><strong>Hypoxic Guard:</strong> Prevents hypoxic mixtures</li>
                            <li><strong>Link-25:</strong> Mechanical O₂/N₂O proportioning</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/anesthesia-machine.js"></script>
    <script src="../JS/spdd-diagrams.js"></script>
    <script>
        // Initialize module
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnesthesiaMachine();
            initializeSPDDDiagrams();
            startMachineAnimations();
        });
    </script>
</body>
</html>
