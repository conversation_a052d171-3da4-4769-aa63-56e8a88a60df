body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    color: #333;
    line-height: 1.6;
}

header {
    background-color: #007bff;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
}

header h1 {
    margin: 0;
}

nav {
    background-color: #333;
    color: #fff;
    padding: 0.5rem;
    text-align: center;
}

nav a {
    color: #fff;
    margin: 0 1rem;
    text-decoration: none;
}

nav a:hover {
    text-decoration: underline;
}

main {
    padding: 1rem;
    max-width: 960px;
    margin: 20px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

section {
    margin-bottom: 2rem;
}

footer {
    text-align: center;
    padding: 1rem 0;
    background-color: #333;
    color: #fff;
    position: relative;
    bottom: 0;
    width: 100%;
}

/* يمكنك إضافة المزيد من الأنماط المخصصة هنا */