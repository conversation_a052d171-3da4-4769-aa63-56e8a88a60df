// ===== INTERACTIVE DIAGRAMS SYSTEM ===== //

// Diagram State
const diagramState = {
    currentCategory: 'patient-monitoring',
    currentType: 'block',
    zoomLevel: 1,
    isAnimating: false,
    selectedComponent: null,
    panOffset: { x: 0, y: 0 },
    isDragging: false
};

// Component Information Database
const componentInfo = {
    'patient-interface': {
        title: 'Patient Interface',
        description: 'The patient interface consists of electrodes, sensors, and transducers that acquire physiological signals directly from the patient. This includes ECG electrodes, SpO₂ sensors, temperature probes, and blood pressure cuffs.',
        specifications: {
            'ECG Electrodes': 'Ag/AgCl, disposable',
            'Input Impedance': '>10 MΩ',
            'Frequency Response': '0.05-150 Hz',
            'Safety Standard': 'IEC 60601-1',
            'Isolation': '4000V AC'
        },
        connections: [
            'Signal Acquisition Module',
            'Patient Safety Isolation',
            'Defibrillation Protection'
        ]
    },
    'signal-acquisition': {
        title: 'Signal Acquisition',
        description: 'The signal acquisition module amplifies, filters, and conditions the weak physiological signals from the patient interface. It includes instrumentation amplifiers, anti-aliasing filters, and gain control circuits.',
        specifications: {
            'Gain Range': '100-10,000',
            'CMRR': '>100 dB',
            'Input Noise': '<2 μV RMS',
            'Bandwidth': '0.05-1000 Hz',
            'THD': '<0.1%'
        },
        connections: [
            'Patient Interface',
            'ADC Module',
            'Calibration Circuit'
        ]
    },
    'adc': {
        title: 'Analog-to-Digital Converter',
        description: 'High-resolution ADC converts the conditioned analog signals to digital format for processing. Features simultaneous sampling across multiple channels with precise timing.',
        specifications: {
            'Resolution': '16-bit',
            'Sampling Rate': '1000 Hz',
            'Channels': '12 simultaneous',
            'SNR': '>90 dB',
            'Linearity': '±0.5 LSB'
        },
        connections: [
            'Signal Acquisition',
            'Digital Processing Unit',
            'Timing Reference'
        ]
    },
    'digital-processing': {
        title: 'Digital Processing Unit',
        description: 'Advanced DSP algorithms process the digitized signals to extract vital signs, detect arrhythmias, and perform real-time analysis. Includes QRS detection, heart rate calculation, and artifact rejection.',
        specifications: {
            'Processor': 'ARM Cortex-M7',
            'Clock Speed': '400 MHz',
            'RAM': '1 MB',
            'Flash': '2 MB',
            'Processing Delay': '<100 ms'
        },
        connections: [
            'ADC Module',
            'Display System',
            'Alarm System',
            'Data Storage'
        ]
    },
    'display-system': {
        title: 'Display System',
        description: 'High-resolution color display presents waveforms, numerical values, and alarms in real-time. Features touch interface and customizable layouts for different clinical scenarios.',
        specifications: {
            'Screen Size': '15" TFT LCD',
            'Resolution': '1920x1080',
            'Refresh Rate': '60 Hz',
            'Touch': 'Capacitive',
            'Brightness': '400 cd/m²'
        },
        connections: [
            'Digital Processing Unit',
            'User Interface Controller',
            'Network Interface'
        ]
    },
    'ecg-processing': {
        title: 'ECG Processing',
        description: 'Specialized ECG processing algorithms for lead selection, QRS detection, heart rate calculation, and arrhythmia analysis. Implements advanced filtering and baseline correction.',
        specifications: {
            'Lead Configuration': '3, 5, or 12-lead',
            'QRS Detection': '>99% accuracy',
            'Heart Rate Range': '15-300 bpm',
            'Arrhythmia Detection': '15+ types',
            'ST Analysis': '±2 mV range'
        },
        connections: [
            'Signal Acquisition',
            'Alarm System',
            'Trend Analysis'
        ]
    },
    'spo2-processing': {
        title: 'SpO₂ Processing',
        description: 'Pulse oximetry processing using red and infrared light absorption to calculate oxygen saturation and pulse rate. Features motion artifact reduction and low perfusion algorithms.',
        specifications: {
            'SpO₂ Range': '70-100%',
            'Accuracy': '±2% (70-100%)',
            'Pulse Rate': '30-250 bpm',
            'Update Rate': '1 Hz',
            'Motion Tolerance': 'Advanced algorithm'
        },
        connections: [
            'Pulse Oximeter Sensor',
            'Perfusion Index Calculator',
            'Alarm System'
        ]
    },
    'nibp-processing': {
        title: 'NIBP Processing',
        description: 'Non-invasive blood pressure measurement using oscillometric method. Automated cuff inflation/deflation with artifact detection and measurement validation.',
        specifications: {
            'Method': 'Oscillometric',
            'Pressure Range': '0-300 mmHg',
            'Accuracy': '±3 mmHg',
            'Measurement Time': '15-45 seconds',
            'Cuff Sizes': 'Neonatal to Thigh'
        },
        connections: [
            'Pneumatic System',
            'Pressure Transducer',
            'Cuff Control Valve'
        ]
    },
    'alarm-system': {
        title: 'Alarm System',
        description: 'Intelligent alarm management with priority classification, audio/visual alerts, and alarm fatigue reduction. Complies with IEC 60601-1-8 alarm standard.',
        specifications: {
            'Priority Levels': 'High, Medium, Low',
            'Audio Range': '45-85 dB',
            'Visual Indicators': 'LED + Display',
            'Alarm Delay': 'Configurable',
            'Silence Duration': '2-15 minutes'
        },
        connections: [
            'All Processing Modules',
            'Audio System',
            'Visual Indicators',
            'Network Alerts'
        ]
    },
    'data-storage': {
        title: 'Data Storage',
        description: 'Secure data storage system for patient trends, events, and waveforms. Features automatic backup, data compression, and HIPAA-compliant encryption.',
        specifications: {
            'Storage Capacity': '500 GB SSD',
            'Trend Duration': '72 hours',
            'Waveform Storage': '24 hours',
            'Backup': 'Automatic',
            'Encryption': 'AES-256'
        },
        connections: [
            'Digital Processing Unit',
            'Network Interface',
            'USB Export',
            'Print System'
        ]
    },

    // Anesthesia Machine Components
    'gas-cylinders': {
        title: 'Gas Cylinders',
        description: 'High-pressure gas storage cylinders containing medical gases (O₂, N₂O, Air). Each cylinder has specific color coding and pressure ratings for safety identification.',
        specifications: {
            'Oxygen Pressure': '2200 PSI',
            'N₂O Pressure': '745 PSI',
            'Air Pressure': '2200 PSI',
            'Safety Features': 'PISS, CGA fittings',
            'Capacity': 'E-size cylinders'
        },
        connections: [
            'Pressure Regulators',
            'Cylinder Yokes',
            'Check Valves'
        ]
    },

    'pipeline-supply': {
        title: 'Pipeline Supply',
        description: 'Central gas supply system delivering medical gases through hospital pipelines. Provides primary gas source with automatic cylinder backup.',
        specifications: {
            'Supply Pressure': '50 PSI',
            'Gas Types': 'O₂, N₂O, Air',
            'Connections': 'DISS fittings',
            'Backup System': 'Automatic switchover',
            'Alarm System': 'Low pressure alerts'
        },
        connections: [
            'Pressure Regulators',
            'Gas Supply Monitors',
            'Backup Systems'
        ]
    },

    'pressure-regulators': {
        title: 'Pressure Regulators',
        description: 'Reduce high-pressure gas supply to safe working pressure (50 PSI). Include safety relief valves and pressure gauges for monitoring.',
        specifications: {
            'Input Pressure': '50-2200 PSI',
            'Output Pressure': '50 PSI ±5',
            'Flow Capacity': '120 L/min',
            'Relief Valve': '75 PSI setting',
            'Accuracy': '±2 PSI'
        },
        connections: [
            'Gas Supply Sources',
            'Flowmeter Assembly',
            'Safety Relief System'
        ]
    },

    'flowmeters': {
        title: 'Variable Orifice Flowmeters',
        description: 'Tapered tube flowmeters with floating indicators for precise gas flow measurement. Each gas has dedicated, calibrated flowmeter.',
        specifications: {
            'Flow Range': '0.1-15 L/min',
            'Accuracy': '±5% full scale',
            'Gas Specific': 'Individual calibration',
            'Tube Material': 'Borosilicate glass',
            'Float Design': 'Non-magnetic'
        },
        connections: [
            'Pressure Regulators',
            'Gas Mixing Chamber',
            'Hypoxic Guard System'
        ]
    },

    'vaporizers': {
        title: 'Anesthetic Vaporizers',
        description: 'Precision vaporizers for volatile anesthetic agents. Temperature and pressure compensated for accurate concentration delivery.',
        specifications: {
            'Agents': 'Sevoflurane, Isoflurane',
            'Concentration Range': '0-8%',
            'Accuracy': '±10% of setting',
            'Temperature Compensation': 'Automatic',
            'Filling System': 'Agent-specific'
        },
        connections: [
            'Fresh Gas Flow',
            'Gas Mixing Chamber',
            'Agent Monitors'
        ]
    },

    'gas-mixing': {
        title: 'Gas Mixing Chamber',
        description: 'Combines fresh gas flows and anesthetic vapors into homogeneous mixture. Ensures proper gas composition before delivery.',
        specifications: {
            'Mixing Volume': '500 mL',
            'Flow Range': '0.2-15 L/min',
            'Response Time': '<30 seconds',
            'Material': 'Stainless steel',
            'Connections': 'Standard 22mm'
        },
        connections: [
            'Flowmeters',
            'Vaporizers',
            'Breathing Circuit'
        ]
    },

    'breathing-circuit': {
        title: 'Breathing Circuit',
        description: 'Connects anesthesia machine to patient airway. Includes inspiratory and expiratory limbs with CO₂ absorption system.',
        specifications: {
            'Circuit Type': 'Circle system',
            'Tubing Diameter': '22mm ID',
            'CO₂ Absorber': 'Soda lime',
            'Compliance': '3 mL/cmH₂O',
            'Dead Space': '<50 mL'
        },
        connections: [
            'Gas Mixing Chamber',
            'Patient Interface',
            'Ventilator Bellows'
        ]
    },

    'scavenging-system': {
        title: 'Scavenging System',
        description: 'Removes waste anesthetic gases from operating room to prevent occupational exposure. Active or passive evacuation systems.',
        specifications: {
            'Evacuation Rate': '0.5-10 L/min',
            'Vacuum Level': '-0.5 to -10 cmH₂O',
            'Interface': 'Open or closed',
            'Reservoir': '1-3 L capacity',
            'Alarm System': 'High/low pressure'
        },
        connections: [
            'Breathing Circuit',
            'Vacuum System',
            'Waste Gas Monitors'
        ]
    }
};

// Initialize Interactive Diagrams
function initializeInteractiveDiagrams() {
    console.log('Initializing Interactive Diagrams...');
    
    // Set up event listeners
    setupEventListeners();
    
    // Initialize diagram interactions
    initializeDiagramInteractions();
    
    // Load default diagram
    showCategory('patient-monitoring');
    showDiagramType('block');
    
    console.log('Interactive Diagrams initialized');
}

// Setup Event Listeners
function setupEventListeners() {
    // Component click handlers
    document.addEventListener('click', handleComponentClick);
    
    // Diagram pan and zoom
    const diagrams = document.querySelectorAll('.interactive-diagram');
    diagrams.forEach(diagram => {
        diagram.addEventListener('mousedown', startPan);
        diagram.addEventListener('mousemove', handlePan);
        diagram.addEventListener('mouseup', endPan);
        diagram.addEventListener('wheel', handleZoom);
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboard);
}

// Initialize Diagram Interactions
function initializeDiagramInteractions() {
    // Add hover effects to components
    const components = document.querySelectorAll('.block-component');
    components.forEach(component => {
        component.addEventListener('mouseenter', highlightComponent);
        component.addEventListener('mouseleave', unhighlightComponent);
    });
}

// Show Category
function showCategory(category) {
    // Update state
    diagramState.currentCategory = category;
    
    // Update tab states
    const tabs = document.querySelectorAll('.category-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.category === category) {
            tab.classList.add('active');
        }
    });
    
    // Show category panel
    const panels = document.querySelectorAll('.diagram-category-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
    });
    
    const activePanel = document.getElementById(`${category}-diagrams`);
    if (activePanel) {
        activePanel.classList.add('active');
    }
    
    // Load category-specific diagrams
    loadCategoryDiagrams(category);
}

// Show Diagram Type
function showDiagramType(type) {
    // Update state
    diagramState.currentType = type;
    
    // Update button states
    const buttons = document.querySelectorAll('.type-btn');
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.type === type) {
            btn.classList.add('active');
        }
    });
    
    // Show type panel
    const panels = document.querySelectorAll('.diagram-type-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
        if (panel.dataset.type === type) {
            panel.classList.add('active');
        }
    });
    
    // Load type-specific content
    loadDiagramType(type);
}

// Load Category Diagrams
function loadCategoryDiagrams(category) {
    console.log(`Loading diagrams for category: ${category}`);
    
    switch (category) {
        case 'patient-monitoring':
            loadPatientMonitoringDiagrams();
            break;
        case 'anesthesia-machine':
            loadAnesthesiaMachineDiagrams();
            break;
        case 'ecg-systems':
            loadECGSystemDiagrams();
            break;
        case 'ventilation':
            loadVentilationDiagrams();
            break;
        case 'hemodynamics':
            loadHemodynamicsDiagrams();
            break;
        case 'gas-flow':
            loadGasFlowDiagrams();
            break;
    }
}

// Load Diagram Type
function loadDiagramType(type) {
    console.log(`Loading diagram type: ${type}`);
    
    switch (type) {
        case 'block':
            loadBlockDiagrams();
            break;
        case 'schematic':
            loadSchematicDiagrams();
            break;
        case 'signal-flow':
            loadSignalFlowDiagrams();
            break;
    }
}

// Load Patient Monitoring Diagrams
function loadPatientMonitoringDiagrams() {
    // Block diagram is already loaded in HTML
    // Load schematic and signal flow diagrams
    loadPatientMonitoringSchematic();
    loadPatientMonitoringSignalFlow();
}

// Load Patient Monitoring Schematic
function loadPatientMonitoringSchematic() {
    const svg = document.getElementById('patientMonitoringSchematic');
    if (!svg) return;
    
    // Clear existing content
    svg.innerHTML = '';
    
    // Add schematic content
    svg.innerHTML = `
        <defs>
            <marker id="arrowhead-schematic" markerWidth="10" markerHeight="7" 
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
            </marker>
        </defs>
        
        <!-- ECG Input Stage -->
        <g class="circuit-component" data-component="ecg-input">
            <rect x="50" y="100" width="120" height="80" fill="none" stroke="#10b981" stroke-width="2" rx="5"/>
            <text x="110" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ECG INPUT</text>
            <text x="110" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">STAGE</text>
            
            <!-- Input terminals -->
            <circle cx="30" cy="120" r="3" fill="#3b82f6"/>
            <circle cx="30" cy="140" r="3" fill="#ef4444"/>
            <circle cx="30" cy="160" r="3" fill="#22c55e"/>
            <text x="20" y="125" text-anchor="end" fill="#94a3b8" font-size="10">RA</text>
            <text x="20" y="145" text-anchor="end" fill="#94a3b8" font-size="10">LA</text>
            <text x="20" y="165" text-anchor="end" fill="#94a3b8" font-size="10">LL</text>
            
            <!-- Protection diodes -->
            <path d="M 35 120 L 45 120 M 40 115 L 40 125 M 35 125 L 45 115" stroke="#f59e0b" stroke-width="1" fill="none"/>
            <path d="M 35 140 L 45 140 M 40 135 L 40 145 M 35 145 L 45 135" stroke="#f59e0b" stroke-width="1" fill="none"/>
            <path d="M 35 160 L 45 160 M 40 155 L 40 165 M 35 165 L 45 155" stroke="#f59e0b" stroke-width="1" fill="none"/>
        </g>
        
        <!-- Instrumentation Amplifier -->
        <g class="circuit-component" data-component="inst-amp">
            <path d="M 200 100 L 280 140 L 200 180 Z" fill="none" stroke="#8b5cf6" stroke-width="2"/>
            <text x="240" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">INA</text>
            
            <!-- Input connections -->
            <line x1="170" y1="120" x2="200" y2="120" stroke="#10b981" stroke-width="2"/>
            <line x1="170" y1="160" x2="200" y2="160" stroke="#10b981" stroke-width="2"/>
            <text x="190" y="115" text-anchor="end" fill="#94a3b8" font-size="8">+</text>
            <text x="190" y="165" text-anchor="end" fill="#94a3b8" font-size="8">-</text>
            
            <!-- Gain setting -->
            <rect x="220" y="185" width="40" height="15" fill="none" stroke="#f59e0b" stroke-width="1"/>
            <text x="240" y="195" text-anchor="middle" fill="#f59e0b" font-size="8">Rg</text>
            <line x1="240" y1="180" x2="240" y2="185" stroke="#f59e0b" stroke-width="1"/>
        </g>
        
        <!-- Anti-aliasing Filter -->
        <g class="circuit-component" data-component="aa-filter">
            <rect x="320" y="120" width="80" height="40" fill="none" stroke="#06b6d4" stroke-width="2" rx="5"/>
            <text x="360" y="135" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">LPF</text>
            <text x="360" y="150" text-anchor="middle" fill="#94a3b8" font-size="8">fc=500Hz</text>
            
            <!-- Filter response curve -->
            <path d="M 325 125 Q 340 125 350 135 Q 360 145 375 145 Q 390 145 395 155" 
                  stroke="#06b6d4" stroke-width="1" fill="none"/>
        </g>
        
        <!-- ADC -->
        <g class="circuit-component" data-component="adc-circuit">
            <rect x="450" y="110" width="100" height="60" fill="none" stroke="#dc2626" stroke-width="2" rx="5"/>
            <text x="500" y="130" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">ADC</text>
            <text x="500" y="145" text-anchor="middle" fill="#94a3b8" font-size="9">16-bit</text>
            <text x="500" y="158" text-anchor="middle" fill="#94a3b8" font-size="9">1kHz</text>
            
            <!-- Digital output -->
            <rect x="560" y="125" width="30" height="20" fill="none" stroke="#dc2626" stroke-width="1"/>
            <text x="575" y="138" text-anchor="middle" fill="#dc2626" font-size="8">SPI</text>
        </g>
        
        <!-- Power Supply -->
        <g class="circuit-component" data-component="power-supply">
            <rect x="50" y="300" width="150" height="100" fill="none" stroke="#f59e0b" stroke-width="2" rx="5"/>
            <text x="125" y="325" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">POWER SUPPLY</text>
            
            <!-- Voltage rails -->
            <line x1="70" y1="340" x2="180" y2="340" stroke="#ef4444" stroke-width="2"/>
            <line x1="70" y1="360" x2="180" y2="360" stroke="#22c55e" stroke-width="2"/>
            <line x1="70" y1="380" x2="180" y2="380" stroke="#3b82f6" stroke-width="2"/>
            
            <text x="190" y="345" fill="#ef4444" font-size="10">+5V</text>
            <text x="190" y="365" fill="#22c55e" font-size="10">GND</text>
            <text x="190" y="385" fill="#3b82f6" font-size="10">-5V</text>
        </g>
        
        <!-- Isolation Barrier -->
        <g class="circuit-component" data-component="isolation">
            <rect x="250" y="250" width="200" height="30" fill="none" stroke="#8b5cf6" stroke-width="2" rx="5"/>
            <text x="350" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">ISOLATION BARRIER</text>
            
            <!-- Isolation symbols -->
            <path d="M 270 255 L 270 275 M 275 255 L 275 275" stroke="#8b5cf6" stroke-width="2"/>
            <path d="M 425 255 L 425 275 M 430 255 L 430 275" stroke="#8b5cf6" stroke-width="2"/>
        </g>
        
        <!-- Connection lines -->
        <line x1="280" y1="140" x2="320" y2="140" stroke="#10b981" stroke-width="2" marker-end="url(#arrowhead-schematic)"/>
        <line x1="400" y1="140" x2="450" y2="140" stroke="#06b6d4" stroke-width="2" marker-end="url(#arrowhead-schematic)"/>
        
        <!-- Power connections -->
        <line x1="125" y1="300" x2="125" y2="200" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        <line x1="240" y1="200" x2="240" y2="250" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        <line x1="360" y1="200" x2="360" y2="250" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        <line x1="500" y1="200" x2="500" y2="250" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        
        <!-- Title -->
        <text x="350" y="40" text-anchor="middle" fill="#e2e8f0" font-size="18" font-weight="700">
            ECG Signal Acquisition - Circuit Schematic
        </text>
        <text x="350" y="60" text-anchor="middle" fill="#94a3b8" font-size="12">
            Instrumentation Amplifier → Filter → ADC Chain
        </text>
    `;
}

// Handle Component Click
function handleComponentClick(event) {
    const component = event.target.closest('.block-component, .circuit-component');
    if (!component) return;
    
    const componentId = component.dataset.component;
    if (!componentId) return;
    
    // Update selected component
    diagramState.selectedComponent = componentId;
    
    // Update visual selection
    updateComponentSelection(component);
    
    // Show component information
    showComponentInfo(componentId);
}

// Update Component Selection
function updateComponentSelection(selectedComponent) {
    // Remove previous selection
    const components = document.querySelectorAll('.block-component, .circuit-component');
    components.forEach(comp => comp.classList.remove('selected'));
    
    // Add selection to current component
    selectedComponent.classList.add('selected');
}

// Show Component Info
function showComponentInfo(componentId) {
    const info = componentInfo[componentId];
    if (!info) return;
    
    // Update info panel content
    document.getElementById('componentTitle').textContent = info.title;
    document.getElementById('componentDescription').innerHTML = `<p>${info.description}</p>`;
    
    // Update specifications
    const specsDiv = document.getElementById('componentSpecs');
    specsDiv.innerHTML = '<h4>Specifications</h4>';
    
    Object.entries(info.specifications).forEach(([key, value]) => {
        const specItem = document.createElement('div');
        specItem.className = 'spec-item';
        specItem.innerHTML = `
            <span class="spec-label">${key}:</span>
            <span class="spec-value">${value}</span>
        `;
        specsDiv.appendChild(specItem);
    });
    
    // Update connections
    const connectionsDiv = document.getElementById('componentConnections');
    connectionsDiv.innerHTML = '<h4>Connections</h4>';
    
    info.connections.forEach(connection => {
        const connectionItem = document.createElement('div');
        connectionItem.className = 'connection-item';
        connectionItem.innerHTML = `
            <i class="fas fa-arrow-right"></i>
            <span>${connection}</span>
        `;
        connectionsDiv.appendChild(connectionItem);
    });
    
    // Show info panel
    document.querySelector('.component-info-panel').classList.add('active');
}

// Close Component Info
function closeComponentInfo() {
    document.querySelector('.component-info-panel').classList.remove('active');
    
    // Remove component selection
    const components = document.querySelectorAll('.block-component, .circuit-component');
    components.forEach(comp => comp.classList.remove('selected'));
    
    diagramState.selectedComponent = null;
}

// Zoom Controls
function zoomIn() {
    diagramState.zoomLevel = Math.min(diagramState.zoomLevel * 1.2, 3);
    updateDiagramTransform();
}

function zoomOut() {
    diagramState.zoomLevel = Math.max(diagramState.zoomLevel / 1.2, 0.5);
    updateDiagramTransform();
}

function resetZoom() {
    diagramState.zoomLevel = 1;
    diagramState.panOffset = { x: 0, y: 0 };
    updateDiagramTransform();
}

// Update Diagram Transform
function updateDiagramTransform() {
    const diagrams = document.querySelectorAll('.interactive-diagram');
    diagrams.forEach(diagram => {
        diagram.style.transform = `scale(${diagramState.zoomLevel}) translate(${diagramState.panOffset.x}px, ${diagramState.panOffset.y}px)`;
    });
    
    // Update zoom indicator
    updateZoomIndicator();
}

// Update Zoom Indicator
function updateZoomIndicator() {
    let indicator = document.querySelector('.zoom-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'zoom-indicator';
        document.querySelector('.interactive-diagram-container').appendChild(indicator);
    }
    
    indicator.textContent = `${Math.round(diagramState.zoomLevel * 100)}%`;
}

// Toggle Animation
function toggleAnimation() {
    diagramState.isAnimating = !diagramState.isAnimating;
    
    const animationIcon = document.getElementById('animationIcon');
    const animationText = document.getElementById('animationText');
    
    if (diagramState.isAnimating) {
        animationIcon.className = 'fas fa-pause';
        animationText.textContent = 'Stop Animation';
        startDiagramAnimation();
    } else {
        animationIcon.className = 'fas fa-play';
        animationText.textContent = 'Start Animation';
        stopDiagramAnimation();
    }
}

// Start Diagram Animation
function startDiagramAnimation() {
    const flowLines = document.querySelectorAll('.flow-line');
    flowLines.forEach(line => {
        line.classList.add('animated');
    });
}

// Stop Diagram Animation
function stopDiagramAnimation() {
    const flowLines = document.querySelectorAll('.flow-line');
    flowLines.forEach(line => {
        line.classList.remove('animated');
    });
}

// Export Diagram
function exportDiagram() {
    const diagram = document.querySelector('.interactive-diagram.active, .interactive-diagram');
    if (!diagram) return;
    
    // Create canvas for export
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = 1200;
    canvas.height = 800;
    
    // Convert SVG to image (simplified implementation)
    const svgData = new XMLSerializer().serializeToString(diagram);
    const img = new Image();
    
    img.onload = function() {
        ctx.drawImage(img, 0, 0);
        
        // Download image
        const link = document.createElement('a');
        link.download = `diagram-${diagramState.currentCategory}-${diagramState.currentType}.png`;
        link.href = canvas.toDataURL();
        link.click();
    };
    
    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
}

// Highlight Component
function highlightComponent(event) {
    const component = event.target.closest('.block-component, .circuit-component');
    if (component && component !== diagramState.selectedComponent) {
        component.style.filter = 'url(#glow)';
    }
}

// Unhighlight Component
function unhighlightComponent(event) {
    const component = event.target.closest('.block-component, .circuit-component');
    if (component && !component.classList.contains('selected')) {
        component.style.filter = '';
    }
}

// Pan functionality (simplified)
let isPanning = false;
let lastPanPoint = { x: 0, y: 0 };

function startPan(event) {
    if (event.button === 0) { // Left mouse button
        isPanning = true;
        lastPanPoint = { x: event.clientX, y: event.clientY };
        event.preventDefault();
    }
}

function handlePan(event) {
    if (isPanning) {
        const deltaX = event.clientX - lastPanPoint.x;
        const deltaY = event.clientY - lastPanPoint.y;
        
        diagramState.panOffset.x += deltaX / diagramState.zoomLevel;
        diagramState.panOffset.y += deltaY / diagramState.zoomLevel;
        
        updateDiagramTransform();
        
        lastPanPoint = { x: event.clientX, y: event.clientY };
    }
}

function endPan() {
    isPanning = false;
}

// Handle Zoom
function handleZoom(event) {
    event.preventDefault();
    
    const delta = event.deltaY > 0 ? 0.9 : 1.1;
    diagramState.zoomLevel = Math.max(0.5, Math.min(3, diagramState.zoomLevel * delta));
    
    updateDiagramTransform();
}

// Handle Keyboard
function handleKeyboard(event) {
    switch (event.key) {
        case 'Escape':
            closeComponentInfo();
            break;
        case '+':
        case '=':
            zoomIn();
            break;
        case '-':
            zoomOut();
            break;
        case '0':
            resetZoom();
            break;
        case ' ':
            event.preventDefault();
            toggleAnimation();
            break;
    }
}

// Load Patient Monitoring Signal Flow
function loadPatientMonitoringSignalFlow() {
    const svg = document.getElementById('patientMonitoringSignalFlow');
    if (!svg) return;

    // Clear existing content
    svg.innerHTML = '';

    // Add signal flow content
    svg.innerHTML = `
        <defs>
            <marker id="arrowhead-signal" markerWidth="10" markerHeight="7"
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#06b6d4" />
            </marker>
        </defs>

        <!-- ECG Signal Path -->
        <g class="signal-path" data-signal="ecg">
            <rect x="50" y="100" width="100" height="40" rx="5" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
            <text x="100" y="125" text-anchor="middle" fill="#3b82f6" font-size="12" font-weight="600">ECG Signal</text>

            <!-- Signal waveform -->
            <path d="M 60 140 L 70 140 L 75 120 L 80 160 L 85 100 L 90 140 L 140 140"
                  stroke="#3b82f6" stroke-width="2" fill="none" class="signal-waveform"/>
        </g>

        <!-- Processing Stages -->
        <g class="processing-stage" data-stage="amplification">
            <rect x="250" y="150" width="120" height="60" rx="8" fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="310" y="175" text-anchor="middle" fill="#8b5cf6" font-size="11" font-weight="600">AMPLIFICATION</text>
            <text x="310" y="190" text-anchor="middle" fill="#8b5cf6" font-size="11" font-weight="600">& FILTERING</text>
        </g>

        <!-- Signal flow arrows -->
        <line x1="150" y1="120" x2="250" y2="180" stroke="#3b82f6" stroke-width="3" marker-end="url(#arrowhead-signal)" class="flow-arrow"/>

        <!-- Title -->
        <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="18" font-weight="700">
            Patient Monitoring - Signal Flow Analysis
        </text>
    `;
}

// Load Anesthesia Machine Diagrams
function loadAnesthesiaMachineDiagrams() {
    loadAnesthesiaMachineBlock();
    loadAnesthesiaMachineSchematic();
    loadAnesthesiaMachineSignalFlow();
}

// Load Anesthesia Machine Block Diagram
function loadAnesthesiaMachineBlock() {
    // Block diagram is already loaded in HTML
    console.log('Anesthesia Machine Block Diagram loaded');

    // Add interactive behaviors specific to anesthesia machine
    const spddSections = document.querySelectorAll('.spdd-section');
    spddSections.forEach(section => {
        section.addEventListener('mouseenter', highlightSPDDSection);
        section.addEventListener('mouseleave', unhighlightSPDDSection);
    });
}

// Highlight SPDD Section
function highlightSPDDSection(event) {
    const section = event.currentTarget;
    section.style.filter = 'brightness(1.2)';
    section.style.transform = 'scale(1.02)';
}

// Unhighlight SPDD Section
function unhighlightSPDDSection(event) {
    const section = event.currentTarget;
    section.style.filter = '';
    section.style.transform = '';
}

// Load Anesthesia Machine Schematic
function loadAnesthesiaMachineSchematic() {
    console.log('Loading Anesthesia Machine Schematic');
}

// Load Anesthesia Machine Signal Flow
function loadAnesthesiaMachineSignalFlow() {
    console.log('Loading Anesthesia Machine Signal Flow');
}

// Export functions for global access
window.initializeInteractiveDiagrams = initializeInteractiveDiagrams;
window.showCategory = showCategory;
window.showDiagramType = showDiagramType;
window.zoomIn = zoomIn;
window.zoomOut = zoomOut;
window.resetZoom = resetZoom;
window.toggleAnimation = toggleAnimation;
window.exportDiagram = exportDiagram;
window.closeComponentInfo = closeComponentInfo;
