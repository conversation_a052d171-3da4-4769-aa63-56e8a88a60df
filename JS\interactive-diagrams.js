// ===== INTERACTIVE DIAGRAMS SYSTEM ===== //

// Diagram State
const diagramState = {
    currentCategory: 'patient-monitoring',
    currentType: 'block',
    zoomLevel: 1,
    isAnimating: false,
    selectedComponent: null,
    panOffset: { x: 0, y: 0 },
    isDragging: false
};

// Component Information Database
const componentInfo = {
    'patient-interface': {
        title: 'Patient Interface',
        description: 'The patient interface consists of electrodes, sensors, and transducers that acquire physiological signals directly from the patient. This includes ECG electrodes, SpO₂ sensors, temperature probes, and blood pressure cuffs.',
        specifications: {
            'ECG Electrodes': 'Ag/AgCl, disposable',
            'Input Impedance': '>10 MΩ',
            'Frequency Response': '0.05-150 Hz',
            'Safety Standard': 'IEC 60601-1',
            'Isolation': '4000V AC'
        },
        connections: [
            'Signal Acquisition Module',
            'Patient Safety Isolation',
            'Defibrillation Protection'
        ]
    },
    'signal-acquisition': {
        title: 'Signal Acquisition',
        description: 'The signal acquisition module amplifies, filters, and conditions the weak physiological signals from the patient interface. It includes instrumentation amplifiers, anti-aliasing filters, and gain control circuits.',
        specifications: {
            'Gain Range': '100-10,000',
            'CMRR': '>100 dB',
            'Input Noise': '<2 μV RMS',
            'Bandwidth': '0.05-1000 Hz',
            'THD': '<0.1%'
        },
        connections: [
            'Patient Interface',
            'ADC Module',
            'Calibration Circuit'
        ]
    },
    'adc': {
        title: 'Analog-to-Digital Converter',
        description: 'High-resolution ADC converts the conditioned analog signals to digital format for processing. Features simultaneous sampling across multiple channels with precise timing.',
        specifications: {
            'Resolution': '16-bit',
            'Sampling Rate': '1000 Hz',
            'Channels': '12 simultaneous',
            'SNR': '>90 dB',
            'Linearity': '±0.5 LSB'
        },
        connections: [
            'Signal Acquisition',
            'Digital Processing Unit',
            'Timing Reference'
        ]
    },
    'digital-processing': {
        title: 'Digital Processing Unit',
        description: 'Advanced DSP algorithms process the digitized signals to extract vital signs, detect arrhythmias, and perform real-time analysis. Includes QRS detection, heart rate calculation, and artifact rejection.',
        specifications: {
            'Processor': 'ARM Cortex-M7',
            'Clock Speed': '400 MHz',
            'RAM': '1 MB',
            'Flash': '2 MB',
            'Processing Delay': '<100 ms'
        },
        connections: [
            'ADC Module',
            'Display System',
            'Alarm System',
            'Data Storage'
        ]
    },
    'display-system': {
        title: 'Display System',
        description: 'High-resolution color display presents waveforms, numerical values, and alarms in real-time. Features touch interface and customizable layouts for different clinical scenarios.',
        specifications: {
            'Screen Size': '15" TFT LCD',
            'Resolution': '1920x1080',
            'Refresh Rate': '60 Hz',
            'Touch': 'Capacitive',
            'Brightness': '400 cd/m²'
        },
        connections: [
            'Digital Processing Unit',
            'User Interface Controller',
            'Network Interface'
        ]
    },
    'ecg-processing': {
        title: 'ECG Processing',
        description: 'Specialized ECG processing algorithms for lead selection, QRS detection, heart rate calculation, and arrhythmia analysis. Implements advanced filtering and baseline correction.',
        specifications: {
            'Lead Configuration': '3, 5, or 12-lead',
            'QRS Detection': '>99% accuracy',
            'Heart Rate Range': '15-300 bpm',
            'Arrhythmia Detection': '15+ types',
            'ST Analysis': '±2 mV range'
        },
        connections: [
            'Signal Acquisition',
            'Alarm System',
            'Trend Analysis'
        ]
    },
    'spo2-processing': {
        title: 'SpO₂ Processing',
        description: 'Pulse oximetry processing using red and infrared light absorption to calculate oxygen saturation and pulse rate. Features motion artifact reduction and low perfusion algorithms.',
        specifications: {
            'SpO₂ Range': '70-100%',
            'Accuracy': '±2% (70-100%)',
            'Pulse Rate': '30-250 bpm',
            'Update Rate': '1 Hz',
            'Motion Tolerance': 'Advanced algorithm'
        },
        connections: [
            'Pulse Oximeter Sensor',
            'Perfusion Index Calculator',
            'Alarm System'
        ]
    },
    'nibp-processing': {
        title: 'NIBP Processing',
        description: 'Non-invasive blood pressure measurement using oscillometric method. Automated cuff inflation/deflation with artifact detection and measurement validation.',
        specifications: {
            'Method': 'Oscillometric',
            'Pressure Range': '0-300 mmHg',
            'Accuracy': '±3 mmHg',
            'Measurement Time': '15-45 seconds',
            'Cuff Sizes': 'Neonatal to Thigh'
        },
        connections: [
            'Pneumatic System',
            'Pressure Transducer',
            'Cuff Control Valve'
        ]
    },
    'alarm-system': {
        title: 'Alarm System',
        description: 'Intelligent alarm management with priority classification, audio/visual alerts, and alarm fatigue reduction. Complies with IEC 60601-1-8 alarm standard.',
        specifications: {
            'Priority Levels': 'High, Medium, Low',
            'Audio Range': '45-85 dB',
            'Visual Indicators': 'LED + Display',
            'Alarm Delay': 'Configurable',
            'Silence Duration': '2-15 minutes'
        },
        connections: [
            'All Processing Modules',
            'Audio System',
            'Visual Indicators',
            'Network Alerts'
        ]
    },
    'data-storage': {
        title: 'Data Storage',
        description: 'Secure data storage system for patient trends, events, and waveforms. Features automatic backup, data compression, and HIPAA-compliant encryption.',
        specifications: {
            'Storage Capacity': '500 GB SSD',
            'Trend Duration': '72 hours',
            'Waveform Storage': '24 hours',
            'Backup': 'Automatic',
            'Encryption': 'AES-256'
        },
        connections: [
            'Digital Processing Unit',
            'Network Interface',
            'USB Export',
            'Print System'
        ]
    },

    // Anesthesia Machine Components
    'gas-cylinders': {
        title: 'Gas Cylinders',
        description: 'High-pressure gas storage cylinders containing medical gases (O₂, N₂O, Air). Each cylinder has specific color coding and pressure ratings for safety identification.',
        specifications: {
            'Oxygen Pressure': '2200 PSI',
            'N₂O Pressure': '745 PSI',
            'Air Pressure': '2200 PSI',
            'Safety Features': 'PISS, CGA fittings',
            'Capacity': 'E-size cylinders'
        },
        connections: [
            'Pressure Regulators',
            'Cylinder Yokes',
            'Check Valves'
        ]
    },

    'pipeline-supply': {
        title: 'Pipeline Supply',
        description: 'Central gas supply system delivering medical gases through hospital pipelines. Provides primary gas source with automatic cylinder backup.',
        specifications: {
            'Supply Pressure': '50 PSI',
            'Gas Types': 'O₂, N₂O, Air',
            'Connections': 'DISS fittings',
            'Backup System': 'Automatic switchover',
            'Alarm System': 'Low pressure alerts'
        },
        connections: [
            'Pressure Regulators',
            'Gas Supply Monitors',
            'Backup Systems'
        ]
    },

    'pressure-regulators': {
        title: 'Pressure Regulators',
        description: 'Reduce high-pressure gas supply to safe working pressure (50 PSI). Include safety relief valves and pressure gauges for monitoring.',
        specifications: {
            'Input Pressure': '50-2200 PSI',
            'Output Pressure': '50 PSI ±5',
            'Flow Capacity': '120 L/min',
            'Relief Valve': '75 PSI setting',
            'Accuracy': '±2 PSI'
        },
        connections: [
            'Gas Supply Sources',
            'Flowmeter Assembly',
            'Safety Relief System'
        ]
    },

    'flowmeters': {
        title: 'Variable Orifice Flowmeters',
        description: 'Tapered tube flowmeters with floating indicators for precise gas flow measurement. Each gas has dedicated, calibrated flowmeter.',
        specifications: {
            'Flow Range': '0.1-15 L/min',
            'Accuracy': '±5% full scale',
            'Gas Specific': 'Individual calibration',
            'Tube Material': 'Borosilicate glass',
            'Float Design': 'Non-magnetic'
        },
        connections: [
            'Pressure Regulators',
            'Gas Mixing Chamber',
            'Hypoxic Guard System'
        ]
    },

    'vaporizers': {
        title: 'Anesthetic Vaporizers',
        description: 'Precision vaporizers for volatile anesthetic agents. Temperature and pressure compensated for accurate concentration delivery.',
        specifications: {
            'Agents': 'Sevoflurane, Isoflurane',
            'Concentration Range': '0-8%',
            'Accuracy': '±10% of setting',
            'Temperature Compensation': 'Automatic',
            'Filling System': 'Agent-specific'
        },
        connections: [
            'Fresh Gas Flow',
            'Gas Mixing Chamber',
            'Agent Monitors'
        ]
    },

    'gas-mixing': {
        title: 'Gas Mixing Chamber',
        description: 'Combines fresh gas flows and anesthetic vapors into homogeneous mixture. Ensures proper gas composition before delivery.',
        specifications: {
            'Mixing Volume': '500 mL',
            'Flow Range': '0.2-15 L/min',
            'Response Time': '<30 seconds',
            'Material': 'Stainless steel',
            'Connections': 'Standard 22mm'
        },
        connections: [
            'Flowmeters',
            'Vaporizers',
            'Breathing Circuit'
        ]
    },

    'breathing-circuit': {
        title: 'Breathing Circuit',
        description: 'Connects anesthesia machine to patient airway. Includes inspiratory and expiratory limbs with CO₂ absorption system.',
        specifications: {
            'Circuit Type': 'Circle system',
            'Tubing Diameter': '22mm ID',
            'CO₂ Absorber': 'Soda lime',
            'Compliance': '3 mL/cmH₂O',
            'Dead Space': '<50 mL'
        },
        connections: [
            'Gas Mixing Chamber',
            'Patient Interface',
            'Ventilator Bellows'
        ]
    },

    'scavenging-system': {
        title: 'Scavenging System',
        description: 'Removes waste anesthetic gases from operating room to prevent occupational exposure. Active or passive evacuation systems.',
        specifications: {
            'Evacuation Rate': '0.5-10 L/min',
            'Vacuum Level': '-0.5 to -10 cmH₂O',
            'Interface': 'Open or closed',
            'Reservoir': '1-3 L capacity',
            'Alarm System': 'High/low pressure'
        },
        connections: [
            'Breathing Circuit',
            'Vacuum System',
            'Waste Gas Monitors'
        ]
    },

    // ECG Systems Components
    'limb-leads': {
        title: 'Limb Lead Electrodes',
        description: 'Standard limb electrodes (RA, LA, LL, RL) for frontal plane ECG recording. Provides leads I, II, III and augmented leads aVR, aVL, aVF.',
        specifications: {
            'Electrode Type': 'Ag/AgCl disposable',
            'Lead Configuration': 'I, II, III, aVR, aVL, aVF',
            'Impedance': '<5 kΩ',
            'Frequency Response': '0.05-150 Hz',
            'Noise Level': '<10 μV'
        },
        connections: [
            'Lead Selection Matrix',
            'Differential Amplifier',
            'Right Leg Drive'
        ]
    },

    'precordial-leads': {
        title: 'Precordial Lead Electrodes',
        description: 'Chest electrodes V1-V6 for horizontal plane ECG recording. Provides detailed view of anterior, septal, and lateral cardiac regions.',
        specifications: {
            'Electrode Positions': 'V1-V6 chest placement',
            'Lead Configuration': 'Unipolar precordial',
            'Spatial Resolution': 'Horizontal plane',
            'Impedance': '<5 kΩ',
            'Sensitivity': '10 mm/mV'
        },
        connections: [
            'Wilson Central Terminal',
            'Lead Selection Matrix',
            'Amplifier Array'
        ]
    },

    'lead-selection': {
        title: 'Lead Selection Matrix',
        description: 'Electronic switching matrix for selecting and combining electrode signals to form 12-lead ECG configuration. Includes Wilson central terminal.',
        specifications: {
            'Switch Type': 'Analog multiplexer',
            'Channels': '12 simultaneous',
            'Switching Speed': '<1 ms',
            'Crosstalk': '<-60 dB',
            'Isolation': '>100 MΩ'
        },
        connections: [
            'Limb Electrodes',
            'Precordial Electrodes',
            'Differential Amplifiers'
        ]
    },

    'differential-amplifier': {
        title: 'Differential Amplifier',
        description: 'High-performance instrumentation amplifier for ECG signal acquisition. Provides high CMRR and low noise for quality signal conditioning.',
        specifications: {
            'Gain Range': '100-10,000',
            'CMRR': '>120 dB',
            'Input Impedance': '>100 MΩ',
            'Noise': '<2 μV RMS',
            'Bandwidth': '0.05-1000 Hz'
        },
        connections: [
            'Lead Selection Matrix',
            'Isolation Amplifier',
            'Calibration Circuit'
        ]
    },

    'isolation-amplifier': {
        title: 'Isolation Amplifier',
        description: 'Patient isolation amplifier providing 4000V AC isolation for safety. Prevents leakage current and protects against defibrillation.',
        specifications: {
            'Isolation Voltage': '4000V AC',
            'Leakage Current': '<10 μA',
            'Bandwidth': '0.05-150 Hz',
            'Gain Accuracy': '±1%',
            'Defibrillation Protection': 'Yes'
        },
        connections: [
            'Differential Amplifier',
            'ECG Filters',
            'Safety Ground'
        ]
    },

    'ecg-filters': {
        title: 'ECG Signal Filters',
        description: 'Multi-stage filtering system including high-pass, low-pass, and notch filters for ECG signal conditioning and artifact removal.',
        specifications: {
            'High-Pass Filter': '0.05 Hz (-3dB)',
            'Low-Pass Filter': '150 Hz (-3dB)',
            'Notch Filter': '50/60 Hz',
            'Filter Type': 'Butterworth',
            'Order': '4th order'
        },
        connections: [
            'Isolation Amplifier',
            'ECG ADC',
            'Baseline Correction'
        ]
    },

    'ecg-adc': {
        title: 'ECG Analog-to-Digital Converter',
        description: 'High-resolution ADC for digitizing conditioned ECG signals. Simultaneous sampling across all 12 leads with precise timing.',
        specifications: {
            'Resolution': '16-bit',
            'Sampling Rate': '1000 Hz',
            'Channels': '12 simultaneous',
            'SNR': '>90 dB',
            'Linearity': '±0.5 LSB'
        },
        connections: [
            'ECG Filters',
            'DSP Engine',
            'Timing Reference'
        ]
    },

    'ecg-dsp': {
        title: 'ECG Digital Signal Processor',
        description: 'Dedicated DSP for real-time ECG analysis including QRS detection, heart rate calculation, and arrhythmia detection algorithms.',
        specifications: {
            'Processor': 'ARM Cortex-M7',
            'Clock Speed': '400 MHz',
            'QRS Detection': '>99% accuracy',
            'Heart Rate Range': '15-300 bpm',
            'Processing Delay': '<100 ms'
        },
        connections: [
            'ECG ADC',
            'Analysis Algorithms',
            'Display System'
        ]
    },

    'analysis-algorithms': {
        title: 'ECG Analysis Algorithms',
        description: 'Advanced algorithms for ECG interpretation including rhythm analysis, ST segment monitoring, and automated diagnosis.',
        specifications: {
            'Arrhythmia Types': '15+ classifications',
            'ST Analysis': '±2 mV range',
            'Axis Calculation': '±180 degrees',
            'Interval Measurement': 'PR, QRS, QT',
            'Diagnostic Accuracy': '>95%'
        },
        connections: [
            'DSP Engine',
            'Interpretation System',
            'Alarm Management'
        ]
    },

    'waveform-display': {
        title: 'ECG Waveform Display',
        description: 'High-resolution display system for real-time ECG waveforms with multiple lead configurations and sweep speeds.',
        specifications: {
            'Display Type': 'TFT LCD',
            'Resolution': '1920x1080',
            'Sweep Speed': '6.25-50 mm/s',
            'Gain Settings': '2.5-40 mm/mV',
            'Lead Formats': '3, 6, 12-lead'
        },
        connections: [
            'DSP Engine',
            'User Interface',
            'Print System'
        ]
    },

    'measurements': {
        title: 'ECG Measurements',
        description: 'Automated measurement system for ECG intervals, amplitudes, and derived parameters with statistical analysis.',
        specifications: {
            'Heart Rate': '15-300 bpm',
            'PR Interval': '80-400 ms',
            'QRS Duration': '40-200 ms',
            'QT Interval': '200-600 ms',
            'Accuracy': '±5 ms'
        },
        connections: [
            'Analysis Algorithms',
            'Display System',
            'Trend Analysis'
        ]
    },

    'interpretation': {
        title: 'ECG Interpretation System',
        description: 'Computer-aided ECG interpretation providing automated diagnosis and clinical decision support with confidence levels.',
        specifications: {
            'Diagnostic Categories': '200+ conditions',
            'Confidence Levels': 'High/Medium/Low',
            'Sensitivity': '>95%',
            'Specificity': '>90%',
            'Update Rate': 'Real-time'
        },
        connections: [
            'Analysis Algorithms',
            'Clinical Database',
            'Report Generation'
        ]
    },

    // Mechanical Ventilation Components
    'oxygen-source': {
        title: 'Oxygen Source',
        description: 'High-pressure oxygen supply system providing variable FiO₂ from 21% to 100%. Includes pressure regulation and flow control.',
        specifications: {
            'FiO₂ Range': '21-100%',
            'Supply Pressure': '50 PSI',
            'Flow Rate': '0-120 L/min',
            'Accuracy': '±3% FiO₂',
            'Response Time': '<2 seconds'
        },
        connections: [
            'Gas Blender',
            'Pressure Regulation',
            'Flow Sensors'
        ]
    },

    'air-source': {
        title: 'Medical Air Source',
        description: 'Medical-grade compressed air supply system. Provides carrier gas for oxygen blending and pneumatic control systems.',
        specifications: {
            'Air Quality': 'Medical grade',
            'Supply Pressure': '50 PSI',
            'Flow Rate': '0-120 L/min',
            'Moisture Content': '<50 ppm',
            'Oil Content': '<0.1 mg/m³'
        },
        connections: [
            'Gas Blender',
            'Pressure Regulation',
            'Pneumatic Controls'
        ]
    },

    'gas-blender': {
        title: 'Gas Blender',
        description: 'Precision gas mixing system for accurate FiO₂ control. Uses proportional valves to blend oxygen and air to desired concentration.',
        specifications: {
            'FiO₂ Accuracy': '±3%',
            'Response Time': '<5 seconds',
            'Flow Range': '0.5-120 L/min',
            'Mixing Ratio': 'Proportional control',
            'Calibration': 'Automatic'
        },
        connections: [
            'Oxygen Source',
            'Air Source',
            'Flow Control'
        ]
    },

    'pressure-regulation': {
        title: 'Pressure Regulation System',
        description: 'Multi-stage pressure regulation providing stable working pressure for ventilator operation and patient safety.',
        specifications: {
            'Working Pressure': '10-100 cmH₂O',
            'Pressure Accuracy': '±2 cmH₂O',
            'Relief Valve': '120 cmH₂O',
            'Response Time': '<50 ms',
            'Stability': '±1 cmH₂O'
        },
        connections: [
            'Gas Blender',
            'Flow Control',
            'Safety Systems'
        ]
    },

    'microprocessor': {
        title: 'Ventilator Microprocessor',
        description: 'Central processing unit controlling all ventilator functions including mode selection, parameter monitoring, and safety systems.',
        specifications: {
            'Processor': 'ARM Cortex-A9',
            'Clock Speed': '1 GHz',
            'Memory': '1 GB RAM',
            'Real-time OS': 'QNX',
            'Safety Rating': 'IEC 60601-1'
        },
        connections: [
            'Mode Selection',
            'Flow Control',
            'Monitoring Systems'
        ]
    },

    'mode-selection': {
        title: 'Ventilation Mode Selection',
        description: 'Software-controlled mode selection system supporting volume control, pressure control, and advanced ventilation modes.',
        specifications: {
            'Volume Modes': 'VCV, SIMV, A/C',
            'Pressure Modes': 'PCV, PSV, CPAP',
            'Advanced Modes': 'PRVC, APRV, BiPAP',
            'Mode Switching': 'Seamless transition',
            'Safety Checks': 'Parameter validation'
        },
        connections: [
            'Microprocessor',
            'Flow Control',
            'Parameter Monitoring'
        ]
    },

    'flow-control': {
        title: 'Flow Control System',
        description: 'Precision flow control using servo-controlled valves for accurate tidal volume and flow rate delivery during inspiration.',
        specifications: {
            'Flow Range': '0.5-120 L/min',
            'Flow Accuracy': '±5%',
            'Response Time': '<20 ms',
            'Control Type': 'Servo valve',
            'Flow Patterns': 'Square, decelerating'
        },
        connections: [
            'Microprocessor',
            'Gas Blender',
            'Flow Sensors'
        ]
    },

    'peep-control': {
        title: 'PEEP Control System',
        description: 'Positive End-Expiratory Pressure control system maintaining baseline pressure during expiration for improved oxygenation.',
        specifications: {
            'PEEP Range': '0-30 cmH₂O',
            'Accuracy': '±1 cmH₂O',
            'Response Time': '<100 ms',
            'Valve Type': 'Proportional',
            'Auto-PEEP Detection': 'Yes'
        },
        connections: [
            'Expiratory Limb',
            'Pressure Sensors',
            'Microprocessor'
        ]
    },

    'vent-safety': {
        title: 'Ventilator Safety Systems',
        description: 'Comprehensive safety monitoring including pressure limits, disconnect alarms, and backup ventilation systems.',
        specifications: {
            'Pressure Limits': 'High/Low alarms',
            'Volume Limits': 'Minute ventilation',
            'Apnea Backup': 'Automatic mode',
            'Power Backup': '30 minutes battery',
            'Safety Standards': 'ISO 80601-2-12'
        },
        connections: [
            'All Monitoring Systems',
            'Alarm System',
            'Backup Power'
        ]
    },

    'inspiratory-limb': {
        title: 'Inspiratory Limb',
        description: 'Heated inspiratory breathing circuit delivering humidified gas mixture from ventilator to patient during inspiration.',
        specifications: {
            'Tubing Diameter': '22mm ID',
            'Length': '1.5-2.0 meters',
            'Heating': 'Heated wire',
            'Temperature': '37°C ±2°C',
            'Compliance': '2-4 mL/cmH₂O'
        },
        connections: [
            'Flow Control',
            'Humidifier',
            'Patient Connection'
        ]
    },

    'expiratory-limb': {
        title: 'Expiratory Limb',
        description: 'Expiratory breathing circuit with PEEP valve and flow measurement for exhaled gas monitoring and control.',
        specifications: {
            'Tubing Diameter': '22mm ID',
            'Length': '1.5-2.0 meters',
            'PEEP Valve': 'Integrated',
            'Flow Measurement': 'Bidirectional',
            'Dead Space': '<50 mL'
        },
        connections: [
            'PEEP Control',
            'Flow Sensors',
            'Gas Analysis'
        ]
    },

    'humidifier': {
        title: 'Active Humidification System',
        description: 'Heated humidifier providing optimal temperature and humidity for inspired gas to prevent airway drying and maintain mucociliary function.',
        specifications: {
            'Temperature': '37°C ±2°C',
            'Humidity': '100% RH',
            'Water Capacity': '340 mL',
            'Heating Power': '60W',
            'Auto-fill': 'Available'
        },
        connections: [
            'Inspiratory Limb',
            'Temperature Sensors',
            'Water Supply'
        ]
    },

    'vent-filters': {
        title: 'Breathing Circuit Filters',
        description: 'High-efficiency particulate and bacterial filters protecting patient and ventilator from contamination and cross-infection.',
        specifications: {
            'Filter Type': 'HEPA/Bacterial',
            'Efficiency': '>99.97% at 0.3μm',
            'Resistance': '<2 cmH₂O at 60 L/min',
            'Capacity': '24-48 hours',
            'Viral Filtration': '>99.999%'
        },
        connections: [
            'Inspiratory Limb',
            'Expiratory Limb',
            'Patient Connection'
        ]
    },

    'patient-connection': {
        title: 'Patient Connection Interface',
        description: 'Secure patient interface including endotracheal tube connector, pressure monitoring port, and safety disconnect features.',
        specifications: {
            'Connector Type': '15mm/22mm ISO',
            'Pressure Port': 'Luer lock',
            'Safety Features': 'Anti-disconnect',
            'Materials': 'Medical grade plastic',
            'Sterilization': 'ETO/Autoclave'
        },
        connections: [
            'Inspiratory Limb',
            'Expiratory Limb',
            'Pressure Sensors'
        ]
    },

    'pressure-sensors': {
        title: 'Pressure Monitoring Sensors',
        description: 'High-accuracy pressure transducers monitoring airway, plateau, and PEEP pressures for ventilation control and safety.',
        specifications: {
            'Pressure Range': '-20 to +120 cmH₂O',
            'Accuracy': '±2% full scale',
            'Response Time': '<5 ms',
            'Sensor Type': 'Piezoresistive',
            'Sampling Rate': '100 Hz'
        },
        connections: [
            'Patient Connection',
            'Microprocessor',
            'Display System'
        ]
    },

    'flow-sensors': {
        title: 'Flow Measurement Sensors',
        description: 'Bidirectional flow sensors measuring inspiratory and expiratory flow rates for volume calculation and leak detection.',
        specifications: {
            'Flow Range': '±180 L/min',
            'Accuracy': '±3% reading',
            'Response Time': '<10 ms',
            'Sensor Type': 'Differential pressure',
            'Dead Space': '<5 mL'
        },
        connections: [
            'Breathing Circuit',
            'Volume Calculation',
            'Leak Detection'
        ]
    },

    'volume-measurement': {
        title: 'Volume Measurement System',
        description: 'Integrated volume calculation system determining tidal volume, minute ventilation, and compliance measurements.',
        specifications: {
            'Tidal Volume': '50-2000 mL',
            'Minute Volume': '0.5-50 L/min',
            'Accuracy': '±5% or 10 mL',
            'Compliance': '5-100 mL/cmH₂O',
            'Update Rate': '1 Hz'
        },
        connections: [
            'Flow Sensors',
            'Pressure Sensors',
            'Display System'
        ]
    },

    'gas-analysis': {
        title: 'Gas Analysis System',
        description: 'Real-time monitoring of oxygen, carbon dioxide, and anesthetic agent concentrations in inspired and expired gases.',
        specifications: {
            'O₂ Range': '0-100%',
            'CO₂ Range': '0-15%',
            'O₂ Accuracy': '±2%',
            'CO₂ Accuracy': '±2 mmHg',
            'Response Time': '<200 ms'
        },
        connections: [
            'Breathing Circuit',
            'Sampling Line',
            'Display System'
        ]
    },

    'vent-display': {
        title: 'Ventilator Display System',
        description: 'High-resolution touchscreen display showing real-time waveforms, parameters, alarms, and ventilator settings.',
        specifications: {
            'Display Type': '15" TFT touchscreen',
            'Resolution': '1024x768',
            'Waveforms': 'Pressure, flow, volume',
            'Trends': '24-hour storage',
            'Languages': 'Multi-language support'
        },
        connections: [
            'All Monitoring Systems',
            'User Interface',
            'Data Storage'
        ]
    }
};

// Initialize Interactive Diagrams
function initializeInteractiveDiagrams() {
    console.log('Initializing Interactive Diagrams...');
    
    // Set up event listeners
    setupEventListeners();
    
    // Initialize diagram interactions
    initializeDiagramInteractions();
    
    // Load default diagram
    showCategory('patient-monitoring');
    showDiagramType('block');
    
    console.log('Interactive Diagrams initialized');
}

// Setup Event Listeners
function setupEventListeners() {
    // Component click handlers
    document.addEventListener('click', handleComponentClick);
    
    // Diagram pan and zoom
    const diagrams = document.querySelectorAll('.interactive-diagram');
    diagrams.forEach(diagram => {
        diagram.addEventListener('mousedown', startPan);
        diagram.addEventListener('mousemove', handlePan);
        diagram.addEventListener('mouseup', endPan);
        diagram.addEventListener('wheel', handleZoom);
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboard);
}

// Initialize Diagram Interactions
function initializeDiagramInteractions() {
    // Add hover effects to components
    const components = document.querySelectorAll('.block-component');
    components.forEach(component => {
        component.addEventListener('mouseenter', highlightComponent);
        component.addEventListener('mouseleave', unhighlightComponent);
    });
}

// Show Category
function showCategory(category) {
    // Update state
    diagramState.currentCategory = category;
    
    // Update tab states
    const tabs = document.querySelectorAll('.category-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.category === category) {
            tab.classList.add('active');
        }
    });
    
    // Show category panel
    const panels = document.querySelectorAll('.diagram-category-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
    });
    
    const activePanel = document.getElementById(`${category}-diagrams`);
    if (activePanel) {
        activePanel.classList.add('active');
    }
    
    // Load category-specific diagrams
    loadCategoryDiagrams(category);
}

// Show Diagram Type
function showDiagramType(type) {
    // Update state
    diagramState.currentType = type;
    
    // Update button states
    const buttons = document.querySelectorAll('.type-btn');
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.type === type) {
            btn.classList.add('active');
        }
    });
    
    // Show type panel
    const panels = document.querySelectorAll('.diagram-type-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
        if (panel.dataset.type === type) {
            panel.classList.add('active');
        }
    });
    
    // Load type-specific content
    loadDiagramType(type);
}

// Load Category Diagrams
function loadCategoryDiagrams(category) {
    console.log(`Loading diagrams for category: ${category}`);
    
    switch (category) {
        case 'patient-monitoring':
            loadPatientMonitoringDiagrams();
            break;
        case 'anesthesia-machine':
            loadAnesthesiaMachineDiagrams();
            break;
        case 'ecg-systems':
            loadECGSystemDiagrams();
            break;
        case 'ventilation':
            loadVentilationDiagrams();
            break;
        case 'hemodynamics':
            loadHemodynamicsDiagrams();
            break;
        case 'gas-flow':
            loadGasFlowDiagrams();
            break;
    }
}

// Load Diagram Type
function loadDiagramType(type) {
    console.log(`Loading diagram type: ${type}`);
    
    switch (type) {
        case 'block':
            loadBlockDiagrams();
            break;
        case 'schematic':
            loadSchematicDiagrams();
            break;
        case 'signal-flow':
            loadSignalFlowDiagrams();
            break;
    }
}

// Load Patient Monitoring Diagrams
function loadPatientMonitoringDiagrams() {
    // Block diagram is already loaded in HTML
    // Load schematic and signal flow diagrams
    loadPatientMonitoringSchematic();
    loadPatientMonitoringSignalFlow();
}

// Load Patient Monitoring Schematic
function loadPatientMonitoringSchematic() {
    const svg = document.getElementById('patientMonitoringSchematic');
    if (!svg) return;
    
    // Clear existing content
    svg.innerHTML = '';
    
    // Add schematic content
    svg.innerHTML = `
        <defs>
            <marker id="arrowhead-schematic" markerWidth="10" markerHeight="7" 
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
            </marker>
        </defs>
        
        <!-- ECG Input Stage -->
        <g class="circuit-component" data-component="ecg-input">
            <rect x="50" y="100" width="120" height="80" fill="none" stroke="#10b981" stroke-width="2" rx="5"/>
            <text x="110" y="125" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">ECG INPUT</text>
            <text x="110" y="140" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">STAGE</text>
            
            <!-- Input terminals -->
            <circle cx="30" cy="120" r="3" fill="#3b82f6"/>
            <circle cx="30" cy="140" r="3" fill="#ef4444"/>
            <circle cx="30" cy="160" r="3" fill="#22c55e"/>
            <text x="20" y="125" text-anchor="end" fill="#94a3b8" font-size="10">RA</text>
            <text x="20" y="145" text-anchor="end" fill="#94a3b8" font-size="10">LA</text>
            <text x="20" y="165" text-anchor="end" fill="#94a3b8" font-size="10">LL</text>
            
            <!-- Protection diodes -->
            <path d="M 35 120 L 45 120 M 40 115 L 40 125 M 35 125 L 45 115" stroke="#f59e0b" stroke-width="1" fill="none"/>
            <path d="M 35 140 L 45 140 M 40 135 L 40 145 M 35 145 L 45 135" stroke="#f59e0b" stroke-width="1" fill="none"/>
            <path d="M 35 160 L 45 160 M 40 155 L 40 165 M 35 165 L 45 155" stroke="#f59e0b" stroke-width="1" fill="none"/>
        </g>
        
        <!-- Instrumentation Amplifier -->
        <g class="circuit-component" data-component="inst-amp">
            <path d="M 200 100 L 280 140 L 200 180 Z" fill="none" stroke="#8b5cf6" stroke-width="2"/>
            <text x="240" y="145" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">INA</text>
            
            <!-- Input connections -->
            <line x1="170" y1="120" x2="200" y2="120" stroke="#10b981" stroke-width="2"/>
            <line x1="170" y1="160" x2="200" y2="160" stroke="#10b981" stroke-width="2"/>
            <text x="190" y="115" text-anchor="end" fill="#94a3b8" font-size="8">+</text>
            <text x="190" y="165" text-anchor="end" fill="#94a3b8" font-size="8">-</text>
            
            <!-- Gain setting -->
            <rect x="220" y="185" width="40" height="15" fill="none" stroke="#f59e0b" stroke-width="1"/>
            <text x="240" y="195" text-anchor="middle" fill="#f59e0b" font-size="8">Rg</text>
            <line x1="240" y1="180" x2="240" y2="185" stroke="#f59e0b" stroke-width="1"/>
        </g>
        
        <!-- Anti-aliasing Filter -->
        <g class="circuit-component" data-component="aa-filter">
            <rect x="320" y="120" width="80" height="40" fill="none" stroke="#06b6d4" stroke-width="2" rx="5"/>
            <text x="360" y="135" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">LPF</text>
            <text x="360" y="150" text-anchor="middle" fill="#94a3b8" font-size="8">fc=500Hz</text>
            
            <!-- Filter response curve -->
            <path d="M 325 125 Q 340 125 350 135 Q 360 145 375 145 Q 390 145 395 155" 
                  stroke="#06b6d4" stroke-width="1" fill="none"/>
        </g>
        
        <!-- ADC -->
        <g class="circuit-component" data-component="adc-circuit">
            <rect x="450" y="110" width="100" height="60" fill="none" stroke="#dc2626" stroke-width="2" rx="5"/>
            <text x="500" y="130" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">ADC</text>
            <text x="500" y="145" text-anchor="middle" fill="#94a3b8" font-size="9">16-bit</text>
            <text x="500" y="158" text-anchor="middle" fill="#94a3b8" font-size="9">1kHz</text>
            
            <!-- Digital output -->
            <rect x="560" y="125" width="30" height="20" fill="none" stroke="#dc2626" stroke-width="1"/>
            <text x="575" y="138" text-anchor="middle" fill="#dc2626" font-size="8">SPI</text>
        </g>
        
        <!-- Power Supply -->
        <g class="circuit-component" data-component="power-supply">
            <rect x="50" y="300" width="150" height="100" fill="none" stroke="#f59e0b" stroke-width="2" rx="5"/>
            <text x="125" y="325" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">POWER SUPPLY</text>
            
            <!-- Voltage rails -->
            <line x1="70" y1="340" x2="180" y2="340" stroke="#ef4444" stroke-width="2"/>
            <line x1="70" y1="360" x2="180" y2="360" stroke="#22c55e" stroke-width="2"/>
            <line x1="70" y1="380" x2="180" y2="380" stroke="#3b82f6" stroke-width="2"/>
            
            <text x="190" y="345" fill="#ef4444" font-size="10">+5V</text>
            <text x="190" y="365" fill="#22c55e" font-size="10">GND</text>
            <text x="190" y="385" fill="#3b82f6" font-size="10">-5V</text>
        </g>
        
        <!-- Isolation Barrier -->
        <g class="circuit-component" data-component="isolation">
            <rect x="250" y="250" width="200" height="30" fill="none" stroke="#8b5cf6" stroke-width="2" rx="5"/>
            <text x="350" y="270" text-anchor="middle" fill="#e2e8f0" font-size="11" font-weight="600">ISOLATION BARRIER</text>
            
            <!-- Isolation symbols -->
            <path d="M 270 255 L 270 275 M 275 255 L 275 275" stroke="#8b5cf6" stroke-width="2"/>
            <path d="M 425 255 L 425 275 M 430 255 L 430 275" stroke="#8b5cf6" stroke-width="2"/>
        </g>
        
        <!-- Connection lines -->
        <line x1="280" y1="140" x2="320" y2="140" stroke="#10b981" stroke-width="2" marker-end="url(#arrowhead-schematic)"/>
        <line x1="400" y1="140" x2="450" y2="140" stroke="#06b6d4" stroke-width="2" marker-end="url(#arrowhead-schematic)"/>
        
        <!-- Power connections -->
        <line x1="125" y1="300" x2="125" y2="200" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        <line x1="240" y1="200" x2="240" y2="250" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        <line x1="360" y1="200" x2="360" y2="250" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        <line x1="500" y1="200" x2="500" y2="250" stroke="#f59e0b" stroke-width="1" stroke-dasharray="3,3"/>
        
        <!-- Title -->
        <text x="350" y="40" text-anchor="middle" fill="#e2e8f0" font-size="18" font-weight="700">
            ECG Signal Acquisition - Circuit Schematic
        </text>
        <text x="350" y="60" text-anchor="middle" fill="#94a3b8" font-size="12">
            Instrumentation Amplifier → Filter → ADC Chain
        </text>
    `;
}

// Handle Component Click
function handleComponentClick(event) {
    const component = event.target.closest('.block-component, .circuit-component');
    if (!component) return;
    
    const componentId = component.dataset.component;
    if (!componentId) return;
    
    // Update selected component
    diagramState.selectedComponent = componentId;
    
    // Update visual selection
    updateComponentSelection(component);
    
    // Show component information
    showComponentInfo(componentId);
}

// Update Component Selection
function updateComponentSelection(selectedComponent) {
    // Remove previous selection
    const components = document.querySelectorAll('.block-component, .circuit-component');
    components.forEach(comp => comp.classList.remove('selected'));
    
    // Add selection to current component
    selectedComponent.classList.add('selected');
}

// Show Component Info
function showComponentInfo(componentId) {
    const info = componentInfo[componentId];
    if (!info) return;
    
    // Update info panel content
    document.getElementById('componentTitle').textContent = info.title;
    document.getElementById('componentDescription').innerHTML = `<p>${info.description}</p>`;
    
    // Update specifications
    const specsDiv = document.getElementById('componentSpecs');
    specsDiv.innerHTML = '<h4>Specifications</h4>';
    
    Object.entries(info.specifications).forEach(([key, value]) => {
        const specItem = document.createElement('div');
        specItem.className = 'spec-item';
        specItem.innerHTML = `
            <span class="spec-label">${key}:</span>
            <span class="spec-value">${value}</span>
        `;
        specsDiv.appendChild(specItem);
    });
    
    // Update connections
    const connectionsDiv = document.getElementById('componentConnections');
    connectionsDiv.innerHTML = '<h4>Connections</h4>';
    
    info.connections.forEach(connection => {
        const connectionItem = document.createElement('div');
        connectionItem.className = 'connection-item';
        connectionItem.innerHTML = `
            <i class="fas fa-arrow-right"></i>
            <span>${connection}</span>
        `;
        connectionsDiv.appendChild(connectionItem);
    });
    
    // Show info panel
    document.querySelector('.component-info-panel').classList.add('active');
}

// Close Component Info
function closeComponentInfo() {
    document.querySelector('.component-info-panel').classList.remove('active');
    
    // Remove component selection
    const components = document.querySelectorAll('.block-component, .circuit-component');
    components.forEach(comp => comp.classList.remove('selected'));
    
    diagramState.selectedComponent = null;
}

// Zoom Controls
function zoomIn() {
    diagramState.zoomLevel = Math.min(diagramState.zoomLevel * 1.2, 3);
    updateDiagramTransform();
}

function zoomOut() {
    diagramState.zoomLevel = Math.max(diagramState.zoomLevel / 1.2, 0.5);
    updateDiagramTransform();
}

function resetZoom() {
    diagramState.zoomLevel = 1;
    diagramState.panOffset = { x: 0, y: 0 };
    updateDiagramTransform();
}

// Update Diagram Transform
function updateDiagramTransform() {
    const diagrams = document.querySelectorAll('.interactive-diagram');
    diagrams.forEach(diagram => {
        diagram.style.transform = `scale(${diagramState.zoomLevel}) translate(${diagramState.panOffset.x}px, ${diagramState.panOffset.y}px)`;
    });
    
    // Update zoom indicator
    updateZoomIndicator();
}

// Update Zoom Indicator
function updateZoomIndicator() {
    let indicator = document.querySelector('.zoom-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.className = 'zoom-indicator';
        document.querySelector('.interactive-diagram-container').appendChild(indicator);
    }
    
    indicator.textContent = `${Math.round(diagramState.zoomLevel * 100)}%`;
}

// Toggle Animation
function toggleAnimation() {
    diagramState.isAnimating = !diagramState.isAnimating;
    
    const animationIcon = document.getElementById('animationIcon');
    const animationText = document.getElementById('animationText');
    
    if (diagramState.isAnimating) {
        animationIcon.className = 'fas fa-pause';
        animationText.textContent = 'Stop Animation';
        startDiagramAnimation();
    } else {
        animationIcon.className = 'fas fa-play';
        animationText.textContent = 'Start Animation';
        stopDiagramAnimation();
    }
}

// Start Diagram Animation
function startDiagramAnimation() {
    const flowLines = document.querySelectorAll('.flow-line');
    flowLines.forEach(line => {
        line.classList.add('animated');
    });
}

// Stop Diagram Animation
function stopDiagramAnimation() {
    const flowLines = document.querySelectorAll('.flow-line');
    flowLines.forEach(line => {
        line.classList.remove('animated');
    });
}

// Export Diagram
function exportDiagram() {
    const diagram = document.querySelector('.interactive-diagram.active, .interactive-diagram');
    if (!diagram) return;
    
    // Create canvas for export
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = 1200;
    canvas.height = 800;
    
    // Convert SVG to image (simplified implementation)
    const svgData = new XMLSerializer().serializeToString(diagram);
    const img = new Image();
    
    img.onload = function() {
        ctx.drawImage(img, 0, 0);
        
        // Download image
        const link = document.createElement('a');
        link.download = `diagram-${diagramState.currentCategory}-${diagramState.currentType}.png`;
        link.href = canvas.toDataURL();
        link.click();
    };
    
    img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
}

// Highlight Component
function highlightComponent(event) {
    const component = event.target.closest('.block-component, .circuit-component');
    if (component && component !== diagramState.selectedComponent) {
        component.style.filter = 'url(#glow)';
    }
}

// Unhighlight Component
function unhighlightComponent(event) {
    const component = event.target.closest('.block-component, .circuit-component');
    if (component && !component.classList.contains('selected')) {
        component.style.filter = '';
    }
}

// Pan functionality (simplified)
let isPanning = false;
let lastPanPoint = { x: 0, y: 0 };

function startPan(event) {
    if (event.button === 0) { // Left mouse button
        isPanning = true;
        lastPanPoint = { x: event.clientX, y: event.clientY };
        event.preventDefault();
    }
}

function handlePan(event) {
    if (isPanning) {
        const deltaX = event.clientX - lastPanPoint.x;
        const deltaY = event.clientY - lastPanPoint.y;
        
        diagramState.panOffset.x += deltaX / diagramState.zoomLevel;
        diagramState.panOffset.y += deltaY / diagramState.zoomLevel;
        
        updateDiagramTransform();
        
        lastPanPoint = { x: event.clientX, y: event.clientY };
    }
}

function endPan() {
    isPanning = false;
}

// Handle Zoom
function handleZoom(event) {
    event.preventDefault();
    
    const delta = event.deltaY > 0 ? 0.9 : 1.1;
    diagramState.zoomLevel = Math.max(0.5, Math.min(3, diagramState.zoomLevel * delta));
    
    updateDiagramTransform();
}

// Handle Keyboard
function handleKeyboard(event) {
    switch (event.key) {
        case 'Escape':
            closeComponentInfo();
            break;
        case '+':
        case '=':
            zoomIn();
            break;
        case '-':
            zoomOut();
            break;
        case '0':
            resetZoom();
            break;
        case ' ':
            event.preventDefault();
            toggleAnimation();
            break;
    }
}

// Load Patient Monitoring Signal Flow
function loadPatientMonitoringSignalFlow() {
    const svg = document.getElementById('patientMonitoringSignalFlow');
    if (!svg) return;

    // Clear existing content
    svg.innerHTML = '';

    // Add signal flow content
    svg.innerHTML = `
        <defs>
            <marker id="arrowhead-signal" markerWidth="10" markerHeight="7"
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#06b6d4" />
            </marker>
        </defs>

        <!-- ECG Signal Path -->
        <g class="signal-path" data-signal="ecg">
            <rect x="50" y="100" width="100" height="40" rx="5" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
            <text x="100" y="125" text-anchor="middle" fill="#3b82f6" font-size="12" font-weight="600">ECG Signal</text>

            <!-- Signal waveform -->
            <path d="M 60 140 L 70 140 L 75 120 L 80 160 L 85 100 L 90 140 L 140 140"
                  stroke="#3b82f6" stroke-width="2" fill="none" class="signal-waveform"/>
        </g>

        <!-- Processing Stages -->
        <g class="processing-stage" data-stage="amplification">
            <rect x="250" y="150" width="120" height="60" rx="8" fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="310" y="175" text-anchor="middle" fill="#8b5cf6" font-size="11" font-weight="600">AMPLIFICATION</text>
            <text x="310" y="190" text-anchor="middle" fill="#8b5cf6" font-size="11" font-weight="600">& FILTERING</text>
        </g>

        <!-- Signal flow arrows -->
        <line x1="150" y1="120" x2="250" y2="180" stroke="#3b82f6" stroke-width="3" marker-end="url(#arrowhead-signal)" class="flow-arrow"/>

        <!-- Title -->
        <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="18" font-weight="700">
            Patient Monitoring - Signal Flow Analysis
        </text>
    `;
}

// Load Anesthesia Machine Diagrams
function loadAnesthesiaMachineDiagrams() {
    loadAnesthesiaMachineBlock();
    loadAnesthesiaMachineSchematic();
    loadAnesthesiaMachineSignalFlow();
}

// Load Anesthesia Machine Block Diagram
function loadAnesthesiaMachineBlock() {
    // Block diagram is already loaded in HTML
    console.log('Anesthesia Machine Block Diagram loaded');

    // Add interactive behaviors specific to anesthesia machine
    const spddSections = document.querySelectorAll('.spdd-section');
    spddSections.forEach(section => {
        section.addEventListener('mouseenter', highlightSPDDSection);
        section.addEventListener('mouseleave', unhighlightSPDDSection);
    });
}

// Highlight SPDD Section
function highlightSPDDSection(event) {
    const section = event.currentTarget;
    section.style.filter = 'brightness(1.2)';
    section.style.transform = 'scale(1.02)';
}

// Unhighlight SPDD Section
function unhighlightSPDDSection(event) {
    const section = event.currentTarget;
    section.style.filter = '';
    section.style.transform = '';
}

// Load Anesthesia Machine Schematic
function loadAnesthesiaMachineSchematic() {
    console.log('Loading Anesthesia Machine Schematic');
}

// Load Anesthesia Machine Signal Flow
function loadAnesthesiaMachineSignalFlow() {
    console.log('Loading Anesthesia Machine Signal Flow');
}

// Load ECG Systems Diagrams
function loadECGSystemDiagrams() {
    loadECGSystemsBlock();
    loadECGSystemsSchematic();
    loadECGSystemsSignalFlow();
}

// Load ECG Systems Block Diagram
function loadECGSystemsBlock() {
    // Block diagram is already loaded in HTML
    console.log('ECG Systems Block Diagram loaded');

    // Add interactive behaviors specific to ECG systems
    const ecgSections = document.querySelectorAll('.ecg-section');
    ecgSections.forEach(section => {
        section.addEventListener('mouseenter', highlightECGSection);
        section.addEventListener('mouseleave', unhighlightECGSection);
    });
}

// Highlight ECG Section
function highlightECGSection(event) {
    const section = event.currentTarget;
    section.style.filter = 'brightness(1.3)';
    section.style.transform = 'scale(1.02)';
}

// Unhighlight ECG Section
function unhighlightECGSection(event) {
    const section = event.currentTarget;
    section.style.filter = '';
    section.style.transform = '';
}

// Load ECG Systems Schematic
function loadECGSystemsSchematic() {
    const svg = document.getElementById('ecgSystemsSchematic');
    if (!svg) return;

    // Clear existing content
    svg.innerHTML = '';

    // Add ECG lead configuration schematic
    svg.innerHTML = `
        <defs>
            <marker id="ecgSchematicArrow" markerWidth="10" markerHeight="7"
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
            </marker>
        </defs>

        <!-- Human Body Outline -->
        <g class="body-outline">
            <ellipse cx="600" cy="300" rx="150" ry="200" fill="none" stroke="#64748b" stroke-width="2"/>
            <circle cx="600" cy="250" r="40" fill="none" stroke="#64748b" stroke-width="2"/>
            <text x="600" y="255" text-anchor="middle" fill="#94a3b8" font-size="12" font-weight="600">PATIENT</text>
        </g>

        <!-- Limb Electrode Positions -->
        <g class="limb-electrodes">
            <!-- Right Arm -->
            <circle cx="500" cy="200" r="8" fill="#3b82f6" class="electrode" data-electrode="RA"/>
            <text x="480" y="195" text-anchor="end" fill="#3b82f6" font-size="12" font-weight="600">RA</text>

            <!-- Left Arm -->
            <circle cx="700" cy="200" r="8" fill="#10b981" class="electrode" data-electrode="LA"/>
            <text x="720" y="195" text-anchor="start" fill="#10b981" font-size="12" font-weight="600">LA</text>

            <!-- Left Leg -->
            <circle cx="650" cy="450" r="8" fill="#f59e0b" class="electrode" data-electrode="LL"/>
            <text x="670" y="455" text-anchor="start" fill="#f59e0b" font-size="12" font-weight="600">LL</text>

            <!-- Right Leg (Ground) -->
            <circle cx="550" cy="450" r="8" fill="#ef4444" class="electrode" data-electrode="RL"/>
            <text x="530" y="455" text-anchor="end" fill="#ef4444" font-size="12" font-weight="600">RL</text>
        </g>

        <!-- Precordial Electrode Positions -->
        <g class="precordial-electrodes">
            <circle cx="580" cy="220" r="6" fill="#8b5cf6" class="electrode" data-electrode="V1"/>
            <text x="575" y="210" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">V1</text>

            <circle cx="600" cy="220" r="6" fill="#8b5cf6" class="electrode" data-electrode="V2"/>
            <text x="595" y="210" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">V2</text>

            <circle cx="620" cy="240" r="6" fill="#8b5cf6" class="electrode" data-electrode="V3"/>
            <text x="615" y="230" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">V3</text>

            <circle cx="640" cy="260" r="6" fill="#8b5cf6" class="electrode" data-electrode="V4"/>
            <text x="635" y="250" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">V4</text>

            <circle cx="660" cy="280" r="6" fill="#8b5cf6" class="electrode" data-electrode="V5"/>
            <text x="655" y="270" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">V5</text>

            <circle cx="680" cy="300" r="6" fill="#8b5cf6" class="electrode" data-electrode="V6"/>
            <text x="675" y="290" text-anchor="middle" fill="#8b5cf6" font-size="10" font-weight="600">V6</text>
        </g>

        <!-- Lead Vectors -->
        <g class="lead-vectors">
            <!-- Lead I -->
            <line x1="500" y1="200" x2="700" y2="200" stroke="#3b82f6" stroke-width="3"
                  marker-end="url(#ecgSchematicArrow)" class="lead-vector"/>
            <text x="600" y="190" text-anchor="middle" fill="#3b82f6" font-size="12" font-weight="600">Lead I</text>

            <!-- Lead II -->
            <line x1="500" y1="200" x2="650" y2="450" stroke="#10b981" stroke-width="3"
                  marker-end="url(#ecgSchematicArrow)" class="lead-vector"/>
            <text x="520" y="320" text-anchor="middle" fill="#10b981" font-size="12" font-weight="600">Lead II</text>

            <!-- Lead III -->
            <line x1="700" y1="200" x2="650" y2="450" stroke="#f59e0b" stroke-width="3"
                  marker-end="url(#ecgSchematicArrow)" class="lead-vector"/>
            <text x="720" y="320" text-anchor="middle" fill="#f59e0b" font-size="12" font-weight="600">Lead III</text>
        </g>

        <!-- Einthoven Triangle -->
        <g class="einthoven-triangle">
            <rect x="50" y="100" width="300" height="200" rx="10"
                  fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" stroke-width="2"/>
            <text x="200" y="130" text-anchor="middle" fill="#3b82f6" font-size="16" font-weight="700">EINTHOVEN TRIANGLE</text>

            <!-- Triangle -->
            <path d="M 100 160 L 300 160 L 200 260 Z" fill="none" stroke="#3b82f6" stroke-width="2"/>

            <!-- Vertices -->
            <circle cx="100" cy="160" r="5" fill="#3b82f6"/>
            <circle cx="300" cy="160" r="5" fill="#10b981"/>
            <circle cx="200" cy="260" r="5" fill="#f59e0b"/>

            <!-- Labels -->
            <text x="80" y="155" text-anchor="end" fill="#3b82f6" font-size="12" font-weight="600">RA</text>
            <text x="320" y="155" text-anchor="start" fill="#10b981" font-size="12" font-weight="600">LA</text>
            <text x="200" y="280" text-anchor="middle" fill="#f59e0b" font-size="12" font-weight="600">LL</text>

            <!-- Lead labels -->
            <text x="200" y="150" text-anchor="middle" fill="#3b82f6" font-size="10" font-weight="600">I</text>
            <text x="140" y="220" text-anchor="middle" fill="#10b981" font-size="10" font-weight="600">II</text>
            <text x="260" y="220" text-anchor="middle" fill="#f59e0b" font-size="10" font-weight="600">III</text>
        </g>

        <!-- Augmented Leads -->
        <g class="augmented-leads">
            <rect x="850" y="100" width="300" height="200" rx="10"
                  fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="1000" y="130" text-anchor="middle" fill="#8b5cf6" font-size="16" font-weight="700">AUGMENTED LEADS</text>

            <!-- aVR -->
            <text x="900" y="160" fill="#ef4444" font-size="14" font-weight="600">aVR = -(I + II)/2</text>
            <text x="900" y="180" fill="#10b981" font-size="14" font-weight="600">aVL = I - II/2</text>
            <text x="900" y="200" fill="#f59e0b" font-size="14" font-weight="600">aVF = II - I/2</text>

            <!-- Hexaxial Reference -->
            <circle cx="1000" cy="240" r="40" fill="none" stroke="#8b5cf6" stroke-width="2"/>
            <line x1="960" y1="240" x2="1040" y2="240" stroke="#3b82f6" stroke-width="2"/>
            <line x1="1000" y1="200" x2="1000" y2="280" stroke="#10b981" stroke-width="2"/>
            <line x1="975" y1="215" x2="1025" y2="265" stroke="#f59e0b" stroke-width="2"/>

            <text x="1050" y="245" fill="#3b82f6" font-size="10">0°</text>
            <text x="1005" y="195" fill="#10b981" font-size="10">-90°</text>
            <text x="1005" y="290" fill="#f59e0b" font-size="10">+90°</text>
        </g>

        <!-- Wilson Central Terminal -->
        <g class="wilson-terminal">
            <rect x="50" y="400" width="300" height="150" rx="10"
                  fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="2"/>
            <text x="200" y="430" text-anchor="middle" fill="#06b6d4" font-size="16" font-weight="700">WILSON CENTRAL TERMINAL</text>

            <!-- Circuit diagram -->
            <circle cx="200" cy="480" r="30" fill="none" stroke="#06b6d4" stroke-width="2"/>
            <text x="200" y="485" text-anchor="middle" fill="#06b6d4" font-size="12" font-weight="600">WCT</text>

            <!-- Resistor connections -->
            <line x1="120" y1="460" x2="170" y2="480" stroke="#06b6d4" stroke-width="2"/>
            <line x1="120" y1="500" x2="170" y2="480" stroke="#06b6d4" stroke-width="2"/>
            <line x1="280" y1="480" x2="230" y2="480" stroke="#06b6d4" stroke-width="2"/>

            <!-- Resistor symbols -->
            <rect x="110" y="455" width="20" height="10" fill="none" stroke="#06b6d4" stroke-width="1"/>
            <rect x="110" y="495" width="20" height="10" fill="none" stroke="#06b6d4" stroke-width="1"/>
            <rect x="270" y="475" width="20" height="10" fill="none" stroke="#06b6d4" stroke-width="1"/>

            <text x="100" y="465" text-anchor="end" fill="#06b6d4" font-size="10">RA</text>
            <text x="100" y="505" text-anchor="end" fill="#06b6d4" font-size="10">LA</text>
            <text x="300" y="485" text-anchor="start" fill="#06b6d4" font-size="10">LL</text>

            <text x="200" y="520" text-anchor="middle" fill="#94a3b8" font-size="10">
                Reference for Precordial Leads
            </text>
        </g>

        <!-- Title -->
        <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
            ECG Lead Configuration & Electrode Placement
        </text>
        <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
            12-Lead ECG System with Einthoven Triangle & Wilson Central Terminal
        </text>
    `;
}

// Load ECG Systems Signal Flow
function loadECGSystemsSignalFlow() {
    const svg = document.getElementById('ecgSystemsSignalFlow');
    if (!svg) return;

    // Clear existing content
    svg.innerHTML = '';

    // Add ECG signal flow content
    svg.innerHTML = `
        <defs>
            <marker id="ecgFlowArrow" markerWidth="10" markerHeight="7"
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
            </marker>
        </defs>

        <!-- Raw ECG Signal -->
        <g class="signal-stage" data-stage="raw-ecg">
            <rect x="50" y="150" width="150" height="100" rx="10"
                  fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
            <text x="125" y="180" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="600">RAW ECG SIGNAL</text>

            <!-- Simulated noisy ECG -->
            <path d="M 70 200 L 80 200 L 85 180 L 90 220 L 95 160 L 100 200 L 180 200"
                  stroke="#3b82f6" stroke-width="2" fill="none" class="signal-waveform"/>
            <path d="M 70 210 Q 100 205 130 215 Q 160 225 180 210"
                  stroke="#ef4444" stroke-width="1" fill="none" opacity="0.7"/>

            <text x="125" y="235" text-anchor="middle" fill="#94a3b8" font-size="10">
                Amplitude: ±5mV, Noise: 50μV
            </text>
        </g>

        <!-- Amplification Stage -->
        <g class="signal-stage" data-stage="amplification">
            <rect x="250" y="150" width="150" height="100" rx="10"
                  fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
            <text x="325" y="180" text-anchor="middle" fill="#10b981" font-size="14" font-weight="600">AMPLIFICATION</text>

            <!-- Amplified ECG -->
            <path d="M 270 200 L 280 200 L 285 160 L 290 240 L 295 120 L 300 200 L 380 200"
                  stroke="#10b981" stroke-width="3" fill="none" class="signal-waveform"/>

            <text x="325" y="220" text-anchor="middle" fill="#10b981" font-size="12" font-weight="600">Gain: 1000x</text>
            <text x="325" y="235" text-anchor="middle" fill="#94a3b8" font-size="10">
                CMRR: >120dB
            </text>
        </g>

        <!-- Filtering Stage -->
        <g class="signal-stage" data-stage="filtering">
            <rect x="450" y="150" width="150" height="100" rx="10"
                  fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="525" y="180" text-anchor="middle" fill="#8b5cf6" font-size="14" font-weight="600">FILTERING</text>

            <!-- Filtered ECG -->
            <path d="M 470 200 L 480 200 L 485 170 L 490 230 L 495 140 L 500 200 L 580 200"
                  stroke="#8b5cf6" stroke-width="3" fill="none" class="signal-waveform"/>

            <text x="525" y="220" text-anchor="middle" fill="#8b5cf6" font-size="12" font-weight="600">0.05-150 Hz</text>
            <text x="525" y="235" text-anchor="middle" fill="#94a3b8" font-size="10">
                Notch: 50/60 Hz
            </text>
        </g>

        <!-- Digitization Stage -->
        <g class="signal-stage" data-stage="digitization">
            <rect x="650" y="150" width="150" height="100" rx="10"
                  fill="rgba(6, 182, 212, 0.2)" stroke="#06b6d4" stroke-width="2"/>
            <text x="725" y="180" text-anchor="middle" fill="#06b6d4" font-size="14" font-weight="600">DIGITIZATION</text>

            <!-- Digital ECG -->
            <path d="M 670 200 L 675 200 L 675 170 L 680 170 L 680 230 L 685 230 L 685 140 L 690 140 L 690 200 L 780 200"
                  stroke="#06b6d4" stroke-width="3" fill="none" class="signal-waveform"/>

            <text x="725" y="220" text-anchor="middle" fill="#06b6d4" font-size="12" font-weight="600">16-bit ADC</text>
            <text x="725" y="235" text-anchor="middle" fill="#94a3b8" font-size="10">
                1000 Hz sampling
            </text>
        </g>

        <!-- Analysis Stage -->
        <g class="signal-stage" data-stage="analysis">
            <rect x="850" y="150" width="150" height="100" rx="10"
                  fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
            <text x="925" y="180" text-anchor="middle" fill="#f59e0b" font-size="14" font-weight="600">ANALYSIS</text>

            <!-- Analyzed ECG with markers -->
            <path d="M 870 200 L 880 200 L 885 170 L 890 230 L 895 140 L 900 200 L 980 200"
                  stroke="#f59e0b" stroke-width="3" fill="none" class="signal-waveform"/>

            <!-- QRS markers -->
            <circle cx="885" cy="170" r="3" fill="#ef4444"/>
            <circle cx="895" cy="140" r="3" fill="#ef4444"/>

            <text x="925" y="220" text-anchor="middle" fill="#f59e0b" font-size="12" font-weight="600">QRS Detection</text>
            <text x="925" y="235" text-anchor="middle" fill="#94a3b8" font-size="10">
                HR: 75 bpm
            </text>
        </g>

        <!-- Processing Algorithms -->
        <g class="processing-algorithms">
            <rect x="200" y="350" width="800" height="150" rx="15"
                  fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" stroke-width="2"/>
            <text x="600" y="380" text-anchor="middle" fill="#ef4444" font-size="16" font-weight="700">
                REAL-TIME PROCESSING ALGORITHMS
            </text>

            <!-- Algorithm blocks -->
            <rect x="250" y="400" width="120" height="60" rx="8"
                  fill="rgba(239, 68, 68, 0.2)" stroke="#ef4444" stroke-width="2"/>
            <text x="310" y="425" text-anchor="middle" fill="#ef4444" font-size="12" font-weight="600">QRS DETECTION</text>
            <text x="310" y="440" text-anchor="middle" fill="#94a3b8" font-size="10">Pan-Tompkins</text>
            <text x="310" y="453" text-anchor="middle" fill="#94a3b8" font-size="10">Algorithm</text>

            <rect x="400" y="400" width="120" height="60" rx="8"
                  fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
            <text x="460" y="425" text-anchor="middle" fill="#10b981" font-size="12" font-weight="600">RHYTHM ANALYSIS</text>
            <text x="460" y="440" text-anchor="middle" fill="#94a3b8" font-size="10">Pattern</text>
            <text x="460" y="453" text-anchor="middle" fill="#94a3b8" font-size="10">Recognition</text>

            <rect x="550" y="400" width="120" height="60" rx="8"
                  fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="610" y="425" text-anchor="middle" fill="#8b5cf6" font-size="12" font-weight="600">ST ANALYSIS</text>
            <text x="610" y="440" text-anchor="middle" fill="#94a3b8" font-size="10">Ischemia</text>
            <text x="610" y="453" text-anchor="middle" fill="#94a3b8" font-size="10">Detection</text>

            <rect x="700" y="400" width="120" height="60" rx="8"
                  fill="rgba(6, 182, 212, 0.2)" stroke="#06b6d4" stroke-width="2"/>
            <text x="760" y="425" text-anchor="middle" fill="#06b6d4" font-size="12" font-weight="600">ARRHYTHMIA</text>
            <text x="760" y="440" text-anchor="middle" fill="#94a3b8" font-size="10">Classification</text>
            <text x="760" y="453" text-anchor="middle" fill="#94a3b8" font-size="10">15+ Types</text>

            <rect x="850" y="400" width="120" height="60" rx="8"
                  fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
            <text x="910" y="425" text-anchor="middle" fill="#f59e0b" font-size="12" font-weight="600">INTERPRETATION</text>
            <text x="910" y="440" text-anchor="middle" fill="#94a3b8" font-size="10">Automated</text>
            <text x="910" y="453" text-anchor="middle" fill="#94a3b8" font-size="10">Diagnosis</text>
        </g>

        <!-- Signal flow arrows -->
        <line x1="200" y1="200" x2="250" y2="200" stroke="#3b82f6" stroke-width="4"
              marker-end="url(#ecgFlowArrow)" class="flow-arrow"/>
        <line x1="400" y1="200" x2="450" y2="200" stroke="#10b981" stroke-width="4"
              marker-end="url(#ecgFlowArrow)" class="flow-arrow"/>
        <line x1="600" y1="200" x2="650" y2="200" stroke="#8b5cf6" stroke-width="4"
              marker-end="url(#ecgFlowArrow)" class="flow-arrow"/>
        <line x1="800" y1="200" x2="850" y2="200" stroke="#06b6d4" stroke-width="4"
              marker-end="url(#ecgFlowArrow)" class="flow-arrow"/>

        <!-- Processing connections -->
        <line x1="925" y1="250" x2="600" y2="350" stroke="#ef4444" stroke-width="3"
              marker-end="url(#ecgFlowArrow)" class="flow-arrow"/>

        <!-- Title -->
        <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
            ECG Signal Processing Flow - Real-time Analysis Pipeline
        </text>
        <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
            Raw Signal → Conditioning → Digitization → Analysis → Interpretation
        </text>
    `;
}

// Load Mechanical Ventilation Diagrams
function loadMechanicalVentilationDiagrams() {
    loadMechanicalVentilationBlock();
    loadMechanicalVentilationSchematic();
    loadMechanicalVentilationSignalFlow();
}

// Load Mechanical Ventilation Block Diagram
function loadMechanicalVentilationBlock() {
    // Block diagram is already loaded in HTML
    console.log('Mechanical Ventilation Block Diagram loaded');

    // Add interactive behaviors specific to ventilation systems
    const ventSections = document.querySelectorAll('.vent-section');
    ventSections.forEach(section => {
        section.addEventListener('mouseenter', highlightVentSection);
        section.addEventListener('mouseleave', unhighlightVentSection);
    });
}

// Highlight Ventilation Section
function highlightVentSection(event) {
    const section = event.currentTarget;
    section.style.filter = 'brightness(1.2)';
    section.style.transform = 'scale(1.02)';
}

// Unhighlight Ventilation Section
function unhighlightVentSection(event) {
    const section = event.currentTarget;
    section.style.filter = '';
    section.style.transform = '';
}

// Load Mechanical Ventilation Schematic
function loadMechanicalVentilationSchematic() {
    const svg = document.getElementById('mechanicalVentilationSchematic');
    if (!svg) return;

    // Clear existing content
    svg.innerHTML = '';

    // Add mechanical ventilation pneumatic schematic
    svg.innerHTML = `
        <defs>
            <marker id="ventSchematicArrow" markerWidth="10" markerHeight="7"
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
            </marker>
        </defs>

        <!-- Gas Supply Section -->
        <g class="gas-supply-schematic">
            <rect x="50" y="100" width="300" height="200" rx="10"
                  fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" stroke-width="2"/>
            <text x="200" y="130" text-anchor="middle" fill="#10b981" font-size="16" font-weight="700">GAS SUPPLY SYSTEM</text>

            <!-- Oxygen Supply -->
            <circle cx="100" cy="170" r="15" fill="#3b82f6" stroke="#10b981" stroke-width="2"/>
            <text x="100" y="175" text-anchor="middle" fill="#fff" font-size="10" font-weight="600">O₂</text>
            <text x="100" y="200" text-anchor="middle" fill="#10b981" font-size="10">50 PSI</text>

            <!-- Air Supply -->
            <circle cx="200" cy="170" r="15" fill="#64748b" stroke="#10b981" stroke-width="2"/>
            <text x="200" y="175" text-anchor="middle" fill="#fff" font-size="10" font-weight="600">AIR</text>
            <text x="200" y="200" text-anchor="middle" fill="#10b981" font-size="10">50 PSI</text>

            <!-- Blender -->
            <rect x="270" y="155" width="60" height="30" rx="5"
                  fill="rgba(16, 185, 129, 0.3)" stroke="#10b981" stroke-width="2"/>
            <text x="300" y="173" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">BLENDER</text>

            <!-- Flow lines -->
            <line x1="115" y1="170" x2="270" y2="170" stroke="#10b981" stroke-width="3"
                  marker-end="url(#ventSchematicArrow)"/>
            <line x1="215" y1="170" x2="270" y2="170" stroke="#10b981" stroke-width="3"
                  marker-end="url(#ventSchematicArrow)"/>

            <!-- Pressure regulators -->
            <rect x="80" y="220" width="40" height="20" fill="none" stroke="#10b981" stroke-width="2"/>
            <rect x="180" y="220" width="40" height="20" fill="none" stroke="#10b981" stroke-width="2"/>
            <text x="100" y="255" text-anchor="middle" fill="#94a3b8" font-size="8">Regulator</text>
            <text x="200" y="255" text-anchor="middle" fill="#94a3b8" font-size="8">Regulator</text>
        </g>

        <!-- Ventilator Control Section -->
        <g class="ventilator-control-schematic">
            <rect x="400" y="100" width="350" height="200" rx="10"
                  fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" stroke-width="2"/>
            <text x="575" y="130" text-anchor="middle" fill="#3b82f6" font-size="16" font-weight="700">VENTILATOR CONTROL</text>

            <!-- Inspiratory Valve -->
            <rect x="430" y="160" width="60" height="40" rx="5"
                  fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
            <text x="460" y="178" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">INSP</text>
            <text x="460" y="190" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">VALVE</text>

            <!-- Expiratory Valve -->
            <rect x="430" y="220" width="60" height="40" rx="5"
                  fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
            <text x="460" y="238" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">EXP</text>
            <text x="460" y="250" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">VALVE</text>

            <!-- Flow Sensor -->
            <circle cx="550" cy="180" r="20" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
            <text x="550" y="185" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">FLOW</text>

            <!-- Pressure Sensor -->
            <circle cx="650" cy="180" r="20" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
            <text x="650" y="185" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PRESS</text>

            <!-- PEEP Valve -->
            <rect x="680" y="220" width="50" height="40" rx="5"
                  fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
            <text x="705" y="238" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">PEEP</text>
            <text x="705" y="250" text-anchor="middle" fill="#e2e8f0" font-size="9" font-weight="600">VALVE</text>

            <!-- Control connections -->
            <line x1="350" y1="170" x2="430" y2="180" stroke="#10b981" stroke-width="4"
                  marker-end="url(#ventSchematicArrow)"/>
        </g>

        <!-- Patient Circuit Section -->
        <g class="patient-circuit-schematic">
            <rect x="800" y="100" width="300" height="200" rx="10"
                  fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="950" y="130" text-anchor="middle" fill="#8b5cf6" font-size="16" font-weight="700">PATIENT CIRCUIT</text>

            <!-- Humidifier -->
            <rect x="830" y="160" width="60" height="40" rx="5"
                  fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="860" y="178" text-anchor="middle" fill="#e2e8f0" font-size="10" font-weight="600">HME</text>
            <text x="860" y="190" text-anchor="middle" fill="#94a3b8" font-size="8">37°C</text>

            <!-- Patient -->
            <ellipse cx="950" cy="180" rx="40" ry="30" fill="rgba(139, 92, 246, 0.2)"
                     stroke="#8b5cf6" stroke-width="2"/>
            <text x="950" y="185" text-anchor="middle" fill="#8b5cf6" font-size="12" font-weight="600">PATIENT</text>

            <!-- Y-Piece -->
            <circle cx="1020" cy="180" r="10" fill="rgba(139, 92, 246, 0.3)" stroke="#8b5cf6" stroke-width="2"/>
            <text x="1020" y="205" text-anchor="middle" fill="#94a3b8" font-size="8">Y-Piece</text>

            <!-- Circuit connections -->
            <line x1="750" y1="180" x2="830" y2="180" stroke="#8b5cf6" stroke-width="4"
                  marker-end="url(#ventSchematicArrow)"/>
            <line x1="890" y1="180" x2="910" y2="180" stroke="#8b5cf6" stroke-width="4"
                  marker-end="url(#ventSchematicArrow)"/>
            <line x1="990" y1="180" x2="1010" y2="180" stroke="#8b5cf6" stroke-width="4"
                  marker-end="url(#ventSchematicArrow)"/>
        </g>

        <!-- Breathing Cycle Diagram -->
        <g class="breathing-cycle">
            <rect x="200" y="400" width="800" height="150" rx="10"
                  fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="2"/>
            <text x="600" y="430" text-anchor="middle" fill="#06b6d4" font-size="16" font-weight="700">BREATHING CYCLE PHASES</text>

            <!-- Inspiration Phase -->
            <rect x="250" y="450" width="150" height="60" rx="8"
                  fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
            <text x="325" y="475" text-anchor="middle" fill="#10b981" font-size="14" font-weight="600">INSPIRATION</text>
            <text x="270" y="490" fill="#e2e8f0" font-size="10">• Inspiratory valve opens</text>
            <text x="270" y="502" fill="#e2e8f0" font-size="10">• Gas flows to patient</text>

            <!-- Expiration Phase -->
            <rect x="450" y="450" width="150" height="60" rx="8"
                  fill="rgba(239, 68, 68, 0.2)" stroke="#ef4444" stroke-width="2"/>
            <text x="525" y="475" text-anchor="middle" fill="#ef4444" font-size="14" font-weight="600">EXPIRATION</text>
            <text x="470" y="490" fill="#e2e8f0" font-size="10">• Expiratory valve opens</text>
            <text x="470" y="502" fill="#e2e8f0" font-size="10">• Gas flows from patient</text>

            <!-- PEEP Phase -->
            <rect x="650" y="450" width="150" height="60" rx="8"
                  fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
            <text x="725" y="475" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="600">PEEP</text>
            <text x="670" y="490" fill="#e2e8f0" font-size="10">• Maintains baseline</text>
            <text x="670" y="502" fill="#e2e8f0" font-size="10">• Prevents collapse</text>

            <!-- Monitoring -->
            <rect x="850" y="450" width="120" height="60" rx="8"
                  fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
            <text x="910" y="475" text-anchor="middle" fill="#f59e0b" font-size="14" font-weight="600">MONITORING</text>
            <text x="870" y="490" fill="#e2e8f0" font-size="10">• Pressure/Flow</text>
            <text x="870" y="502" fill="#e2e8f0" font-size="10">• Volume/Gas</text>
        </g>

        <!-- Title -->
        <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
            Mechanical Ventilation - Pneumatic Control Circuit
        </text>
        <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
            Gas Supply → Control Valves → Patient Circuit → Monitoring
        </text>
    `;
}

// Load Mechanical Ventilation Signal Flow
function loadMechanicalVentilationSignalFlow() {
    const svg = document.getElementById('mechanicalVentilationSignalFlow');
    if (!svg) return;

    // Clear existing content
    svg.innerHTML = '';

    // Add mechanical ventilation signal flow content
    svg.innerHTML = `
        <defs>
            <marker id="ventFlowArrow" markerWidth="10" markerHeight="7"
                    refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
            </marker>
        </defs>

        <!-- Respiratory Mechanics Flow -->
        <g class="respiratory-mechanics">
            <rect x="100" y="100" width="1000" height="250" rx="15"
                  fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" stroke-width="2"/>
            <text x="600" y="130" text-anchor="middle" fill="#10b981" font-size="18" font-weight="700">
                RESPIRATORY MECHANICS & VENTILATION PARAMETERS
            </text>

            <!-- Pressure Waveform -->
            <g class="pressure-waveform">
                <rect x="150" y="160" width="200" height="80" rx="8"
                      fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
                <text x="250" y="185" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="600">PRESSURE</text>

                <!-- Simulated pressure waveform -->
                <path d="M 170 220 L 180 220 L 185 180 L 210 180 L 215 220 L 330 220"
                      stroke="#3b82f6" stroke-width="3" fill="none"/>

                <text x="250" y="255" text-anchor="middle" fill="#94a3b8" font-size="10">
                    Peak: 25 cmH₂O, PEEP: 5 cmH₂O
                </text>
            </g>

            <!-- Flow Waveform -->
            <g class="flow-waveform">
                <rect x="400" y="160" width="200" height="80" rx="8"
                      fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
                <text x="500" y="185" text-anchor="middle" fill="#10b981" font-size="14" font-weight="600">FLOW</text>

                <!-- Simulated flow waveform -->
                <path d="M 420 200 L 430 180 L 460 180 L 470 200 L 480 220 L 510 220 L 520 200 L 580 200"
                      stroke="#10b981" stroke-width="3" fill="none"/>

                <text x="500" y="255" text-anchor="middle" fill="#94a3b8" font-size="10">
                    Peak: 60 L/min, I:E = 1:2
                </text>
            </g>

            <!-- Volume Waveform -->
            <g class="volume-waveform">
                <rect x="650" y="160" width="200" height="80" rx="8"
                      fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
                <text x="750" y="185" text-anchor="middle" fill="#8b5cf6" font-size="14" font-weight="600">VOLUME</text>

                <!-- Simulated volume waveform -->
                <path d="M 670 220 Q 690 180 720 180 Q 750 180 770 180 Q 790 180 810 220"
                      stroke="#8b5cf6" stroke-width="3" fill="none"/>

                <text x="750" y="255" text-anchor="middle" fill="#94a3b8" font-size="10">
                    Tidal Volume: 500 mL
                </text>
            </g>

            <!-- Compliance Calculation -->
            <g class="compliance-calc">
                <rect x="900" y="160" width="150" height="80" rx="8"
                      fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
                <text x="975" y="185" text-anchor="middle" fill="#f59e0b" font-size="14" font-weight="600">COMPLIANCE</text>

                <text x="975" y="205" text-anchor="middle" fill="#e2e8f0" font-size="12" font-weight="600">
                    C = ΔV / ΔP
                </text>
                <text x="975" y="220" text-anchor="middle" fill="#e2e8f0" font-size="11">
                    25 mL/cmH₂O
                </text>

                <text x="975" y="255" text-anchor="middle" fill="#94a3b8" font-size="10">
                    Static Compliance
                </text>
            </g>

            <!-- Parameter arrows -->
            <line x1="350" y1="200" x2="400" y2="200" stroke="#10b981" stroke-width="3"
                  marker-end="url(#ventFlowArrow)"/>
            <line x1="600" y1="200" x2="650" y2="200" stroke="#10b981" stroke-width="3"
                  marker-end="url(#ventFlowArrow)"/>
            <line x1="850" y1="200" x2="900" y2="200" stroke="#10b981" stroke-width="3"
                  marker-end="url(#ventFlowArrow)"/>
        </g>

        <!-- Ventilation Modes Analysis -->
        <g class="ventilation-modes-analysis">
            <rect x="100" y="400" width="1000" height="150" rx="15"
                  fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" stroke-width="2"/>
            <text x="600" y="430" text-anchor="middle" fill="#3b82f6" font-size="18" font-weight="700">
                VENTILATION MODE CHARACTERISTICS
            </text>

            <!-- Volume Control -->
            <g class="vcv-analysis">
                <rect x="150" y="450" width="180" height="70" rx="8"
                      fill="rgba(16, 185, 129, 0.2)" stroke="#10b981" stroke-width="2"/>
                <text x="240" y="475" text-anchor="middle" fill="#10b981" font-size="14" font-weight="600">VOLUME CONTROL</text>
                <text x="170" y="490" fill="#e2e8f0" font-size="10">• Constant tidal volume</text>
                <text x="170" y="502" fill="#e2e8f0" font-size="10">• Variable pressure</text>
                <text x="170" y="514" fill="#e2e8f0" font-size="10">• Square flow pattern</text>
            </g>

            <!-- Pressure Control -->
            <g class="pcv-analysis">
                <rect x="370" y="450" width="180" height="70" rx="8"
                      fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" stroke-width="2"/>
                <text x="460" y="475" text-anchor="middle" fill="#3b82f6" font-size="14" font-weight="600">PRESSURE CONTROL</text>
                <text x="390" y="490" fill="#e2e8f0" font-size="10">• Constant pressure</text>
                <text x="390" y="502" fill="#e2e8f0" font-size="10">• Variable volume</text>
                <text x="390" y="514" fill="#e2e8f0" font-size="10">• Decelerating flow</text>
            </g>

            <!-- SIMV -->
            <g class="simv-analysis">
                <rect x="590" y="450" width="180" height="70" rx="8"
                      fill="rgba(139, 92, 246, 0.2)" stroke="#8b5cf6" stroke-width="2"/>
                <text x="680" y="475" text-anchor="middle" fill="#8b5cf6" font-size="14" font-weight="600">SIMV</text>
                <text x="610" y="490" fill="#e2e8f0" font-size="10">• Synchronized breaths</text>
                <text x="610" y="502" fill="#e2e8f0" font-size="10">• Spontaneous support</text>
                <text x="610" y="514" fill="#e2e8f0" font-size="10">• Weaning mode</text>
            </g>

            <!-- PSV -->
            <g class="psv-analysis">
                <rect x="810" y="450" width="180" height="70" rx="8"
                      fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" stroke-width="2"/>
                <text x="900" y="475" text-anchor="middle" fill="#f59e0b" font-size="14" font-weight="600">PSV</text>
                <text x="830" y="490" fill="#e2e8f0" font-size="10">• Patient triggered</text>
                <text x="830" y="502" fill="#e2e8f0" font-size="10">• Pressure support</text>
                <text x="830" y="514" fill="#e2e8f0" font-size="10">• Spontaneous breathing</text>
            </g>
        </g>

        <!-- Real-time Monitoring -->
        <g class="realtime-monitoring">
            <rect x="200" y="600" width="800" height="100" rx="10"
                  fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" stroke-width="2"/>
            <text x="600" y="630" text-anchor="middle" fill="#ef4444" font-size="16" font-weight="700">
                REAL-TIME MONITORING & ALARMS
            </text>

            <!-- Alarm conditions -->
            <text x="250" y="655" fill="#e2e8f0" font-size="12" font-weight="600">High Pressure: >40 cmH₂O</text>
            <text x="250" y="670" fill="#e2e8f0" font-size="12" font-weight="600">Low Volume: <300 mL</text>
            <text x="250" y="685" fill="#e2e8f0" font-size="12" font-weight="600">Apnea: >20 seconds</text>

            <text x="500" y="655" fill="#e2e8f0" font-size="12" font-weight="600">Disconnect: Flow <10%</text>
            <text x="500" y="670" fill="#e2e8f0" font-size="12" font-weight="600">High Rate: >35 bpm</text>
            <text x="500" y="685" fill="#e2e8f0" font-size="12" font-weight="600">Power Failure: Battery</text>

            <text x="750" y="655" fill="#e2e8f0" font-size="12" font-weight="600">FiO₂: ±5% of set</text>
            <text x="750" y="670" fill="#e2e8f0" font-size="12" font-weight="600">PEEP: ±2 cmH₂O</text>
            <text x="750" y="685" fill="#e2e8f0" font-size="12" font-weight="600">Temperature: 37°C ±2°C</text>
        </g>

        <!-- Title -->
        <text x="600" y="40" text-anchor="middle" fill="#e2e8f0" font-size="20" font-weight="700">
            Mechanical Ventilation - Respiratory Mechanics & Real-time Analysis
        </text>
        <text x="600" y="60" text-anchor="middle" fill="#94a3b8" font-size="14">
            Pressure/Flow/Volume Waveforms → Mode Analysis → Monitoring & Alarms
        </text>
    `;
}

// Export functions for global access
window.initializeInteractiveDiagrams = initializeInteractiveDiagrams;
window.showCategory = showCategory;
window.showDiagramType = showDiagramType;
window.zoomIn = zoomIn;
window.zoomOut = zoomOut;
window.resetZoom = resetZoom;
window.toggleAnimation = toggleAnimation;
window.exportDiagram = exportDiagram;
window.closeComponentInfo = closeComponentInfo;
