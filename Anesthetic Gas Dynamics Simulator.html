<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthetic Gas Dynamics Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f0f2f5; /* Softer background */
            color: #333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .app-container {
            width: 95%;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-radius: 8px;
            flex-grow: 1;
        }

        .header, .footer {
            text-align: center;
            padding: 10px 0;
        }
        .header h1 {
            margin-bottom: 10px;
            color: #1a2533; /* Darker blue-grey */
            font-size: 1.8em;
        }
        .footer {
            margin-top: 20px;
            font-size: 0.9em;
            color: #555;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 25px; /* Increased gap */
        }

        .diagram-section, .controls-section, .outputs-section {
            padding: 20px; /* Increased padding */
            border: 1px solid #d9dee3; /* Lighter border */
            border-radius: 6px;
            background-color: #fcfdff; /* Very light, almost white */
        }

        h2 {
            margin-top: 0;
            margin-bottom: 15px; /* Space below h2 */
            color: #007bff; /* Standard blue */
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
            font-size: 1.4em;
        }

        /* Diagram Styles */
        .circuit-diagram-schematic {
            display: flex;
            flex-direction: column;
            align-items: center;
            border: 2px solid #ced4da; 
            padding: 15px;
            background-color: #e9ecef; /* Light grey */
            border-radius: 8px;
            margin-top: 10px;
        }
        .diagram-component {
            border: 1px solid #adb5bd; 
            padding: 10px 15px; /* Increased padding */
            margin: 8px 0;
            text-align: center;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            width: 90%;
            box-sizing: border-box;
        }
        .system-volume-box {
            background-color: #e0f0ff; /* Lighter blue */
        }
        .patient-lungs-box {
            background-color: #fff0e0; /* Lighter peach */
            margin-top: 10px;
            padding: 10px;
        }
        .fgf-inlet-box {
            background-color: #e0ffe0; /* Lighter green */
        }
        .co2-absorber-box {
            font-style: italic;
            background-color: #f8f9fa; /* Very light grey */
            font-size: 0.9em;
            padding: 8px 12px;
        }
        .arrow { font-size: 1.3em; color: #28a745; /* Bootstrap green */}

        /* Controls Styles */
        .controls-section div {
            margin-bottom: 18px; /* Increased margin */
        }
        .controls-section label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600; /* Slightly bolder */
            color: #495057; /* Grey text */
        }
        .controls-section input[type="range"] {
            width: 100%;
            cursor: pointer;
            accent-color: #007bff; /* Slider color */
        }
        .controls-section button {
            background-color: #007bff; /* Blue */
            color: white;
            border: none;
            padding: 10px 18px; /* Adjusted padding */
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1em;
            font-weight: 500;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            transition: background-color 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls-section button:hover:not(:disabled) {
            background-color: #0056b3; /* Darker blue */
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .controls-section button:disabled {
            background-color: #adb5bd; /* Disabled grey */
            cursor: not-allowed;
            box-shadow: none;
        }
        /* Slider value display */
        #fgfValueDisplay, #targetConcValueDisplay {
            font-weight: 500;
            color: #007bff; /* Blue to match theme */
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }

        /* Outputs Styles */
        .outputs-section p {
            margin: 10px 0; /* Adjusted margin */
            font-size: 1.05em;
        }
        .outputs-section p span {
            font-weight: bold;
            color: #dc3545; /* Bootstrap red */
        }
        #tcDisplay { color: #6f42c1; } /* Bootstrap purple for TC */
        
        .concentration-visualizer-container {
            width: 70px; /* Slightly wider */
            height: 220px; /* Slightly taller */
            border: 1px solid #ced4da;
            position: relative;
            margin-top: 15px;
            background-color: #e9ecef; /* Light grey background for bar */
            border-radius: 4px;
            overflow: hidden;
        }
        .concentration-bar {
            width: 100%;
            background-color: #28a745; /* Bootstrap green */
            position: absolute;
            bottom: 0;
            left: 0;
            height: 0%;
            transition: height 0.1s linear;
        }
        .concentration-bar-label {
            position: absolute;
            top: 50%; 
            left: 50%; 
            transform: translate(-50%, -50%);
            color: #343a40; /* Dark grey text */
            font-size: 0.9em;
            font-weight: bold;
            text-shadow: 0 0 2px #fff, 0 0 2px #fff; /* Make text readable on bar */
        }

        /* Layout for controls and outputs row */
        .row {
            display: flex;
            flex-wrap: wrap; 
            gap: 25px; 
        }
        .column {
            flex: 1; 
            min-width: 300px; /* Min width before stacking */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .app-container {
                width: 100%;
                margin: 0;
                padding: 15px; /* Adjusted padding for mobile */
                box-shadow: none;
                border-radius: 0;
            }
            .row {
                flex-direction: column; 
            }
            .header h1 {
                font-size: 1.6em;
            }
            .controls-section button {
                padding: 9px 15px;
                font-size: 0.95em;
                width: calc(50% - 5px); /* Two buttons per row */
                box-sizing: border-box;
            }
             .controls-section button:last-of-type {
                margin-right: 0;
            }
        }
         @media (max-width: 480px) {
            .controls-section button {
                width: 100%; /* Stack buttons on very small screens */
                margin-right: 0;
                margin-bottom: 10px;
            }
             .controls-section button:last-of-type {
                margin-bottom: 0;
            }
            .diagram-component {
                padding: 8px 10px;
                font-size: 0.9em;
            }
             h2 { font-size: 1.3em; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>Anesthetic Gas Dynamics Simulator</h1>
        </div>

        <div class="main-content">
            <div class="diagram-section">
                <h2>Breathing Circuit Diagram</h2>
                <div class="circuit-diagram-schematic">
                    <div class="diagram-component fgf-inlet-box">
                        Fresh Gas Flow (FGF): <span id="fgfDiagram">1.00 L/min</span> <span class="arrow">&rarr;</span>
                    </div>
                    <div class="diagram-component system-volume-box">
                        Breathing System / Circuit Volume (VS): <span id="vsDisplay">6.0 L</span>
                        <div class="diagram-component patient-lungs-box">
                            Patient Lungs Anesthetic Conc: <span id="lungConcDiagram">0.000%</span>
                        </div>
                    </div>
                    <div class="diagram-component co2-absorber-box">
                        (CO<sub>2</sub> Absorber assumed in rebreathing path)
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="column controls-section">
                    <h2>Controls</h2>
                    <div>
                        <label for="fgfSlider">Fresh Gas Flow (FGF): <span id="fgfValueDisplay">1.00</span> L/min</label>
                        <input type="range" id="fgfSlider" min="0.25" max="8" step="0.05" value="1">
                    </div>
                    <div>
                        <label for="targetConcSlider">Target Anesthetic Conc.: <span id="targetConcValueDisplay">2.0</span> %</label>
                        <input type="range" id="targetConcSlider" min="0.1" max="5" step="0.1" value="2">
                    </div>
                    <button id="startButton">Start</button>
                    <button id="resetButton">Reset</button>
                </div>

                <div class="column outputs-section">
                    <h2>Outputs</h2>
                    <p>System Volume (VS): <span id="vsOutputDisplay">6.0 L</span></p>
                    <p>Time Constant (TC): <span id="tcDisplay">- min</span></p>
                    <p>Elapsed Time: <span id="elapsedTimeDisplay">0.00 s</span></p>
                    <p>Current Lung Concentration: <span id="currentConcDisplay">0.000 %</span></p>
                    <div class="concentration-visualizer-container">
                        <div id="concentrationBar" class="concentration-bar"></div>
                        <span id="concentrationBarLabel" class="concentration-bar-label">0.0%</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>This simulator helps understand how Fresh Gas Flow (FGF) and System Volume (VS) influence the Time Constant (TC), and consequently, the rate of change in anesthetic concentration.</p>
        </div>
    </div>

    <script>
        // Constants
        const VS_LITERS = 6.0;
        const SIMULATION_INTERVAL_MS = 100; // Update 10 times per second

        // State variables
        let fgfLitersPerMin;
        let targetConcentrationPercent;
        let timeConstantMinutes;
        let currentConcentrationPercent = 0;
        let elapsedTimeSeconds = 0;
        let simulationIntervalId = null;
        let isRunning = false;
        let lastFrameTime = 0;

        // DOM Elements
        const fgfSlider = document.getElementById('fgfSlider');
        const targetConcSlider = document.getElementById('targetConcSlider');
        const startButton = document.getElementById('startButton');
        const resetButton = document.getElementById('resetButton');

        const fgfValueDisplay = document.getElementById('fgfValueDisplay');
        const targetConcValueDisplay = document.getElementById('targetConcValueDisplay');
        const vsDisplay = document.getElementById('vsDisplay');
        const vsOutputDisplay = document.getElementById('vsOutputDisplay');
        const fgfDiagramDisplay = document.getElementById('fgfDiagram');
        const lungConcDiagramDisplay = document.getElementById('lungConcDiagram');
        
        const tcDisplay = document.getElementById('tcDisplay');
        const elapsedTimeDisplay = document.getElementById('elapsedTimeDisplay');
        const currentConcDisplay = document.getElementById('currentConcDisplay');
        const concentrationBar = document.getElementById('concentrationBar');
        const concentrationBarLabel = document.getElementById('concentrationBarLabel');

        function init() {
            vsDisplay.textContent = `${VS_LITERS.toFixed(1)} L`;
            vsOutputDisplay.textContent = `${VS_LITERS.toFixed(1)} L`;
            
            addEventListeners();
            resetSimulation(); 
        }

        function addEventListeners() {
            fgfSlider.addEventListener('input', (event) => {
                updateFGF(parseFloat(event.target.value));
            });
            targetConcSlider.addEventListener('input', (event) => {
                updateTargetConcentration(parseFloat(event.target.value));
            });
            startButton.addEventListener('click', handleStartButtonClick);
            resetButton.addEventListener('click', resetSimulation);
        }

        function updateFGF(value) {
            fgfLitersPerMin = value;
            calculateAndDisplayTC();
            // No need to call updateUIDisplays here, calculateAndDisplayTC calls it
        }

        function updateTargetConcentration(value) {
            targetConcentrationPercent = value;
            updateUIDisplays();
        }

        function calculateAndDisplayTC() {
            if (fgfLitersPerMin > 0) {
                timeConstantMinutes = VS_LITERS / fgfLitersPerMin;
            } else {
                timeConstantMinutes = Infinity;
            }
            updateUIDisplays();
        }

        function handleStartButtonClick() {
            if (!isRunning) {
                startSimulation();
            }
        }
        
        function startSimulation() {
            if (isRunning) return;
            isRunning = true;
            lastFrameTime = Date.now();
            
            if (elapsedTimeSeconds === 0) { // Only reset concentration if it's a true fresh start
                currentConcentrationPercent = 0;
            }

            simulationIntervalId = setInterval(simulationStep, SIMULATION_INTERVAL_MS);
            updateButtonStates();
        }

        function stopSimulation() {
            // No direct stop button, but used by reset
            if (!isRunning && !simulationIntervalId) return; // Avoid clearing if already stopped
            isRunning = false;
            clearInterval(simulationIntervalId);
            simulationIntervalId = null;
            updateButtonStates();
        }

        function resetSimulation() {
            stopSimulation();
            elapsedTimeSeconds = 0;
            currentConcentrationPercent = 0;
            
            // Sync internal state with current slider values
            fgfLitersPerMin = parseFloat(fgfSlider.value); 
            targetConcentrationPercent = parseFloat(targetConcSlider.value);
            
            calculateAndDisplayTC(); // This will also call updateUIDisplays
            updateButtonStates(); // Ensure buttons are correct after reset
        }

        function simulationStep() {
            let now = Date.now();
            let deltaTimeSeconds = (now - lastFrameTime) / 1000;
            lastFrameTime = now;

            elapsedTimeSeconds += deltaTimeSeconds;
            let deltaTimeMinutes = deltaTimeSeconds / 60;

            if (timeConstantMinutes > 0 && isFinite(timeConstantMinutes)) {
                let concentrationDifference = targetConcentrationPercent - currentConcentrationPercent;
                // For wash-in or wash-out, the change is proportional to the difference
                let changeInConcentration = (concentrationDifference * deltaTimeMinutes) / timeConstantMinutes;
                currentConcentrationPercent += changeInConcentration;

                // Clamp to target to prevent overshooting due to discrete steps,
                // especially if deltaTimeMinutes / timeConstantMinutes is large.
                if ((concentrationDifference > 0 && currentConcentrationPercent > targetConcentrationPercent) ||
                    (concentrationDifference < 0 && currentConcentrationPercent < targetConcentrationPercent)) {
                    currentConcentrationPercent = targetConcentrationPercent;
                }
            }
            
             if (currentConcentrationPercent < 0) { // Safety, should not happen with proper logic
                currentConcentrationPercent = 0;
             }


            updateUIDisplays();
        }

        function updateUIDisplays() {
            fgfValueDisplay.textContent = fgfLitersPerMin.toFixed(2);
            targetConcValueDisplay.textContent = targetConcentrationPercent.toFixed(1);

            fgfDiagramDisplay.textContent = `${fgfLitersPerMin.toFixed(2)} L/min`;
            lungConcDiagramDisplay.textContent = `${currentConcentrationPercent.toFixed(3)}%`;

            if (isFinite(timeConstantMinutes)) {
                tcDisplay.textContent = `${timeConstantMinutes.toFixed(2)} min`;
            } else {
                tcDisplay.textContent = `\u221E min`; // Infinity symbol
            }
            elapsedTimeDisplay.textContent = `${elapsedTimeSeconds.toFixed(2)} s`;
            currentConcDisplay.textContent = `${currentConcentrationPercent.toFixed(3)} %`;

            const maxTargetConcOnSlider = parseFloat(targetConcSlider.max);
            let barHeightPercent = (currentConcentrationPercent / maxTargetConcOnSlider) * 100;
            barHeightPercent = Math.max(0, Math.min(100, barHeightPercent)); 

            concentrationBar.style.height = `${barHeightPercent}%`;
            concentrationBarLabel.textContent = `${currentConcentrationPercent.toFixed(1)}%`;
        }
        
        function updateButtonStates() {
            startButton.disabled = isRunning;
            // Reset button is always enabled, or could be disabled if in pristine state:
            // resetButton.disabled = !isRunning && elapsedTimeSeconds === 0 && currentConcentrationPercent === 0;
            // For simplicity, let's keep it always enabled.
        }

        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
