<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Monitoring Module - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/animations.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="index.html" class="nav-link" onclick="window.location.href='index.html'; return false;">
                    <i class="fas fa-home"></i>
                    العودة للصفحة الرئيسية
                </a>
                <div class="module-title">
                    <h1>Patient Monitoring Module</h1>
                    <p>Comprehensive Vital Signs & Monitoring Systems</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Module Overview -->
    <section class="module-overview">
        <div class="overview-content">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-heartbeat animated-heartbeat"></i>
                    </div>
                    <h3>Vital Signs Monitoring</h3>
                    <p>Master the five essential vital signs with real-time monitoring simulations</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <span class="progress-text">85% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-chart-line animated-pulse"></i>
                    </div>
                    <h3>ECG Interpretation</h3>
                    <p>Advanced electrocardiogram analysis with interactive waveform tools</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 70%"></div>
                    </div>
                    <span class="progress-text">70% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-tachometer-alt animated-gauge"></i>
                    </div>
                    <h3>Blood Pressure</h3>
                    <p>Comprehensive hemodynamics and pressure measurement techniques</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 60%"></div>
                    </div>
                    <span class="progress-text">60% Complete</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Learning Tools -->
    <section class="learning-tools">
        <div class="tools-container">
            <h2 class="section-title">Interactive Learning Tools</h2>
            
            <!-- Virtual Patient Monitor -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Virtual Patient Monitor</h3>
                    <p>Real-time simulation of patient monitoring equipment</p>
                </div>
                
                <div class="virtual-monitor">
                    <div class="monitor-screen">
                        <div class="monitor-display">
                            <!-- ECG Waveform -->
                            <div class="ecg-section">
                                <div class="parameter-label">ECG - Lead II</div>
                                <div class="ecg-waveform">
                                    <canvas id="ecgCanvas" width="400" height="100"></canvas>
                                </div>
                                <div class="parameter-value">
                                    <span class="value">75</span>
                                    <span class="unit">bpm</span>
                                </div>
                            </div>
                            
                            <!-- Blood Pressure -->
                            <div class="bp-section">
                                <div class="parameter-label">Blood Pressure</div>
                                <div class="bp-display">
                                    <div class="bp-waveform">
                                        <canvas id="bpCanvas" width="400" height="80"></canvas>
                                    </div>
                                    <div class="bp-values">
                                        <span class="systolic">120</span>
                                        <span class="separator">/</span>
                                        <span class="diastolic">80</span>
                                        <span class="unit">mmHg</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- SpO2 -->
                            <div class="spo2-section">
                                <div class="parameter-label">SpO₂</div>
                                <div class="spo2-display">
                                    <div class="pulse-wave">
                                        <canvas id="spo2Canvas" width="400" height="60"></canvas>
                                    </div>
                                    <div class="spo2-value">
                                        <span class="value">98</span>
                                        <span class="unit">%</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Temperature -->
                            <div class="temp-section">
                                <div class="parameter-label">Temperature</div>
                                <div class="temp-display">
                                    <div class="thermometer">
                                        <div class="temp-scale"></div>
                                        <div class="temp-mercury"></div>
                                    </div>
                                    <div class="temp-value">
                                        <span class="value">36.8</span>
                                        <span class="unit">°C</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Respiratory Rate -->
                            <div class="resp-section">
                                <div class="parameter-label">Respiratory Rate</div>
                                <div class="resp-display">
                                    <div class="lung-animation">
                                        <div class="lung left-lung"></div>
                                        <div class="lung right-lung"></div>
                                    </div>
                                    <div class="resp-value">
                                        <span class="value">16</span>
                                        <span class="unit">/min</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Monitor Controls -->
                        <div class="monitor-controls">
                            <button class="control-btn active" onclick="toggleParameter('ecg')">
                                <i class="fas fa-heartbeat"></i>
                                ECG
                            </button>
                            <button class="control-btn active" onclick="toggleParameter('bp')">
                                <i class="fas fa-tachometer-alt"></i>
                                BP
                            </button>
                            <button class="control-btn active" onclick="toggleParameter('spo2')">
                                <i class="fas fa-lungs"></i>
                                SpO₂
                            </button>
                            <button class="control-btn active" onclick="toggleParameter('temp')">
                                <i class="fas fa-thermometer-half"></i>
                                Temp
                            </button>
                            <button class="control-btn active" onclick="toggleParameter('resp')">
                                <i class="fas fa-wind"></i>
                                Resp
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Interactive Diagrams -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Interactive System Diagrams</h3>
                    <p>Explore monitoring system components and connections</p>
                </div>
                
                <div class="diagram-container">
                    <div class="diagram-tabs">
                        <button class="tab-btn active" onclick="showDiagram('block')">Block Diagram</button>
                        <button class="tab-btn" onclick="showDiagram('schematic')">Circuit Schematic</button>
                        <button class="tab-btn" onclick="showDiagram('signal')">Signal Flow</button>
                    </div>
                    
                    <!-- Block Diagram -->
                    <div id="blockDiagram" class="diagram-panel active">
                        <svg class="interactive-diagram" viewBox="0 0 800 600">
                            <!-- Patient -->
                            <g class="diagram-component patient" onclick="showComponentInfo('patient')">
                                <rect x="50" y="250" width="100" height="80" rx="10" class="component-box"/>
                                <text x="100" y="285" class="component-label">Patient</text>
                                <text x="100" y="300" class="component-sublabel">Physiological</text>
                                <text x="100" y="315" class="component-sublabel">Signals</text>
                            </g>
                            
                            <!-- Sensors -->
                            <g class="diagram-component sensors" onclick="showComponentInfo('sensors')">
                                <rect x="200" y="150" width="120" height="60" rx="8" class="component-box"/>
                                <text x="260" y="175" class="component-label">ECG Electrodes</text>
                                <text x="260" y="190" class="component-sublabel">Lead II</text>
                            </g>
                            
                            <g class="diagram-component sensors" onclick="showComponentInfo('bp-cuff')">
                                <rect x="200" y="230" width="120" height="60" rx="8" class="component-box"/>
                                <text x="260" y="255" class="component-label">BP Cuff</text>
                                <text x="260" y="270" class="component-sublabel">Oscillometric</text>
                            </g>
                            
                            <g class="diagram-component sensors" onclick="showComponentInfo('pulse-ox')">
                                <rect x="200" y="310" width="120" height="60" rx="8" class="component-box"/>
                                <text x="260" y="335" class="component-label">Pulse Oximeter</text>
                                <text x="260" y="350" class="component-sublabel">SpO₂ Sensor</text>
                            </g>
                            
                            <!-- Signal Processing -->
                            <g class="diagram-component processing" onclick="showComponentInfo('processing')">
                                <rect x="370" y="220" width="140" height="100" rx="10" class="component-box"/>
                                <text x="440" y="245" class="component-label">Signal Processing</text>
                                <text x="440" y="260" class="component-sublabel">• Amplification</text>
                                <text x="440" y="275" class="component-sublabel">• Filtering</text>
                                <text x="440" y="290" class="component-sublabel">• A/D Conversion</text>
                                <text x="440" y="305" class="component-sublabel">• Algorithm Processing</text>
                            </g>
                            
                            <!-- Display -->
                            <g class="diagram-component display" onclick="showComponentInfo('display')">
                                <rect x="560" y="240" width="120" height="80" rx="10" class="component-box"/>
                                <text x="620" y="270" class="component-label">Monitor Display</text>
                                <text x="620" y="285" class="component-sublabel">Waveforms</text>
                                <text x="620" y="300" class="component-sublabel">Parameters</text>
                            </g>
                            
                            <!-- Connections -->
                            <line x1="150" y1="180" x2="200" y2="180" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="150" y1="260" x2="200" y2="260" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="150" y1="340" x2="200" y2="340" class="connection-line" marker-end="url(#arrowhead)"/>
                            
                            <line x1="320" y1="180" x2="370" y2="240" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="320" y1="260" x2="370" y2="270" class="connection-line" marker-end="url(#arrowhead)"/>
                            <line x1="320" y1="340" x2="370" y2="300" class="connection-line" marker-end="url(#arrowhead)"/>
                            
                            <line x1="510" y1="270" x2="560" y2="280" class="connection-line" marker-end="url(#arrowhead)"/>
                            
                            <!-- Arrow marker -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" class="arrow-fill"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>
                    
                    <!-- Component Information Panel -->
                    <div class="component-info" id="componentInfo">
                        <h4 id="componentTitle">Select a component to learn more</h4>
                        <p id="componentDescription">Click on any component in the diagram to see detailed information about its function and specifications.</p>
                        <div id="componentSpecs"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lecture Notes Section -->
    <section class="lecture-notes">
        <div class="notes-container">
            <h2 class="section-title">Comprehensive Lecture Notes</h2>
            
            <div class="notes-grid">
                <!-- Vital Signs Notes -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h3>Vital Signs Fundamentals</h3>
                    </div>
                    <div class="note-content">
                        <h4>Key Concepts</h4>
                        <ul>
                            <li>Five primary vital signs: HR, BP, RR, Temp, SpO₂</li>
                            <li>Age-specific normal ranges and variations</li>
                            <li>Physiological mechanisms and compensatory responses</li>
                            <li>Clinical significance and interpretation</li>
                        </ul>
                        
                        <h4>Normal Adult Ranges</h4>
                        <div class="reference-table">
                            <div class="table-row">
                                <span class="parameter">Heart Rate</span>
                                <span class="range">60-100 bpm</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Blood Pressure</span>
                                <span class="range">&lt;120/80 mmHg</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Respiratory Rate</span>
                                <span class="range">12-20 /min</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Temperature</span>
                                <span class="range">36.5-37.5°C</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">SpO₂</span>
                                <span class="range">95-100%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- ECG Notes -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>ECG Interpretation</h3>
                    </div>
                    <div class="note-content">
                        <h4>Waveform Components</h4>
                        <ul>
                            <li><strong>P Wave:</strong> Atrial depolarization</li>
                            <li><strong>QRS Complex:</strong> Ventricular depolarization</li>
                            <li><strong>T Wave:</strong> Ventricular repolarization</li>
                            <li><strong>PR Interval:</strong> 0.12-0.20 seconds</li>
                            <li><strong>QT Interval:</strong> Rate-dependent</li>
                        </ul>
                        
                        <h4>Systematic Analysis</h4>
                        <ol>
                            <li>Rate: 300/large squares method</li>
                            <li>Rhythm: Regular vs irregular</li>
                            <li>Axis: Normal -30° to +90°</li>
                            <li>Intervals: PR, QRS, QT measurements</li>
                            <li>Morphology: P, QRS, T wave analysis</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/modules.js"></script>
    <script src="../JS/patient-monitor.js"></script>
    <script src="../JS/diagrams.js"></script>
    <script>
        // Initialize module
        document.addEventListener('DOMContentLoaded', function() {
            initializePatientMonitor();
            initializeDiagrams();
            startVitalSignsAnimation();
        });
    </script>
</body>
</html>
