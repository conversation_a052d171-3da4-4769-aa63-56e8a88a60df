<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mechanical Ventilation Module - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/ventilation-module.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="../HTML/lectures.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Lectures
                </a>
                <div class="module-title">
                    <h1>Mechanical Ventilation Module</h1>
                    <p>Advanced Respiratory Support & Ventilator Management</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Module Overview -->
    <section class="module-overview">
        <div class="overview-content">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-lungs animated-pulse"></i>
                    </div>
                    <h3>Ventilator Modes</h3>
                    <p>Master volume-controlled, pressure-controlled, and spontaneous breathing modes</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%"></div>
                    </div>
                    <span class="progress-text">88% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-chart-area animated-gauge"></i>
                    </div>
                    <h3>Respiratory Mechanics</h3>
                    <p>Understand compliance, resistance, and pressure-volume relationships</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 82%"></div>
                    </div>
                    <span class="progress-text">82% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-exclamation-triangle animated-heartbeat"></i>
                    </div>
                    <h3>Troubleshooting</h3>
                    <p>Identify and resolve ventilator alarms and patient-ventilator asynchrony</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <span class="progress-text">75% Complete</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Ventilator Simulator -->
    <section class="learning-tools">
        <div class="tools-container">
            <h2 class="section-title">Virtual Ventilator System</h2>
            
            <!-- Ventilator Simulator -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Advanced Ventilator Simulator</h3>
                    <p>Real-time mechanical ventilation with multiple modes and comprehensive monitoring</p>
                </div>
                
                <div class="ventilator-system">
                    <!-- Ventilator Display -->
                    <div class="ventilator-display">
                        <div class="ventilator-screen">
                            <div class="screen-header">
                                <h4>Mechanical Ventilator</h4>
                                <div class="ventilator-status">
                                    <span class="status-light active" id="ventPowerStatus"></span>
                                    <span class="status-text">Ventilating</span>
                                    <div class="mode-display">
                                        <span class="mode-label">Mode:</span>
                                        <span class="mode-value" id="currentMode">VCV</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Waveform Display -->
                            <div class="waveform-section">
                                <div class="waveform-group">
                                    <div class="waveform-display">
                                        <div class="waveform-label">Pressure (cmH₂O)</div>
                                        <canvas id="pressureWaveform" width="600" height="120"></canvas>
                                        <div class="waveform-scale">
                                            <span>40</span><span>20</span><span>0</span>
                                        </div>
                                    </div>
                                    
                                    <div class="waveform-display">
                                        <div class="waveform-label">Flow (L/min)</div>
                                        <canvas id="flowWaveform" width="600" height="120"></canvas>
                                        <div class="waveform-scale">
                                            <span>60</span><span>0</span><span>-60</span>
                                        </div>
                                    </div>
                                    
                                    <div class="waveform-display">
                                        <div class="waveform-label">Volume (mL)</div>
                                        <canvas id="volumeWaveform" width="600" height="120"></canvas>
                                        <div class="waveform-scale">
                                            <span>800</span><span>400</span><span>0</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Pressure-Volume Loop -->
                                <div class="pv-loop-display">
                                    <div class="loop-label">Pressure-Volume Loop</div>
                                    <canvas id="pvLoopCanvas" width="300" height="300"></canvas>
                                    <div class="loop-axes">
                                        <span class="x-axis">Volume (mL)</span>
                                        <span class="y-axis">Pressure (cmH₂O)</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Parameter Display -->
                            <div class="parameter-section">
                                <div class="parameter-group">
                                    <h5>Set Parameters</h5>
                                    <div class="parameter-grid">
                                        <div class="parameter-item">
                                            <span class="param-label">Tidal Volume</span>
                                            <span class="param-value" id="setTV">500</span>
                                            <span class="param-unit">mL</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">Respiratory Rate</span>
                                            <span class="param-value" id="setRR">12</span>
                                            <span class="param-unit">/min</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">PEEP</span>
                                            <span class="param-value" id="setPEEP">5</span>
                                            <span class="param-unit">cmH₂O</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">FiO₂</span>
                                            <span class="param-value" id="setFiO2">50</span>
                                            <span class="param-unit">%</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">I:E Ratio</span>
                                            <span class="param-value" id="setIE">1:2</span>
                                            <span class="param-unit"></span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">Trigger</span>
                                            <span class="param-value" id="setTrigger">-2</span>
                                            <span class="param-unit">cmH₂O</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="parameter-group">
                                    <h5>Measured Values</h5>
                                    <div class="parameter-grid">
                                        <div class="parameter-item measured">
                                            <span class="param-label">Peak Pressure</span>
                                            <span class="param-value" id="measuredPIP">25</span>
                                            <span class="param-unit">cmH₂O</span>
                                        </div>
                                        <div class="parameter-item measured">
                                            <span class="param-label">Plateau Pressure</span>
                                            <span class="param-value" id="measuredPplat">20</span>
                                            <span class="param-unit">cmH₂O</span>
                                        </div>
                                        <div class="parameter-item measured">
                                            <span class="param-label">Expired TV</span>
                                            <span class="param-value" id="measuredTVe">485</span>
                                            <span class="param-unit">mL</span>
                                        </div>
                                        <div class="parameter-item measured">
                                            <span class="param-label">Minute Volume</span>
                                            <span class="param-value" id="measuredMV">5.8</span>
                                            <span class="param-unit">L/min</span>
                                        </div>
                                        <div class="parameter-item measured">
                                            <span class="param-label">Compliance</span>
                                            <span class="param-value" id="measuredComp">32</span>
                                            <span class="param-unit">mL/cmH₂O</span>
                                        </div>
                                        <div class="parameter-item measured">
                                            <span class="param-label">Resistance</span>
                                            <span class="param-value" id="measuredRes">8</span>
                                            <span class="param-unit">cmH₂O/L/s</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Alarm Panel -->
                            <div class="alarm-section">
                                <h5>Alarms & Alerts</h5>
                                <div class="alarm-display" id="alarmDisplay">
                                    <div class="alarm-item normal">
                                        <i class="fas fa-check-circle"></i>
                                        <span>All parameters within normal limits</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Ventilator Controls -->
                        <div class="ventilator-controls">
                            <div class="control-section">
                                <h5>Ventilation Mode</h5>
                                <div class="mode-controls">
                                    <button class="mode-btn active" onclick="selectMode('VCV')">
                                        <i class="fas fa-chart-bar"></i>
                                        VCV
                                    </button>
                                    <button class="mode-btn" onclick="selectMode('PCV')">
                                        <i class="fas fa-chart-area"></i>
                                        PCV
                                    </button>
                                    <button class="mode-btn" onclick="selectMode('SIMV')">
                                        <i class="fas fa-wave-square"></i>
                                        SIMV
                                    </button>
                                    <button class="mode-btn" onclick="selectMode('PSV')">
                                        <i class="fas fa-wind"></i>
                                        PSV
                                    </button>
                                    <button class="mode-btn" onclick="selectMode('CPAP')">
                                        <i class="fas fa-compress"></i>
                                        CPAP
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Parameter Adjustment</h5>
                                <div class="parameter-controls">
                                    <div class="control-group">
                                        <label>Tidal Volume (mL)</label>
                                        <div class="control-buttons">
                                            <button onclick="adjustParameter('tv', -50)">-50</button>
                                            <button onclick="adjustParameter('tv', -10)">-10</button>
                                            <button onclick="adjustParameter('tv', 10)">+10</button>
                                            <button onclick="adjustParameter('tv', 50)">+50</button>
                                        </div>
                                    </div>
                                    
                                    <div class="control-group">
                                        <label>Respiratory Rate (/min)</label>
                                        <div class="control-buttons">
                                            <button onclick="adjustParameter('rr', -2)">-2</button>
                                            <button onclick="adjustParameter('rr', -1)">-1</button>
                                            <button onclick="adjustParameter('rr', 1)">+1</button>
                                            <button onclick="adjustParameter('rr', 2)">+2</button>
                                        </div>
                                    </div>
                                    
                                    <div class="control-group">
                                        <label>PEEP (cmH₂O)</label>
                                        <div class="control-buttons">
                                            <button onclick="adjustParameter('peep', -2)">-2</button>
                                            <button onclick="adjustParameter('peep', -1)">-1</button>
                                            <button onclick="adjustParameter('peep', 1)">+1</button>
                                            <button onclick="adjustParameter('peep', 2)">+2</button>
                                        </div>
                                    </div>
                                    
                                    <div class="control-group">
                                        <label>FiO₂ (%)</label>
                                        <div class="control-buttons">
                                            <button onclick="adjustParameter('fio2', -10)">-10</button>
                                            <button onclick="adjustParameter('fio2', -5)">-5</button>
                                            <button onclick="adjustParameter('fio2', 5)">+5</button>
                                            <button onclick="adjustParameter('fio2', 10)">+10</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Patient Scenarios</h5>
                                <div class="scenario-controls">
                                    <button class="scenario-btn" onclick="loadScenario('normal')">
                                        <i class="fas fa-user"></i>
                                        Normal Lungs
                                    </button>
                                    <button class="scenario-btn" onclick="loadScenario('ards')">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        ARDS
                                    </button>
                                    <button class="scenario-btn" onclick="loadScenario('copd')">
                                        <i class="fas fa-lungs"></i>
                                        COPD
                                    </button>
                                    <button class="scenario-btn" onclick="loadScenario('asthma')">
                                        <i class="fas fa-wind"></i>
                                        Asthma
                                    </button>
                                    <button class="scenario-btn" onclick="loadScenario('pneumothorax')">
                                        <i class="fas fa-heartbeat"></i>
                                        Pneumothorax
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Respiratory Mechanics Diagram -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Respiratory Mechanics</h3>
                    <p>Interactive visualization of lung compliance, resistance, and pressure-volume relationships</p>
                </div>
                
                <div class="diagram-container">
                    <div class="diagram-tabs">
                        <button class="tab-btn active" onclick="showMechanicsDiagram('compliance')">Compliance</button>
                        <button class="tab-btn" onclick="showMechanicsDiagram('resistance')">Resistance</button>
                        <button class="tab-btn" onclick="showMechanicsDiagram('work')">Work of Breathing</button>
                    </div>
                    
                    <!-- Compliance Diagram -->
                    <div id="complianceDiagram" class="diagram-panel active">
                        <div class="mechanics-visualization">
                            <div class="lung-model">
                                <svg viewBox="0 0 400 300" class="lung-diagram">
                                    <!-- Lung outline -->
                                    <path d="M 100 80 Q 150 60 200 80 Q 250 60 300 80 Q 320 120 300 180 Q 250 220 200 200 Q 150 220 100 180 Q 80 120 100 80 Z" 
                                          class="lung-outline" id="lungOutline"/>
                                    
                                    <!-- Alveoli -->
                                    <g class="alveoli-group">
                                        <circle cx="150" cy="120" r="8" class="alveolus"/>
                                        <circle cx="180" cy="110" r="6" class="alveolus"/>
                                        <circle cx="220" cy="125" r="7" class="alveolus"/>
                                        <circle cx="250" cy="115" r="8" class="alveolus"/>
                                        <circle cx="170" cy="150" r="9" class="alveolus"/>
                                        <circle cx="210" cy="160" r="7" class="alveolus"/>
                                        <circle cx="240" cy="155" r="8" class="alveolus"/>
                                    </g>
                                    
                                    <!-- Pressure indicators -->
                                    <g class="pressure-indicators">
                                        <text x="200" y="50" class="pressure-label">Airway Pressure</text>
                                        <text x="200" y="250" class="pressure-value" id="airwayPressure">20 cmH₂O</text>
                                    </g>
                                </svg>
                            </div>
                            
                            <div class="compliance-graph">
                                <canvas id="complianceGraph" width="300" height="200"></canvas>
                                <div class="graph-labels">
                                    <span class="x-label">Volume (mL)</span>
                                    <span class="y-label">Pressure (cmH₂O)</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mechanics-controls">
                            <div class="slider-control">
                                <label>Lung Compliance</label>
                                <input type="range" min="10" max="60" value="30" id="complianceSlider" onchange="updateCompliance(this.value)">
                                <span id="complianceValue">30 mL/cmH₂O</span>
                            </div>
                            
                            <div class="compliance-info">
                                <h6>Compliance Conditions</h6>
                                <ul>
                                    <li><strong>Normal:</strong> 50-100 mL/cmH₂O</li>
                                    <li><strong>ARDS:</strong> 20-30 mL/cmH₂O</li>
                                    <li><strong>Fibrosis:</strong> 15-25 mL/cmH₂O</li>
                                    <li><strong>Emphysema:</strong> 100-200 mL/cmH₂O</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Component Information Panel -->
                    <div class="component-info" id="mechanicsComponentInfo">
                        <h4 id="mechanicsComponentTitle">Respiratory Mechanics</h4>
                        <p id="mechanicsComponentDescription">Respiratory mechanics describe the physical properties of the lungs and chest wall that determine the work required for breathing. Adjust the controls to see how different pathological conditions affect lung function.</p>
                        <div id="mechanicsComponentSpecs"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comprehensive Ventilation Notes -->
    <section class="lecture-notes">
        <div class="notes-container">
            <h2 class="section-title">Mechanical Ventilation Fundamentals</h2>
            
            <div class="notes-grid">
                <!-- Ventilator Modes -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-lungs"></i>
                        </div>
                        <h3>Ventilator Modes</h3>
                    </div>
                    <div class="note-content">
                        <h4>Volume-Controlled Ventilation (VCV)</h4>
                        <ul>
                            <li><strong>Principle:</strong> Delivers set tidal volume</li>
                            <li><strong>Pressure:</strong> Variable, depends on compliance</li>
                            <li><strong>Advantages:</strong> Guaranteed minute ventilation</li>
                            <li><strong>Disadvantages:</strong> Risk of barotrauma</li>
                        </ul>
                        
                        <h4>Pressure-Controlled Ventilation (PCV)</h4>
                        <ul>
                            <li><strong>Principle:</strong> Delivers set pressure</li>
                            <li><strong>Volume:</strong> Variable, depends on compliance</li>
                            <li><strong>Advantages:</strong> Pressure limitation</li>
                            <li><strong>Disadvantages:</strong> Variable tidal volumes</li>
                        </ul>
                        
                        <h4>Spontaneous Modes</h4>
                        <ul>
                            <li><strong>PSV:</strong> Pressure support for spontaneous breaths</li>
                            <li><strong>CPAP:</strong> Continuous positive airway pressure</li>
                            <li><strong>SIMV:</strong> Synchronized intermittent mandatory ventilation</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Respiratory Mechanics -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <h3>Respiratory Mechanics</h3>
                    </div>
                    <div class="note-content">
                        <h4>Compliance</h4>
                        <ul>
                            <li><strong>Definition:</strong> ΔVolume / ΔPressure</li>
                            <li><strong>Normal:</strong> 50-100 mL/cmH₂O</li>
                            <li><strong>Decreased:</strong> ARDS, fibrosis, pneumonia</li>
                            <li><strong>Increased:</strong> Emphysema, age</li>
                        </ul>
                        
                        <h4>Resistance</h4>
                        <ul>
                            <li><strong>Definition:</strong> ΔPressure / Flow</li>
                            <li><strong>Normal:</strong> 0.5-2.5 cmH₂O/L/s</li>
                            <li><strong>Increased:</strong> Asthma, COPD, secretions</li>
                            <li><strong>Factors:</strong> Airway diameter, length, turbulence</li>
                        </ul>
                        
                        <h4>Work of Breathing</h4>
                        <ul>
                            <li><strong>Elastic work:</strong> Overcoming compliance</li>
                            <li><strong>Resistive work:</strong> Overcoming resistance</li>
                            <li><strong>Normal:</strong> 0.3-0.7 J/L</li>
                            <li><strong>Increased in:</strong> Respiratory failure</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/ventilator-simulator.js"></script>
    <script src="../JS/respiratory-mechanics.js"></script>
    <script>
        // Initialize module
        document.addEventListener('DOMContentLoaded', function() {
            initializeVentilatorSimulator();
            initializeRespiratoryMechanics();
            startVentilationAnimations();
        });
    </script>
</body>
</html>
