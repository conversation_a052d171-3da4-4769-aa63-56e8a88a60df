<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ventilator Settings Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3, h4 {
            color: #0056b3; /* A slightly darker blue for headings */
        }
        .control-group {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .control-group input[type="range"] {
            width: 100%;
            box-sizing: border-box; /* Ensures padding/border don't add to width */
            cursor: pointer;
        }
        .control-group input[type="range"]:disabled {
            cursor: not-allowed;
            opacity: 0.6; /* More noticeable disabled state */
        }
        .value-display {
            font-weight: normal;
            color: #007bff; /* Standard blue for values */
        }
        .outputs-section, .summary-section, .equations-toggle-section, .control-mode-selection {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f5ff; /* Light blue background for sections */
            border: 1px solid #bde0ff; /* Border to match */
            border-radius: 5px;
        }
        .outputs-section p {
            font-size: 1.1em;
            margin: 8px 0;
        }
        .outputs-section strong {
            color: #0056b3;
        }
        #equationsSection {
            margin-top: 10px;
            padding: 10px;
            border: 1px dashed #ccc;
            background-color: #fff; /* White background for equation box */
        }
        #equationsSection p {
            margin: 5px 0;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.95em;
        }
        .control-mode-selection fieldset {
            border: 1px solid #ccc;
            padding: 10px 10px 0px 10px; /* Adjusted padding */
            margin-bottom: 0; /* fieldset already in a padded box */
        }
        .control-mode-selection legend {
            font-weight: bold;
            color: #333;
            padding: 0 5px; /* Padding for legend */
        }
        .control-mode-selection label {
            margin-right: 15px;
            font-weight: normal;
            display: inline-block; /* Keep labels inline */
            margin-bottom: 10px; /* Spacing for wrapped labels */
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 1em; /* Relative font size */
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            .control-mode-selection label {
                /* Make radio labels stack on small screens for better readability */
                display: block;
                margin-bottom: 10px; 
                margin-right: 0;
            }
            h1 {
                font-size: 1.8em;
            }
            h2 {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Anesthesia Ventilator Settings Simulator</h1>

        <div class="summary-section">
            <h2>Concept Overview</h2>
            <p>This tool illustrates key ventilator settings and their relationships. Understanding these parameters is crucial for safe and effective mechanical ventilation.</p>
            <ul>
                <li><strong>Tidal Volume (TV):</strong> The amount of air delivered with each breath.</li>
                <li><strong>Respiratory Rate (RR):</strong> The number of breaths per minute.</li>
                <li><strong>Inspiratory Flow Rate (IFR):</strong> The speed at which air is delivered during inhalation. This setting, along with TV, determines the inspiratory time.</li>
                <li><strong>Minute Volume (MV):</strong> The total volume of air delivered to the lungs per minute. It's a primary indicator of overall ventilation. (MV = TV x RR).</li>
                <li><strong>Inspiratory Time (Ti):</strong> The duration of the inspiratory phase of each breath. (Ti = TV / IFR).</li>
            </ul>
            <p>You can choose which two parameters among TV, RR, and MV you want to directly control. The third will be calculated and its dial adjusted automatically. Inspiratory Flow Rate (IFR) is always controlled directly.</p>
        </div>

        <div class="control-mode-selection">
            <fieldset>
                <legend>Select Control Mode (Determines the calculated variable):</legend>
                <label><input type="radio" name="controlMode" value="MV_calculated" checked> Control TV & RR (MV is calculated)</label>
                <label><input type="radio" name="controlMode" value="RR_calculated"> Control TV & MV (RR is calculated)</label>
                <label><input type="radio" name="controlMode" value="TV_calculated"> Control RR & MV (TV is calculated)</label>
            </fieldset>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="tvDial">Tidal Volume (TV): <span id="tvValueDisplay" class="value-display">500</span> mL</label>
                <input type="range" id="tvDial" min="50" max="1500" step="10" value="500">
            </div>
            <div class="control-group">
                <label for="rrDial">Respiratory Rate (RR): <span id="rrValueDisplay" class="value-display">12.0</span> breaths/min</label>
                <input type="range" id="rrDial" min="4" max="40" step="1" value="12">
            </div>
            <div class="control-group">
                <label for="mvDial">Minute Volume (MV): <span id="mvValueDisplay" class="value-display">6.00</span> L/min</label>
                <input type="range" id="mvDial" min="1" max="30" step="0.1" value="6.0">
            </div>
            <div class="control-group">
                <label for="ifrDial">Inspiratory Flow Rate (IFR): <span id="ifrValueDisplay" class="value-display">60</span> L/min</label>
                <input type="range" id="ifrDial" min="10" max="120" step="1" value="60">
            </div>
        </div>

        <div class="outputs-section">
            <h3>Calculated Outputs</h3>
            <p><strong>Actual Minute Volume:</strong> <span id="finalMinuteVolumeDisplay" class="value-display">6.00</span> L/min</p>
            <p><strong>Inspiratory Time (Ti):</strong> <span id="finalInspiratoryTimeDisplay" class="value-display">0.50</span> seconds</p>
        </div>

        <div class="equations-toggle-section">
            <button id="toggleEquationsButton">Show Equations</button>
            <div id="equationsSection" style="display: none;">
                <h4>Relevant Equations:</h4>
                <p>Minute Volume (MV) [L/min] = (Tidal Volume (TV) [mL] / 1000) * Respiratory Rate (RR) [breaths/min]</p>
                <p>Inspiratory Time (Ti) [s] = Tidal Volume (TV) [mL] / (Inspiratory Flow Rate (IFR) [L/min] * 1000 / 60) [mL/s]</p>
                <p><em>Derived forms based on control mode:</em></p>
                <p>If RR is calculated: RR [breaths/min] = (Target MV [L/min] * 1000) / TV [mL]</p>
                <p>If TV is calculated: TV [mL] = (Target MV [L/min] * 1000) / RR [breaths/min]</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tvDial = document.getElementById('tvDial');
            const rrDial = document.getElementById('rrDial');
            const mvDial = document.getElementById('mvDial');
            const ifrDial = document.getElementById('ifrDial');

            const tvValueDisplay = document.getElementById('tvValueDisplay');
            const rrValueDisplay = document.getElementById('rrValueDisplay');
            const mvValueDisplay = document.getElementById('mvValueDisplay');
            const ifrValueDisplay = document.getElementById('ifrValueDisplay');

            const finalMinuteVolumeDisplay = document.getElementById('finalMinuteVolumeDisplay');
            const finalInspiratoryTimeDisplay = document.getElementById('finalInspiratoryTimeDisplay');

            const controlModeRadios = document.querySelectorAll('input[name="controlMode"]');
            const toggleEquationsButton = document.getElementById('toggleEquationsButton');
            const equationsSection = document.getElementById('equationsSection');

            let currentControlMode = 'MV_calculated'; // Default

            function updateCalculationsAndDisplays() {
                let tv_input = parseFloat(tvDial.value);
                let rr_input = parseFloat(rrDial.value);
                let mv_input = parseFloat(mvDial.value); 
                let ifr_input = parseFloat(ifrDial.value);

                let effectiveTV = tv_input;
                let effectiveRR = rr_input;
                let effectiveMV = mv_input;

                if (currentControlMode === 'MV_calculated') {
                    effectiveTV = tv_input;
                    effectiveRR = rr_input;
                    effectiveMV = (effectiveTV / 1000) * effectiveRR;
                    mvDial.value = effectiveMV.toFixed(2); 
                } else if (currentControlMode === 'RR_calculated') {
                    effectiveTV = tv_input;
                    effectiveMV = mv_input; 
                    if (effectiveTV > 0) {
                        effectiveRR = (effectiveMV * 1000) / effectiveTV;
                    } else {
                        effectiveRR = 0; // Avoid division by zero, though slider min should prevent
                    }
                    // Set dial value, browser will clamp to min/max/step if necessary
                    rrDial.value = effectiveRR; 
                } else if (currentControlMode === 'TV_calculated') {
                    effectiveRR = rr_input;
                    effectiveMV = mv_input; 
                    if (effectiveRR > 0) {
                        effectiveTV = (effectiveMV * 1000) / effectiveRR;
                    } else {
                        effectiveTV = 0; // Avoid division by zero
                    }
                    tvDial.value = effectiveTV;
                }

                // Update value displays next to dials with precise calculated values
                tvValueDisplay.textContent = effectiveTV.toFixed(0);
                rrValueDisplay.textContent = effectiveRR.toFixed(1);
                mvValueDisplay.textContent = effectiveMV.toFixed(2);
                ifrValueDisplay.textContent = ifr_input.toFixed(0); // IFR is always direct input

                // Final calculated outputs based on effective values
                // These always use the current effective TV and RR, regardless of how they were derived
                const actualFinalMV = (effectiveTV / 1000) * effectiveRR;
                finalMinuteVolumeDisplay.textContent = actualFinalMV.toFixed(2);

                let inspiratoryTime = 0;
                const ifr_mL_per_sec = ifr_input * 1000 / 60;
                if (ifr_mL_per_sec > 0) {
                    inspiratoryTime = effectiveTV / ifr_mL_per_sec;
                }
                finalInspiratoryTimeDisplay.textContent = inspiratoryTime.toFixed(2);
            }

            function handleControlModeChange() {
                const prevTV = parseFloat(tvDial.value);
                const prevRR = parseFloat(rrDial.value);
                // MV dial's previous value is its current value if it was an input,
                // or the calculated value if it was an output.
                const prevMV_dial_value = parseFloat(mvDial.value);


                currentControlMode = document.querySelector('input[name="controlMode"]:checked').value;

                tvDial.disabled = (currentControlMode === 'TV_calculated');
                rrDial.disabled = (currentControlMode === 'RR_calculated');
                mvDial.disabled = (currentControlMode === 'MV_calculated');
                ifrDial.disabled = false; // Always enabled

                // Smart transition: if a dial becomes an input, ensure its value is consistent
                if (currentControlMode === 'RR_calculated' || currentControlMode === 'TV_calculated') {
                    // MV dial is now an input. If it was previously calculated, make it retain that value.
                    // If it was already an input (e.g. switching between RR_calc and TV_calc), it keeps its value.
                    // The prevMV_dial_value already holds the correct value to start with.
                    // If switching from MV_calculated, prevMV_dial_value was (prevTV/1000)*prevRR.
                    mvDial.value = prevMV_dial_value.toFixed(2);
                }
                // No special handling needed for TV or RR dials becoming inputs, as their values are preserved.
                
                updateCalculationsAndDisplays();
            }

            // Event Listeners
            tvDial.addEventListener('input', updateCalculationsAndDisplays);
            rrDial.addEventListener('input', updateCalculationsAndDisplays);
            mvDial.addEventListener('input', updateCalculationsAndDisplays);
            ifrDial.addEventListener('input', updateCalculationsAndDisplays);

            controlModeRadios.forEach(radio => {
                radio.addEventListener('change', handleControlModeChange);
            });

            toggleEquationsButton.addEventListener('click', () => {
                if (equationsSection.style.display === 'none') {
                    equationsSection.style.display = 'block';
                    toggleEquationsButton.textContent = 'Hide Equations';
                } else {
                    equationsSection.style.display = 'none';
                    toggleEquationsButton.textContent = 'Show Equations';
                }
            });

            // Initial setup
            handleControlModeChange(); // Sets initial dial disabled states and calculates/displays
        });
    </script>
</body>
</html>
