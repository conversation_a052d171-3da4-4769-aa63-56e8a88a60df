<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ventilator Basics Simulator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding-top: 20px;
            padding-bottom: 20px;
        }

        .app-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            margin: 0 15px;
        }

        header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 15px;
        }

        h1 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 1.8em;
        }

        .equation {
            font-style: italic;
            color: #555;
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.95em;
        }

        h2 {
            color: #34495e;
            margin-top: 20px;
            margin-bottom: 15px;
            font-size: 1.4em;
            border-bottom: 1px dashed #ddd;
            padding-bottom: 5px;
        }

        .controls, .simulation, .info-display {
            margin-bottom: 20px;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
            font-size: 0.9em;
        }

        .input-group input[type="number"],
        .input-group input[type="text"] {
            width: calc(100% - 20px);
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 1em;
            box-sizing: border-box;
        }
        .input-group input[readonly] {
            background-color: #e9ecef;
            cursor: not-allowed;
        }

        .input-group .description {
            display: block;
            font-size: 0.8em;
            color: #777;
            margin-top: 3px;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease;
            display: block;
            width: 100%;
            margin-top: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }

        .error-messages {
            color: #d9534f;
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            padding: 10px;
            border-radius: 4px;
            margin-top: 15px;
            font-size: 0.9em;
        }
        .error-messages:empty {
            display: none;
        }

        .bellows-section {
            text-align: center;
        }

        .bellows-container {
            width: 120px;
            height: 280px;
            margin: 20px auto;
            border: 3px solid #34495e;
            display: flex;
            flex-direction: column-reverse; /* Bellows "fills" from bottom up */
            background-color: #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }
        .bellows-container::before { /* Top plate visual */
            content: '';
            position: absolute;
            top: 0;
            left: 5%;
            width: 90%;
            height: 15px;
            background-color: #546e7a;
            border-radius: 3px 3px 0 0;
            z-index: 2;
        }


        #bellows {
            width: 100%;
            background-color: #007bff; /* Accent color */
            /* height is set by JS */
            transition: height 0.3s ease-in-out;
            position: relative;
            z-index: 1;
        }
        #bellows::after { /* Bottom plate visual - attached to the moving part */
            content: '';
            position: absolute;
            bottom: 0;
            left: 5%;
            width: 90%;
            height: 15px;
            background-color: #546e7a;
            border-radius: 0 0 3px 3px;
        }


        .info-display p {
            margin: 5px 0;
            font-size: 0.95em;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 3px;
            border: 1px solid #eee;
        }
        .info-display span {
            font-weight: bold;
            color: #0056b3;
        }

        footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.85em;
            color: #777;
        }

        @media (max-width: 480px) {
            h1 { font-size: 1.6em; }
            h2 { font-size: 1.3em; }
            .app-container { padding: 15px; }
            .bellows-container { width: 100px; height: 220px; }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <div style="text-align: center; margin-bottom: 15px;">
            <a href="HTML/index.html" style="display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold; margin-bottom: 10px;">
                🏠 Return to Home
            </a>
        </div>
        <header>
            <h1>Ventilator Basics</h1>
            <p class="equation">Minute Volume (L/min) = (Tidal Volume (mL) &times; Respiratory Rate (breaths/min)) / 1000</p>
        </header>

        <main>
            <section class="controls">
                <h2>Controls</h2>
                <div class="input-group">
                    <label for="tidalVolume">Tidal Volume (mL):</label>
                    <input type="number" id="tidalVolume" value="500" step="10">
                    <small class="description">Volume of air per breath (e.g., 50-1500 mL).</small>
                </div>
                <div class="input-group">
                    <label for="respiratoryRate">Respiratory Rate (breaths/min):</label>
                    <input type="number" id="respiratoryRate" value="12" step="1">
                    <small class="description">Breaths per minute (e.g., 1-40 breaths/min).</small>
                </div>
                <div class="input-group">
                    <label for="inspirationTime">Inspiration Time (sec):</label>
                    <input type="number" id="inspirationTime" value="1.0" step="0.1">
                    <small class="description">Duration of inspiration (e.g., 0.2-5.0 sec).</small>
                </div>
                 <div class="input-group">
                    <label for="minuteVolume">Minute Volume (L/min):</label>
                    <input type="text" id="minuteVolume" readonly>
                    <small class="description">Total air moved per minute.</small>
                </div>
                <button id="manualInspirationButton">Trigger Single Breath</button>
                <div id="errorMessages" class="error-messages"></div>
            </section>

            <section class="simulation">
                <h2>Simulation</h2>
                <div class="bellows-section">
                    <div class="bellows-container">
                        <div id="bellows"></div>
                    </div>
                </div>
                <div class="info-display">
                    <h3>Current Settings:</h3>
                    <p>Tidal Volume: <span id="displayTV">500</span> mL</p>
                    <p>Respiratory Rate: <span id="displayRR">12</span> breaths/min</p>
                    <p>Minute Volume: <span id="displayMV">6.0</span> L/min</p>
                    <p>Inspiration Time: <span id="displayIT">1.0</span> sec</p>
                    <p>Exhalation Time: <span id="displayET">-</span> sec</p>
                    <p>Cycle Time: <span id="displayCT">-</span> sec</p>
                </div>
            </section>
        </main>

        <footer>
            <p>Adjust controls to see how ventilator settings interact.</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const tidalVolumeInput = document.getElementById('tidalVolume');
            const respiratoryRateInput = document.getElementById('respiratoryRate');
            const inspirationTimeInput = document.getElementById('inspirationTime');
            const minuteVolumeOutput = document.getElementById('minuteVolume');
            const manualInspirationButton = document.getElementById('manualInspirationButton');
            const bellows = document.getElementById('bellows');
            const errorMessagesDiv = document.getElementById('errorMessages');

            const displayTV = document.getElementById('displayTV');
            const displayRR = document.getElementById('displayRR');
            const displayMV = document.getElementById('displayMV');
            const displayIT = document.getElementById('displayIT');
            const displayET = document.getElementById('displayET');
            const displayCT = document.getElementById('displayCT');

            // Constants for validation and bellows sizing
            const MIN_TV = 50;    // mL
            const MAX_TV = 1500;  // mL
            const MIN_RR = 1;     // breaths/min
            const MAX_RR = 40;    // breaths/min
            const MIN_INSP_TIME = 0.2; // sec
            const MAX_INSP_TIME = 5.0; // sec

            const BELLOWS_MIN_HEIGHT_PX = 10; // For fully compressed bellows
            const BELLOWS_CONTAINER_HEIGHT_PX = parseInt(window.getComputedStyle(document.querySelector('.bellows-container')).height); // Get actual height
            const BELLOWS_VISUAL_MAX_HEIGHT_PX = BELLOWS_CONTAINER_HEIGHT_PX - 15; // Account for top/bottom "plates" visual if any, or just use container height


            // State variables
            let autoCycleTimerId = null;
            let isAutoCycleRunning = false;
            let singleBreathInProgress = false;

            // Helper: Calculate bellows target height based on Tidal Volume
            function calculateBellowsTargetHeight(tv) {
                const clampedTV = Math.max(MIN_TV, Math.min(MAX_TV, tv));
                const tvPercent = (clampedTV - MIN_TV) / (MAX_TV - MIN_TV);
                // Ensure tvPercent is between 0 and 1, even if MIN_TV === MAX_TV (though unlikely with current constants)
                const safeTvPercent = Math.max(0, Math.min(1, tvPercent || 0));
                return BELLOWS_MIN_HEIGHT_PX + (safeTvPercent * (BELLOWS_VISUAL_MAX_HEIGHT_PX - BELLOWS_MIN_HEIGHT_PX));
            }

            // Helper: Animate bellows
            function animateBellowsVisual(targetHeight, durationSeconds) {
                bellows.style.transition = `height ${durationSeconds}s ease-in-out`;
                bellows.style.height = `${targetHeight}px`;
            }

            // Update Minute Volume display
            function updateMinuteVolumeDisplay(tv, rr) {
                if (isNaN(tv) || isNaN(rr) || tv < MIN_TV || tv > MAX_TV || rr < MIN_RR || rr > MAX_RR) {
                    minuteVolumeOutput.value = 'N/A';
                    displayMV.textContent = 'N/A';
                    return;
                }
                const mv = (tv / 1000) * rr;
                minuteVolumeOutput.value = mv.toFixed(1);
                displayMV.textContent = mv.toFixed(1);
            }
            
            // Update all displayed info values
            function updateInfoDisplay(tv, rr, inspTime, exhTime, cycleTime, mv) {
                displayTV.textContent = isNaN(tv) ? '-' : tv;
                displayRR.textContent = isNaN(rr) ? '-' : rr;
                displayMV.textContent = isNaN(mv) ? (minuteVolumeOutput.value || '-') : mv.toFixed(1);
                displayIT.textContent = isNaN(inspTime) ? '-' : inspTime.toFixed(1);
                displayET.textContent = isNaN(exhTime) || exhTime <=0 ? (cycleTime > 0 ? 'Too short!' : '-') : exhTime.toFixed(1);
                displayCT.textContent = isNaN(cycleTime) || cycleTime <=0 ? '-' : cycleTime.toFixed(1);
            }


            // Validate all inputs and return parsed values and status
            function validateInputsAndGetParams() {
                const tv = parseFloat(tidalVolumeInput.value);
                const rr = parseFloat(respiratoryRateInput.value);
                const inspTime = parseFloat(inspirationTimeInput.value);
                let messages = [];

                if (isNaN(tv) || tv < MIN_TV || tv > MAX_TV) {
                    messages.push(`Tidal Volume must be between ${MIN_TV} and ${MAX_TV} mL.`);
                }
                if (isNaN(rr) || rr < MIN_RR || rr > MAX_RR) {
                    messages.push(`Respiratory Rate must be between ${MIN_RR} and ${MAX_RR} breaths/min.`);
                }
                if (isNaN(inspTime) || inspTime < MIN_INSP_TIME || inspTime > MAX_INSP_TIME) {
                    messages.push(`Inspiration Time must be between ${MIN_INSP_TIME} and ${MAX_INSP_TIME} sec.`);
                }

                let cycleTime = NaN;
                let exhalationTime = NaN;
                let paramsValid = messages.length === 0;

                if (paramsValid && rr > 0) {
                    cycleTime = 60 / rr;
                    exhalationTime = cycleTime - inspTime;
                    if (exhalationTime <= 0.05) { // Using a small positive threshold
                        messages.push(`Inspiration time (${inspTime.toFixed(1)}s) is too long for the current respiratory rate (${rr}), resulting in insufficient exhalation time.`);
                        paramsValid = false;
                        exhalationTime = 0; // Or NaN
                    }
                } else if (paramsValid) { // RR is 0 or invalid
                    cycleTime = NaN;
                    exhalationTime = NaN;
                }
                
                const currentMV = (paramsValid && !isNaN(tv) && !isNaN(rr)) ? (tv / 1000) * rr : NaN;
                updateInfoDisplay(tv, rr, inspTime, exhalationTime, cycleTime, currentMV);


                return {
                    isValid: paramsValid,
                    errorMessages: messages,
                    values: {
                        tv: paramsValid ? tv : parseFloat(tidalVolumeInput.value) || MIN_TV, // Fallback for bellows display
                        rr: rr,
                        inspTime: inspTime,
                        exhTime: exhalationTime,
                        cycleTime: cycleTime,
                        mv: currentMV
                    }
                };
            }

            // Display error messages
            function displayErrors(errorsArray) {
                errorMessagesDiv.innerHTML = errorsArray.join('<br>');
            }

            // Stop automatic breathing cycle
            function stopAutomaticCycle() {
                clearInterval(autoCycleTimerId);
                isAutoCycleRunning = false;
                autoCycleTimerId = null;
            }

            // Start automatic breathing cycle
            function startAutomaticCycle(params) {
                stopAutomaticCycle(); // Ensure any existing cycle is stopped

                if (!params.isValid || params.values.exhTime <= 0) {
                     // Error should already be displayed by handleInputChange calling validate
                    return;
                }

                isAutoCycleRunning = true;
                const { tv, inspTime, exhTime, cycleTime } = params.values;

                function breath() {
                    // Get the latest TV for exhalation height, in case it changed during a cycle
                    const latestTVValue = parseFloat(tidalVolumeInput.value);
                    const validatedLatestTV = Math.max(MIN_TV, Math.min(MAX_TV, latestTVValue || MIN_TV));
                    const targetExhaledHeight = calculateBellowsTargetHeight(validatedLatestTV);

                    animateBellowsVisual(BELLOWS_MIN_HEIGHT_PX, inspTime); // Inspire
                    setTimeout(() => {
                        animateBellowsVisual(targetExhaledHeight, exhTime); // Exhale
                    }, inspTime * 1000);
                }

                breath(); // First breath immediately
                autoCycleTimerId = setInterval(breath, cycleTime * 1000);
            }
            
            // Handle manual breath trigger
            manualInspirationButton.addEventListener('click', () => {
                if (singleBreathInProgress) return;

                const params = validateInputsAndGetParams();
                updateMinuteVolumeDisplay(params.values.tv, params.values.rr);


                if (!params.isValid) {
                    displayErrors(params.errorMessages);
                    return;
                }
                displayErrors([]); // Clear errors

                if (params.values.exhTime <= 0) {
                    displayErrors(["Cannot trigger breath: Inspiration time too long for current rate."]);
                    return;
                }

                singleBreathInProgress = true;
                const { tv, inspTime, exhTime } = params.values;
                const targetExhaledHeight = calculateBellowsTargetHeight(tv);

                animateBellowsVisual(BELLOWS_MIN_HEIGHT_PX, inspTime); // Inspire
                setTimeout(() => {
                    animateBellowsVisual(targetExhaledHeight, exhTime); // Exhale
                    setTimeout(() => { // Reset flag after exhalation completes
                        singleBreathInProgress = false;
                    }, exhTime * 1000 + 50);
                }, inspTime * 1000);
            });


            // Main handler for input changes
            function handleInputChange() {
                const params = validateInputsAndGetParams();
                updateMinuteVolumeDisplay(params.values.tv, params.values.rr); // Update MV field

                // Update bellows static appearance based on TV if not auto-cycling or TV is active input
                const currentTVValue = parseFloat(tidalVolumeInput.value);
                const validatedTVForBellows = Math.max(MIN_TV, Math.min(MAX_TV, currentTVValue || MIN_TV));
                const targetStaticBellowsHeight = calculateBellowsTargetHeight(validatedTVForBellows);

                if (!isAutoCycleRunning || document.activeElement === tidalVolumeInput) {
                     if (!isNaN(validatedTVForBellows)) {
                        animateBellowsVisual(targetStaticBellowsHeight, 0.2);
                     } else { // If TV is completely invalid (e.g. empty string)
                        animateBellowsVisual(BELLOWS_MIN_HEIGHT_PX, 0.2);
                     }
                }


                if (!params.isValid) {
                    displayErrors(params.errorMessages);
                    stopAutomaticCycle();
                    // If TV itself is invalid, reset bellows to min
                    if (isNaN(params.values.tv) || params.values.tv < MIN_TV || params.values.tv > MAX_TV) {
                        animateBellowsVisual(BELLOWS_MIN_HEIGHT_PX, 0.2);
                    }
                    return;
                }
                
                displayErrors([]); // Clear errors
                startAutomaticCycle(params);
            }

            // Initial setup
            function initializeApp() {
                tidalVolumeInput.min = MIN_TV;
                tidalVolumeInput.max = MAX_TV;
                respiratoryRateInput.min = MIN_RR;
                respiratoryRateInput.max = MAX_RR;
                inspirationTimeInput.min = MIN_INSP_TIME;
                inspirationTimeInput.max = MAX_INSP_TIME;

                // Add event listeners
                [tidalVolumeInput, respiratoryRateInput, inspirationTimeInput].forEach(input => {
                    input.addEventListener('input', handleInputChange);
                });
                
                // Initial calculation and UI update
                handleInputChange(); // This will validate, set displays, and start cycle if valid
            }

            initializeApp();
        });
    </script>
</body>
</html>
