// ===== VENTILATOR SIMULATOR ===== //

// Ventilator State
const ventilatorState = {
    isRunning: false,
    mode: 'VCV',
    settings: {
        tidalVolume: 500,      // mL
        respiratoryRate: 12,   // /min
        peep: 5,               // cmH2O
        fio2: 50,              // %
        ieRatio: '1:2',
        trigger: -2,           // cmH2O
        inspiratoryTime: 1.0,  // seconds
        pressureSupport: 10    // cmH2O (for PSV)
    },
    measured: {
        pip: 25,               // Peak inspiratory pressure
        pplat: 20,             // Plateau pressure
        tve: 485,              // Expired tidal volume
        minuteVolume: 5.8,     // L/min
        compliance: 32,        // mL/cmH2O
        resistance: 8          // cmH2O/L/s
    },
    patient: {
        compliance: 50,        // mL/cmH2O
        resistance: 5,         // cmH2O/L/s
        condition: 'normal'
    },
    alarms: {
        highPressure: false,
        lowVolume: false,
        apnea: false,
        disconnect: false
    },
    waveforms: {
        pressure: [],
        flow: [],
        volume: [],
        pvLoop: []
    }
};

// Canvas contexts
let pressureCanvas, flowCanvas, volumeCanvas, pvLoopCanvas;
let pressureCtx, flowCtx, volumeCtx, pvLoopCtx;

// Animation variables
let ventAnimationId;
let ventTimeOffset = 0;
let breathCycle = 0;

// Patient scenarios
const patientScenarios = {
    normal: {
        compliance: 50,
        resistance: 5,
        name: 'Normal Lungs'
    },
    ards: {
        compliance: 25,
        resistance: 8,
        name: 'ARDS'
    },
    copd: {
        compliance: 80,
        resistance: 15,
        name: 'COPD'
    },
    asthma: {
        compliance: 45,
        resistance: 20,
        name: 'Asthma'
    },
    pneumothorax: {
        compliance: 30,
        resistance: 6,
        name: 'Pneumothorax'
    }
};

// Initialize Ventilator Simulator
function initializeVentilatorSimulator() {
    console.log('Initializing Ventilator Simulator...');
    
    // Get canvas elements
    pressureCanvas = document.getElementById('pressureWaveform');
    flowCanvas = document.getElementById('flowWaveform');
    volumeCanvas = document.getElementById('volumeWaveform');
    pvLoopCanvas = document.getElementById('pvLoopCanvas');
    
    if (pressureCanvas) pressureCtx = pressureCanvas.getContext('2d');
    if (flowCanvas) flowCtx = flowCanvas.getContext('2d');
    if (volumeCanvas) volumeCtx = volumeCanvas.getContext('2d');
    if (pvLoopCanvas) pvLoopCtx = pvLoopCanvas.getContext('2d');
    
    // Start ventilator
    startVentilator();
    
    // Update displays
    updateVentilatorDisplay();
    
    console.log('Ventilator Simulator initialized');
}

// Start Ventilator
function startVentilator() {
    ventilatorState.isRunning = true;
    animateVentilator();
}

// Stop Ventilator
function stopVentilator() {
    ventilatorState.isRunning = false;
    if (ventAnimationId) {
        cancelAnimationFrame(ventAnimationId);
    }
}

// Update Ventilator Display
function updateVentilatorDisplay() {
    // Update mode display
    document.getElementById('currentMode').textContent = ventilatorState.mode;
    
    // Update set parameters
    document.getElementById('setTV').textContent = ventilatorState.settings.tidalVolume;
    document.getElementById('setRR').textContent = ventilatorState.settings.respiratoryRate;
    document.getElementById('setPEEP').textContent = ventilatorState.settings.peep;
    document.getElementById('setFiO2').textContent = ventilatorState.settings.fio2;
    document.getElementById('setIE').textContent = ventilatorState.settings.ieRatio;
    document.getElementById('setTrigger').textContent = ventilatorState.settings.trigger;
    
    // Update measured values
    document.getElementById('measuredPIP').textContent = ventilatorState.measured.pip;
    document.getElementById('measuredPplat').textContent = ventilatorState.measured.pplat;
    document.getElementById('measuredTVe').textContent = ventilatorState.measured.tve;
    document.getElementById('measuredMV').textContent = ventilatorState.measured.minuteVolume.toFixed(1);
    document.getElementById('measuredComp').textContent = ventilatorState.measured.compliance;
    document.getElementById('measuredRes').textContent = ventilatorState.measured.resistance;
    
    // Update status
    updateVentilatorStatus();
    
    // Update alarms
    updateAlarmDisplay();
}

// Update Ventilator Status
function updateVentilatorStatus() {
    const statusLight = document.getElementById('ventPowerStatus');
    const statusText = document.querySelector('.ventilator-status .status-text');
    
    if (statusLight && statusText) {
        if (ventilatorState.isRunning) {
            statusLight.classList.add('active');
            statusText.textContent = 'Ventilating';
        } else {
            statusLight.classList.remove('active');
            statusText.textContent = 'Standby';
        }
    }
}

// Animate Ventilator
function animateVentilator() {
    if (!ventilatorState.isRunning) return;
    
    ventTimeOffset += 0.02;
    
    // Calculate breath cycle
    const rr = ventilatorState.settings.respiratoryRate;
    const cycleTime = 60 / rr; // seconds per breath
    const cyclePhase = (ventTimeOffset % cycleTime) / cycleTime;
    
    // Update breath cycle counter
    if (cyclePhase < 0.02 && Math.floor(ventTimeOffset / cycleTime) > breathCycle) {
        breathCycle = Math.floor(ventTimeOffset / cycleTime);
        calculateMeasuredValues();
    }
    
    // Draw waveforms
    drawPressureWaveform(cyclePhase, cycleTime);
    drawFlowWaveform(cyclePhase, cycleTime);
    drawVolumeWaveform(cyclePhase, cycleTime);
    drawPVLoop();
    
    ventAnimationId = requestAnimationFrame(animateVentilator);
}

// Draw Pressure Waveform
function drawPressureWaveform(cyclePhase, cycleTime) {
    if (!pressureCtx || !pressureCanvas) return;
    
    const width = pressureCanvas.width;
    const height = pressureCanvas.height;
    
    // Clear canvas
    pressureCtx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawVentilatorGrid(pressureCtx, width, height, '#3b82f6');
    
    // Set up drawing
    pressureCtx.strokeStyle = '#3b82f6';
    pressureCtx.lineWidth = 3;
    pressureCtx.beginPath();
    
    const pixelsPerSecond = width / 20; // 20 seconds visible
    const peep = ventilatorState.settings.peep;
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + ventTimeOffset;
        const localCyclePhase = (time % cycleTime) / cycleTime;
        
        let pressure = peep;
        
        if (ventilatorState.mode === 'VCV') {
            pressure = calculateVCVPressure(localCyclePhase, peep);
        } else if (ventilatorState.mode === 'PCV') {
            pressure = calculatePCVPressure(localCyclePhase, peep);
        }
        
        const y = height - (pressure / 40) * height;
        
        if (x === 0) {
            pressureCtx.moveTo(x, y);
        } else {
            pressureCtx.lineTo(x, y);
        }
    }
    
    pressureCtx.stroke();
}

// Calculate VCV Pressure
function calculateVCVPressure(phase, peep) {
    const compliance = ventilatorState.patient.compliance;
    const resistance = ventilatorState.patient.resistance;
    const tv = ventilatorState.settings.tidalVolume;
    
    if (phase < 0.33) {
        // Inspiration
        const volume = tv * Math.sin(phase * Math.PI / 0.33);
        const flow = tv * Math.PI * Math.cos(phase * Math.PI / 0.33) / 0.33;
        return peep + (volume / compliance) + (flow * resistance / 60);
    } else if (phase < 0.4) {
        // Plateau
        return peep + (tv / compliance);
    } else {
        // Expiration
        const expPhase = (phase - 0.4) / 0.6;
        const volume = tv * Math.exp(-expPhase * 3);
        return peep + (volume / compliance);
    }
}

// Calculate PCV Pressure
function calculatePCVPressure(phase, peep) {
    const inspiratoryPressure = 20; // cmH2O above PEEP
    
    if (phase < 0.33) {
        // Inspiration - square wave
        return peep + inspiratoryPressure;
    } else {
        // Expiration
        return peep;
    }
}

// Draw Flow Waveform
function drawFlowWaveform(cyclePhase, cycleTime) {
    if (!flowCtx || !flowCanvas) return;
    
    const width = flowCanvas.width;
    const height = flowCanvas.height;
    
    // Clear canvas
    flowCtx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawVentilatorGrid(flowCtx, width, height, '#10b981');
    
    // Draw zero line
    flowCtx.strokeStyle = '#64748b';
    flowCtx.lineWidth = 1;
    flowCtx.beginPath();
    flowCtx.moveTo(0, height / 2);
    flowCtx.lineTo(width, height / 2);
    flowCtx.stroke();
    
    // Set up drawing
    flowCtx.strokeStyle = '#10b981';
    flowCtx.lineWidth = 3;
    flowCtx.beginPath();
    
    const pixelsPerSecond = width / 20;
    const peakFlow = 60; // L/min
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + ventTimeOffset;
        const localCyclePhase = (time % cycleTime) / cycleTime;
        
        let flow = 0;
        
        if (localCyclePhase < 0.33) {
            // Inspiratory flow
            if (ventilatorState.mode === 'VCV') {
                flow = peakFlow * Math.sin(localCyclePhase * Math.PI / 0.33);
            } else {
                flow = peakFlow * (1 - Math.exp(-localCyclePhase * 10));
            }
        } else if (localCyclePhase < 0.4) {
            // End inspiration
            flow = 0;
        } else {
            // Expiratory flow
            const expPhase = (localCyclePhase - 0.4) / 0.6;
            flow = -peakFlow * 0.8 * Math.exp(-expPhase * 4);
        }
        
        const y = height / 2 - (flow / 80) * height;
        
        if (x === 0) {
            flowCtx.moveTo(x, y);
        } else {
            flowCtx.lineTo(x, y);
        }
    }
    
    flowCtx.stroke();
}

// Draw Volume Waveform
function drawVolumeWaveform(cyclePhase, cycleTime) {
    if (!volumeCtx || !volumeCanvas) return;
    
    const width = volumeCanvas.width;
    const height = volumeCanvas.height;
    
    // Clear canvas
    volumeCtx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawVentilatorGrid(volumeCtx, width, height, '#8b5cf6');
    
    // Set up drawing
    volumeCtx.strokeStyle = '#8b5cf6';
    volumeCtx.lineWidth = 3;
    volumeCtx.beginPath();
    
    const pixelsPerSecond = width / 20;
    const tv = ventilatorState.settings.tidalVolume;
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + ventTimeOffset;
        const localCyclePhase = (time % cycleTime) / cycleTime;
        
        let volume = 0;
        
        if (localCyclePhase < 0.33) {
            // Inspiration
            volume = tv * (1 - Math.cos(localCyclePhase * Math.PI / 0.33)) / 2;
        } else if (localCyclePhase < 0.4) {
            // Plateau
            volume = tv;
        } else {
            // Expiration
            const expPhase = (localCyclePhase - 0.4) / 0.6;
            volume = tv * Math.exp(-expPhase * 3);
        }
        
        const y = height - (volume / 800) * height;
        
        if (x === 0) {
            volumeCtx.moveTo(x, y);
        } else {
            volumeCtx.lineTo(x, y);
        }
    }
    
    volumeCtx.stroke();
}

// Draw PV Loop
function drawPVLoop() {
    if (!pvLoopCtx || !pvLoopCanvas) return;
    
    const width = pvLoopCanvas.width;
    const height = pvLoopCanvas.height;
    
    // Clear canvas
    pvLoopCtx.clearRect(0, 0, width, height);
    
    // Draw axes
    pvLoopCtx.strokeStyle = '#64748b';
    pvLoopCtx.lineWidth = 1;
    
    // X-axis
    pvLoopCtx.beginPath();
    pvLoopCtx.moveTo(50, height - 50);
    pvLoopCtx.lineTo(width - 20, height - 50);
    pvLoopCtx.stroke();
    
    // Y-axis
    pvLoopCtx.beginPath();
    pvLoopCtx.moveTo(50, height - 50);
    pvLoopCtx.lineTo(50, 20);
    pvLoopCtx.stroke();
    
    // Draw PV loop
    pvLoopCtx.strokeStyle = '#8b5cf6';
    pvLoopCtx.lineWidth = 3;
    pvLoopCtx.beginPath();
    
    const tv = ventilatorState.settings.tidalVolume;
    const peep = ventilatorState.settings.peep;
    const compliance = ventilatorState.patient.compliance;
    
    for (let i = 0; i <= 100; i++) {
        const volume = (i / 100) * tv;
        const pressure = peep + (volume / compliance);
        
        const x = 50 + (volume / tv) * (width - 70);
        const y = height - 50 - (pressure / 30) * (height - 70);
        
        if (i === 0) {
            pvLoopCtx.moveTo(x, y);
        } else {
            pvLoopCtx.lineTo(x, y);
        }
    }
    
    // Close the loop (expiration)
    for (let i = 100; i >= 0; i--) {
        const volume = (i / 100) * tv;
        const pressure = peep + (volume / compliance) * 0.8; // Lower pressure on expiration
        
        const x = 50 + (volume / tv) * (width - 70);
        const y = height - 50 - (pressure / 30) * (height - 70);
        
        pvLoopCtx.lineTo(x, y);
    }
    
    pvLoopCtx.stroke();
}

// Draw Ventilator Grid
function drawVentilatorGrid(ctx, width, height, color) {
    ctx.strokeStyle = color;
    ctx.globalAlpha = 0.1;
    ctx.lineWidth = 0.5;
    
    // Vertical lines
    for (let x = 0; x < width; x += 40) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = 0; y < height; y += 20) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
    
    ctx.globalAlpha = 1;
}

// Calculate Measured Values
function calculateMeasuredValues() {
    const compliance = ventilatorState.patient.compliance;
    const resistance = ventilatorState.patient.resistance;
    const tv = ventilatorState.settings.tidalVolume;
    const peep = ventilatorState.settings.peep;
    const rr = ventilatorState.settings.respiratoryRate;
    
    // Calculate pressures
    ventilatorState.measured.pip = Math.round(peep + (tv / compliance) + (60 * resistance / 60));
    ventilatorState.measured.pplat = Math.round(peep + (tv / compliance));
    
    // Calculate volumes
    ventilatorState.measured.tve = Math.round(tv * (0.95 + Math.random() * 0.1));
    ventilatorState.measured.minuteVolume = (ventilatorState.measured.tve * rr) / 1000;
    
    // Calculate mechanics
    ventilatorState.measured.compliance = compliance;
    ventilatorState.measured.resistance = resistance;
    
    // Check alarms
    checkAlarms();
}

// Check Alarms
function checkAlarms() {
    const pip = ventilatorState.measured.pip;
    const tve = ventilatorState.measured.tve;
    
    // Reset alarms
    ventilatorState.alarms.highPressure = pip > 35;
    ventilatorState.alarms.lowVolume = tve < ventilatorState.settings.tidalVolume * 0.8;
    ventilatorState.alarms.apnea = false; // Would be triggered by lack of breaths
    ventilatorState.alarms.disconnect = false; // Would be triggered by circuit disconnection
}

// Update Alarm Display
function updateAlarmDisplay() {
    const alarmDisplay = document.getElementById('alarmDisplay');
    if (!alarmDisplay) return;
    
    alarmDisplay.innerHTML = '';
    
    let hasAlarms = false;
    
    if (ventilatorState.alarms.highPressure) {
        hasAlarms = true;
        alarmDisplay.innerHTML += `
            <div class="alarm-item critical">
                <i class="fas fa-exclamation-triangle"></i>
                <span>High Pressure Alarm - PIP: ${ventilatorState.measured.pip} cmH₂O</span>
            </div>
        `;
    }
    
    if (ventilatorState.alarms.lowVolume) {
        hasAlarms = true;
        alarmDisplay.innerHTML += `
            <div class="alarm-item warning">
                <i class="fas fa-exclamation-circle"></i>
                <span>Low Tidal Volume - TVe: ${ventilatorState.measured.tve} mL</span>
            </div>
        `;
    }
    
    if (!hasAlarms) {
        alarmDisplay.innerHTML = `
            <div class="alarm-item normal">
                <i class="fas fa-check-circle"></i>
                <span>All parameters within normal limits</span>
            </div>
        `;
    }
}

// Select Mode
function selectMode(mode) {
    ventilatorState.mode = mode;
    
    // Update button states
    const modeButtons = document.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeButton = document.querySelector(`[onclick="selectMode('${mode}')"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
    
    updateVentilatorDisplay();
    showModeMessage(mode);
}

// Show Mode Message
function showModeMessage(mode) {
    const modeNames = {
        'VCV': 'Volume Controlled Ventilation',
        'PCV': 'Pressure Controlled Ventilation',
        'SIMV': 'Synchronized Intermittent Mandatory Ventilation',
        'PSV': 'Pressure Support Ventilation',
        'CPAP': 'Continuous Positive Airway Pressure'
    };
    
    let messageDiv = document.getElementById('modeMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'modeMessage';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--module-gradient-secondary);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(139, 92, 246, 0.4);
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = `Mode Changed: ${modeNames[mode]}`;
    messageDiv.style.display = 'block';
    
    // Hide after 3 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.display = 'none';
        }
    }, 3000);
}

// Adjust Parameter
function adjustParameter(param, delta) {
    switch (param) {
        case 'tv':
            ventilatorState.settings.tidalVolume = Math.max(200, Math.min(1000, ventilatorState.settings.tidalVolume + delta));
            break;
        case 'rr':
            ventilatorState.settings.respiratoryRate = Math.max(6, Math.min(30, ventilatorState.settings.respiratoryRate + delta));
            break;
        case 'peep':
            ventilatorState.settings.peep = Math.max(0, Math.min(20, ventilatorState.settings.peep + delta));
            break;
        case 'fio2':
            ventilatorState.settings.fio2 = Math.max(21, Math.min(100, ventilatorState.settings.fio2 + delta));
            break;
    }
    
    updateVentilatorDisplay();
}

// Load Scenario
function loadScenario(scenario) {
    const patientData = patientScenarios[scenario];
    if (patientData) {
        ventilatorState.patient.compliance = patientData.compliance;
        ventilatorState.patient.resistance = patientData.resistance;
        ventilatorState.patient.condition = scenario;
        
        showScenarioMessage(patientData.name);
    }
}

// Show Scenario Message
function showScenarioMessage(scenarioName) {
    let messageDiv = document.getElementById('scenarioMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'scenarioMessage';
        messageDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--module-gradient-accent);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(6, 182, 212, 0.4);
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = `Patient Scenario: ${scenarioName}`;
    messageDiv.style.display = 'block';
    
    // Hide after 4 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.display = 'none';
        }
    }, 4000);
}

// Start Ventilation Animations
function startVentilationAnimations() {
    // Animate status indicators
    const statusLight = document.getElementById('ventPowerStatus');
    if (statusLight) {
        statusLight.style.animation = 'pulse 2s ease-in-out infinite';
    }
}

// Export functions for global access
window.initializeVentilatorSimulator = initializeVentilatorSimulator;
window.selectMode = selectMode;
window.adjustParameter = adjustParameter;
window.loadScenario = loadScenario;
window.startVentilationAnimations = startVentilationAnimations;
