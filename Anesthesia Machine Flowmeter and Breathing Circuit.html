<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine Flow Visualizer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        #app-container {
            width: 100%;
            max-width: 900px;
            margin: 10px auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 15px;
            box-sizing: border-box;
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        #diagram-container {
            width: 100%;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #f9f9f9;
        }

        #anesthesiaDiagram {
            width: 100%;
            height: auto;
            display: block;
        }

        #controls-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: space-around;
            padding: 10px;
            border-top: 1px solid #eee;
        }

        .control-group {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ddd;
            min-width: 200px;
            flex: 1;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }
        
        .control-group .value-display {
            font-size: 0.9em;
            text-align: right;
            color: #555;
        }

        .radio-group label {
            font-weight: normal;
            margin-right: 10px;
        }
        .radio-group input[type="radio"] {
            margin-right: 5px;
        }

        #alarm-message {
            margin-top: 15px;
            padding: 10px;
            background-color: #e74c3c;
            color: white;
            text-align: center;
            font-weight: bold;
            border-radius: 4px;
            display: none; /* Hidden by default */
        }

        /* SVG specific styles */
        .gas-path {
            stroke-linecap: round;
            stroke-linejoin: round;
            transition: stroke 0.3s ease-in-out;
        }
        .flow-animation {
            stroke-dasharray: 7, 7; /* Adjust for desired dash pattern */
        }
        
        .component-label {
            font-size: 10px;
            text-anchor: middle;
            fill: #333;
        }
        .component-body {
            stroke: #333;
            stroke-width: 1.5;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 1.3em;
            }
            .control-group {
                min-width: 100%; /* Stack controls on smaller screens */
            }
        }
    </style>
</head>
<body>
    <div id="app-container">
        <div style="text-align: center; margin-bottom: 15px;">
            <a href="HTML/index.html" style="display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold; margin-bottom: 10px;" onclick="window.location.href='HTML/index.html'; return false;">
                🏠 العودة للصفحة الرئيسية
            </a>
        </div>
        <h1>Anesthesia Machine: Flowmeter & Breathing Circuit</h1>

        <div id="diagram-container">
            <svg id="anesthesiaDiagram" viewBox="0 0 800 450" preserveAspectRatio="xMidYMid meet">
                <defs>
                    <marker id="arrow" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
                        <path d="M 0 0 L 10 5 L 0 10 z" fill="#555" />
                    </marker>
                </defs>

                <!-- Oxygen Sources -->
                <g id="oxygen-source-pipeline-group">
                    <rect x="20" y="180" width="70" height="40" fill="#bdc3c7" class="component-body"/>
                    <text x="55" y="205" class="component-label">Pipeline O₂</text>
                    <path id="path-pipeline-to-regulator" d="M 90 200 H 130" stroke-width="8" fill="none" class="gas-path flow-animation"/>
                </g>
                <g id="oxygen-source-cylinder-group" style="display:none;">
                    <ellipse cx="55" cy="180" rx="25" ry="45" fill="#95a5a6" class="component-body"/>
                    <rect x="45" y="125" width="20" height="20" fill="#7f8c8d" class="component-body"/>
                    <text x="55" y="240" class="component-label">Cylinder O₂</text>
                    <path id="path-cylinder-to-regulator" d="M 80 200 H 130" stroke-width="8" fill="none" class="gas-path flow-animation"/>
                </g>

                <!-- Regulator -->
                <rect id="regulator" x="130" y="190" width="50" height="20" fill="#ecf0f1" class="component-body"/>
                <text x="155" y="205" class="component-label">Regulator</text>
                <path id="path-regulator-to-flowmeter" d="M 180 200 H 220" stroke-width="8" fill="none" class="gas-path flow-animation"/>

                <!-- Flowmeter -->
                <rect id="flowmeter-bg" x="220" y="100" width="60" height="200" fill="#ecf0f1" class="component-body"/>
                <line id="flowmeter-tube-left" x1="235" y1="290" x2="235" y2="110" stroke="#333" stroke-width="2"/>
                <line id="flowmeter-tube-right" x1="265" y1="290" x2="265" y2="110" stroke="#333" stroke-width="2"/>
                <rect id="flowmeter-bobbin" x="238" y="270" width="24" height="10" fill="#e74c3c" rx="2" ry="2"/>
                <text x="250" y="90" class="component-label">O₂ Flowmeter</text>
                <text id="flowmeter-value-text" x="250" y="310" class="component-label">0.0 L/min</text>


                <!-- Path from Flowmeter to Vaporizer -->
                <path id="path-flowmeter-to-vaporizer" d="M 280 200 H 350" stroke-width="8" fill="none" class="gas-path flow-animation"/>

                <!-- Vaporizer -->
                <rect id="vaporizer-body" x="350" y="150" width="100" height="100" fill="#a9cce3" class="component-body"/>
                <text x="400" y="140" class="component-label">Vaporizer</text>
                <circle cx="400" cy="200" r="20" fill="#fff" stroke="#333" stroke-width="1.5"/>
                <text id="vaporizer-dial-text" x="400" y="205" font-size="10px" text-anchor="middle">0.0%</text>
                
                <!-- Path from Vaporizer to Common Gas Outlet -->
                <path id="path-vaporizer-to-common" d="M 450 200 H 520" stroke-width="10" fill="none" class="gas-path flow-animation"/>

                <!-- Common Gas Outlet to Breathing Circuit -->
                <path id="path-common-to-circuit" d="M 520 200 Q 550 200 550 230 L 550 260" stroke-width="12" fill="none" class="gas-path flow-animation"/>

                <!-- Breathing Circuit -->
                <ellipse id="breathing-circuit-bag" cx="550" cy="300" rx="50" ry="35" stroke="#333" stroke-width="1.5" />
                <text x="550" y="360" class="component-label">Breathing Circuit</text>

                <!-- Patient Connection -->
                <path id="path-circuit-to-patient" d="M 550 335 Q 550 380 620 380 L 670 380" stroke="#7f8c8d" stroke-width="10" fill="none" marker-end="url(#arrow)"/>
                <rect x="670" y="360" width="80" height="40" fill="#fdebd0" class="component-body"/>
                <text x="710" y="385" class="component-label">Patient</text>
            </svg>
        </div>

        <div id="controls-container">
            <div class="control-group">
                <label for="oxygenSource">Oxygen Source:</label>
                <div class="radio-group">
                    <input type="radio" id="pipelineSource" name="oxygenSource" value="pipeline" checked>
                    <label for="pipelineSource">Pipeline</label>
                    <input type="radio" id="cylinderSource" name="oxygenSource" value="cylinder">
                    <label for="cylinderSource">Cylinder</label>
                </div>
            </div>

            <div class="control-group">
                <label for="oxygenFlow">Oxygen Flow (L/min):</label>
                <input type="range" id="oxygenFlow" min="0" max="10" step="0.1" value="2">
                <div class="value-display" id="oxygenFlowValue">2.0 L/min</div>
            </div>

            <div class="control-group">
                <label for="vaporizerOutput">Vaporizer Output (%):</label>
                <input type="range" id="vaporizerOutput" min="0" max="8" step="0.1" value="1">
                <div class="value-display" id="vaporizerOutputValue">1.0 %</div>
            </div>

            <div class="control-group">
                <label for="breathingRate">Breathing Rate (breaths/min):</label>
                <input type="range" id="breathingRate" min="4" max="30" step="1" value="12">
                <div class="value-display" id="breathingRateValue">12 bpm</div>
            </div>
        </div>

        <div id="alarm-message">ALARM: Low Oxygen Flow!</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // DOM Elements
            const oxygenSourcePipelineGroup = document.getElementById('oxygen-source-pipeline-group');
            const oxygenSourceCylinderGroup = document.getElementById('oxygen-source-cylinder-group');
            const pipelineSourceRadio = document.getElementById('pipelineSource');
            const cylinderSourceRadio = document.getElementById('cylinderSource');

            const oxygenFlowSlider = document.getElementById('oxygenFlow');
            const oxygenFlowValueDisplay = document.getElementById('oxygenFlowValue');
            const vaporizerOutputSlider = document.getElementById('vaporizerOutput');
            const vaporizerOutputValueDisplay = document.getElementById('vaporizerOutputValue');
            const breathingRateSlider = document.getElementById('breathingRate');
            const breathingRateValueDisplay = document.getElementById('breathingRateValue');

            const flowmeterBobbin = document.getElementById('flowmeter-bobbin');
            const flowmeterValueText = document.getElementById('flowmeter-value-text');
            const vaporizerDialText = document.getElementById('vaporizer-dial-text');
            const breathingCircuitBag = document.getElementById('breathing-circuit-bag');
            const alarmMessageDiv = document.getElementById('alarm-message');

            // Paths for gas flow
            const pathPipelineToReg = document.getElementById('path-pipeline-to-regulator');
            const pathCylinderToReg = document.getElementById('path-cylinder-to-regulator');
            const pathRegToFlowmeter = document.getElementById('path-regulator-to-flowmeter');
            const pathFlowmeterToVaporizer = document.getElementById('path-flowmeter-to-vaporizer');
            const pathVaporizerToCommon = document.getElementById('path-vaporizer-to-common');
            const pathCommonToCircuit = document.getElementById('path-common-to-circuit');
            const allAnimatedPaths = [
                pathPipelineToReg, pathCylinderToReg, pathRegToFlowmeter, 
                pathFlowmeterToVaporizer, pathVaporizerToCommon, pathCommonToCircuit
            ];


            // State Variables
            let currentOxygenSource = 'pipeline';
            let currentOxygenFlow = parseFloat(oxygenFlowSlider.value);
            let currentVaporizerOutput = parseFloat(vaporizerOutputSlider.value);
            let currentBreathingRate = parseInt(breathingRateSlider.value);
            let alarmActive = false;

            // Constants
            const OXYGEN_COLOR_RGB = [173, 216, 230]; // Light Blue
            const VAPOR_COLOR_RGB = [128, 0, 128];   // Purple
            const BOBBIN_MIN_Y = 275; // Bobbin at 0 flow (top of bobbin)
            const BOBBIN_MAX_Y = 115; // Bobbin at max flow (top of bobbin)
            const BOBBIN_HEIGHT = 10; // As defined in SVG
            const OXYGEN_ALARM_THRESHOLD = 0.5; // L/min
            const BAG_BASE_RY = 35; // As defined in SVG
            const BAG_RY_VARIATION_FACTOR = 0.6; // Bag size varies by 60% of base

            let dashOffset = 0;
            let lastTimestamp = 0;

            // Helper: Map range
            function mapRange(value, inMin, inMax, outMin, outMax) {
                return (value - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
            }

            // Helper: Interpolate colors [r,g,b]
            function interpolateColors(color1Rgb, color2Rgb, factor) {
                const r = Math.round(color1Rgb[0] * (1 - factor) + color2Rgb[0] * factor);
                const g = Math.round(color1Rgb[1] * (1 - factor) + color2Rgb[1] * factor);
                const b = Math.round(color1Rgb[2] * (1 - factor) + color2Rgb[2] * factor);
                return `rgb(${r},${g},${b})`;
            }

            // Update Functions
            function updateOxygenSource() {
                currentOxygenSource = pipelineSourceRadio.checked ? 'pipeline' : 'cylinder';
                if (currentOxygenSource === 'pipeline') {
                    oxygenSourcePipelineGroup.style.display = 'block';
                    oxygenSourceCylinderGroup.style.display = 'none';
                    pathPipelineToReg.style.display = 'block';
                    pathCylinderToReg.style.display = 'none';
                } else {
                    oxygenSourcePipelineGroup.style.display = 'none';
                    oxygenSourceCylinderGroup.style.display = 'block';
                    pathPipelineToReg.style.display = 'none';
                    pathCylinderToReg.style.display = 'block';
                }
                updateGasColorsAndFlow();
            }

            function updateOxygenFlow() {
                currentOxygenFlow = parseFloat(oxygenFlowSlider.value);
                oxygenFlowValueDisplay.textContent = `${currentOxygenFlow.toFixed(1)} L/min`;
                flowmeterValueText.textContent = `${currentOxygenFlow.toFixed(1)} L/min`;

                // Update bobbin position
                // Bobbin Y is top of bobbin. Min flow = bottom, Max flow = top of tube.
                // SVG Y is from top. So lower Y value means higher on screen.
                let bobbinYPos = mapRange(currentOxygenFlow, 0, 10, BOBBIN_MIN_Y, BOBBIN_MAX_Y);
                flowmeterBobbin.setAttribute('y', bobbinYPos);
                
                checkAlarm();
                updateGasColorsAndFlow();
            }

            function updateVaporizerOutput() {
                currentVaporizerOutput = parseFloat(vaporizerOutputSlider.value);
                vaporizerOutputValueDisplay.textContent = `${currentVaporizerOutput.toFixed(1)} %`;
                vaporizerDialText.textContent = `${currentVaporizerOutput.toFixed(1)}%`;
                updateGasColorsAndFlow();
            }

            function updateBreathingRate() {
                currentBreathingRate = parseInt(breathingRateSlider.value);
                breathingRateValueDisplay.textContent = `${currentBreathingRate} bpm`;
                // Animation loop will pick this up
            }
            
            function checkAlarm() {
                if (currentOxygenFlow < OXYGEN_ALARM_THRESHOLD) {
                    if (!alarmActive) {
                        alarmMessageDiv.style.display = 'block';
                        alarmActive = true;
                    }
                } else {
                    if (alarmActive) {
                        alarmMessageDiv.style.display = 'none';
                        alarmActive = false;
                    }
                }
            }

            function updateGasColorsAndFlow() {
                const pureOxygenColor = `rgb(${OXYGEN_COLOR_RGB.join(',')})`;
                
                // Paths before vaporizer get pure oxygen color
                pathRegToFlowmeter.style.stroke = pureOxygenColor;
                pathFlowmeterToVaporizer.style.stroke = pureOxygenColor;
                if (currentOxygenSource === 'pipeline') {
                    pathPipelineToReg.style.stroke = pureOxygenColor;
                } else {
                    pathCylinderToReg.style.stroke = pureOxygenColor;
                }

                // Calculate mixed gas color
                // Vaporizer output is %, so factor is output / 100. Max effect at e.g. 5% for color.
                let vaporMixFactor = 0;
                if (currentOxygenFlow > 0) { // Vapor only delivered if there's carrier gas flow
                     vaporMixFactor = Math.min(currentVaporizerOutput / 5, 1); // Max color effect at 5%
                }
                const mixedGasColor = interpolateColors(OXYGEN_COLOR_RGB, VAPOR_COLOR_RGB, vaporMixFactor);

                // Paths after vaporizer get mixed gas color
                pathVaporizerToCommon.style.stroke = mixedGasColor;
                pathCommonToCircuit.style.stroke = mixedGasColor;
                breathingCircuitBag.style.fill = mixedGasColor;
                
                // Adjust stroke width slightly for visual volume (optional)
                // const flowStrokeWidth = mapRange(currentOxygenFlow, 0, 10, 4, 12);
                // allAnimatedPaths.forEach(p => p.style.strokeWidth = `${flowStrokeWidth}px`);
                // This needs careful handling of specific path widths if they differ by design.
                // For now, using fixed widths from SVG and relying on animation speed.
            }

            // Animation Loop
            function animationLoop(timestamp) {
                if (!lastTimestamp) lastTimestamp = timestamp;
                const deltaTime = timestamp - lastTimestamp;
                lastTimestamp = timestamp;

                // Gas flow animation (stroke-dashoffset)
                if (currentOxygenFlow > 0 && !alarmActive) { // Stop flow animation if alarm is critical (e.g. flow=0)
                                                     // Or let it run very slow if alarm is just 'low'
                    dashOffset -= (currentOxygenFlow / 10) * 200 * (deltaTime / 1000); // Speed proportional to flow, normalized
                    if (dashOffset < -1000) dashOffset += 1000; // Reset to avoid large numbers, value depends on dasharray length
                    
                    allAnimatedPaths.forEach(path => {
                        if (path.style.display !== 'none') { // Only animate visible paths
                           path.style.strokeDashoffset = dashOffset;
                        }
                    });
                } else if (currentOxygenFlow === 0) {
                     allAnimatedPaths.forEach(path => {
                        if (path.style.display !== 'none') {
                           path.style.strokeDashoffset = 0; // Stop animation
                        }
                    });
                }


                // Breathing bag animation
                const period = 60000 / currentBreathingRate; // ms per breath
                const currentTimeInCycle = timestamp % period;
                // Using a sine wave for smooth inhale/exhale. Inspiration is positive phase.
                const phase = (currentTimeInCycle / period) * 2 * Math.PI;
                const breathProgress = Math.sin(phase); // -1 to 1

                let bagRy;
                if (currentOxygenFlow > 0.1) { // Minimal flow needed to make bag work
                    bagRy = BAG_BASE_RY * (1 + breathProgress * BAG_RY_VARIATION_FACTOR);
                } else {
                    // If no/low flow, bag might appear deflated or minimally moving
                    bagRy = BAG_BASE_RY * (1 - BAG_RY_VARIATION_FACTOR * 0.8); // Mostly deflated
                }
                breathingCircuitBag.setAttribute('ry', Math.max(5, bagRy)); // Ensure ry doesn't go below a minimum

                requestAnimationFrame(animationLoop);
            }

            // Event Listeners
            pipelineSourceRadio.addEventListener('change', updateOxygenSource);
            cylinderSourceRadio.addEventListener('change', updateOxygenSource);
            oxygenFlowSlider.addEventListener('input', updateOxygenFlow);
            vaporizerOutputSlider.addEventListener('input', updateVaporizerOutput);
            breathingRateSlider.addEventListener('input', updateBreathingRate);

            // Initial Setup
            updateOxygenSource();
            updateOxygenFlow();
            updateVaporizerOutput();
            updateBreathingRate();
            checkAlarm(); // Initial alarm check
            
            // Start animation
            requestAnimationFrame(animationLoop);
        });
    </script>
</body>
</html>
