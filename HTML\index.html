<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Medical Simulation LMS - Anesthesia & Patient Monitoring</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/modules.css">
    <link rel="stylesheet" href="../CSS/responsive.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app-container">
        <!-- Header Section -->
        <header class="main-header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-heartbeat logo-icon"></i>
                    <h1>Virtual Medical Simulation LMS</h1>
                    <p class="subtitle">Advanced Training in Anesthesia & Patient Monitoring</p>
                </div>
                <nav class="main-navigation">
                    <ul>
                        <li><a href="#home" class="nav-link active">Home</a></li>
                        <li><a href="#modules" class="nav-link">Modules</a></li>
                        <li><a href="lectures.html" class="nav-link">Lectures</a></li>
                        <li><a href="interactive-diagrams.html" class="nav-link">Interactive Diagrams</a></li>
                        <li><a href="#simulations" class="nav-link">Simulations</a></li>
                        <li><a href="#assessments" class="nav-link">Assessments</a></li>
                        <li><a href="#progress" class="nav-link">Progress</a></li>
                    </ul>
                </nav>
            </div>
        </header>

        <!-- Hero Section -->
        <section id="home" class="hero-section">
            <div class="hero-content">
                <h2>Master Medical Equipment Through Interactive Simulation</h2>
                <p>Comprehensive virtual training platform for healthcare professionals to learn anesthesia machines, patient monitoring systems, and ventilator operations in a safe, controlled environment.</p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <i class="fas fa-microscope"></i>
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Interactive Simulations</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-graduation-cap"></i>
                        <span class="stat-number">3</span>
                        <span class="stat-label">Core Modules</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-certificate"></i>
                        <span class="stat-number">100+</span>
                        <span class="stat-label">Assessment Questions</span>
                    </div>
                </div>
                <button class="cta-button" onclick="scrollToModules()">
                    <i class="fas fa-play"></i>
                    Start Learning
                </button>
            </div>
        </section>

        <!-- Learning Modules Section -->
        <section id="modules" class="modules-section">
            <div class="section-header">
                <h2>Learning Modules</h2>
                <p>Comprehensive training modules designed for progressive skill development</p>
            </div>

            <div class="modules-grid">
                <!-- Patient Monitoring Module -->
                <div class="module-card" data-module="patient-monitoring">
                    <div class="module-header">
                        <i class="fas fa-heartbeat module-icon"></i>
                        <h3>Patient Monitoring & Vital Signs</h3>
                        <span class="module-level">Beginner to Advanced</span>
                    </div>
                    <div class="module-content">
                        <p>Master the fundamentals of patient monitoring systems, vital signs interpretation, and emergency response protocols.</p>
                        <div class="module-features">
                            <div class="feature-item">
                                <i class="fas fa-chart-line"></i>
                                <span>Real-time Vital Signs Simulation</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Emergency Scenarios</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-brain"></i>
                                <span>Interactive Quizzes</span>
                            </div>
                        </div>
                        <div class="module-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                    <div class="module-actions">
                        <button class="btn-primary" onclick="window.location.href='patient-monitoring-module.html'">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button class="btn-secondary" onclick="window.location.href='patient-monitoring.html'">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                    </div>
                </div>

                <!-- Anesthesia Machine Module -->
                <div class="module-card" data-module="anesthesia-machine">
                    <div class="module-header">
                        <i class="fas fa-lungs module-icon"></i>
                        <h3>Anesthesia Machine Systems</h3>
                        <span class="module-level">Intermediate to Advanced</span>
                    </div>
                    <div class="module-content">
                        <p>Comprehensive training on anesthesia machine components, gas flow systems, and safety protocols.</p>
                        <div class="module-features">
                            <div class="feature-item">
                                <i class="fas fa-cogs"></i>
                                <span>SPDD Model Explorer</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-wind"></i>
                                <span>Gas Flow Dynamics</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-tools"></i>
                                <span>Component Identification</span>
                            </div>
                        </div>
                        <div class="module-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                    <div class="module-actions">
                        <button class="btn-primary" onclick="window.location.href='anesthesia-machine-module.html'">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button class="btn-secondary" onclick="window.location.href='../anesthesia_machine_landing.html'">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                    </div>
                </div>

                <!-- ECG Interpretation Module -->
                <div class="module-card" data-module="ecg-interpretation">
                    <div class="module-header">
                        <i class="fas fa-heartbeat module-icon"></i>
                        <h3>ECG Interpretation & Cardiac Rhythms</h3>
                        <span class="module-level">Advanced</span>
                    </div>
                    <div class="module-content">
                        <p>Master electrocardiogram analysis with 12-lead ECG simulation and systematic interpretation approaches.</p>
                        <div class="module-features">
                            <div class="feature-item">
                                <i class="fas fa-chart-line"></i>
                                <span>12-Lead ECG Analyzer</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-wave-square"></i>
                                <span>Rhythm Recognition</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-stethoscope"></i>
                                <span>Clinical Correlation</span>
                            </div>
                        </div>
                        <div class="module-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                    <div class="module-actions">
                        <button class="btn-primary" onclick="window.location.href='ecg-interpretation-module.html'">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button class="btn-secondary" onclick="window.location.href='interactive-diagrams.html#ecg-systems'">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                    </div>
                </div>

                <!-- Mechanical Ventilation Module -->
                <div class="module-card" data-module="mechanical-ventilation">
                    <div class="module-header">
                        <i class="fas fa-wind module-icon"></i>
                        <h3>Mechanical Ventilation & Respiratory Support</h3>
                        <span class="module-level">Expert</span>
                    </div>
                    <div class="module-content">
                        <p>Comprehensive ventilator modes, respiratory mechanics, and advanced ventilation strategies with real-time simulations.</p>
                        <div class="module-features">
                            <div class="feature-item">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>Multiple Ventilator Modes</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-lungs"></i>
                                <span>Respiratory Mechanics</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>Troubleshooting</span>
                            </div>
                        </div>
                        <div class="module-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                    <div class="module-actions">
                        <button class="btn-primary" onclick="window.location.href='mechanical-ventilation-module.html'">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button class="btn-secondary" onclick="window.location.href='../ventilators_landing.html'">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                    </div>
                </div>

                <!-- Blood Pressure Physiology Module -->
                <div class="module-card" data-module="blood-pressure-physiology">
                    <div class="module-header">
                        <i class="fas fa-tachometer-alt module-icon"></i>
                        <h3>Blood Pressure Physiology & Hemodynamics</h3>
                        <span class="module-level">Intermediate</span>
                    </div>
                    <div class="module-content">
                        <p>Understanding cardiovascular hemodynamics, measurement techniques, and pressure regulation mechanisms.</p>
                        <div class="module-features">
                            <div class="feature-item">
                                <i class="fas fa-heart"></i>
                                <span>Cardiac Cycle Animation</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-chart-area"></i>
                                <span>Pressure Waveforms</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-calculator"></i>
                                <span>Hemodynamic Calculations</span>
                            </div>
                        </div>
                        <div class="module-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                    <div class="module-actions">
                        <button class="btn-primary" onclick="window.location.href='blood-pressure-module.html'">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button class="btn-secondary" onclick="window.location.href='../Body\\'s Response to Blood Pressure Changes.html'">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                    </div>
                </div>

                <!-- Gas Flow Dynamics Module -->
                <div class="module-card" data-module="gas-flow-dynamics">
                    <div class="module-header">
                        <i class="fas fa-atom module-icon"></i>
                        <h3>Gas Flow Dynamics & Fluid Mechanics</h3>
                        <span class="module-level">Advanced</span>
                    </div>
                    <div class="module-content">
                        <p>Fluid mechanics principles, flowmeter operation, and safety systems in anesthetic gas delivery.</p>
                        <div class="module-features">
                            <div class="feature-item">
                                <i class="fas fa-wind"></i>
                                <span>Flow Visualization</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>Safety Systems</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-flask"></i>
                                <span>Virtual Laboratory</span>
                            </div>
                        </div>
                        <div class="module-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                    <div class="module-actions">
                        <button class="btn-primary" onclick="window.location.href='gas-flow-dynamics-module.html'">
                            <i class="fas fa-play"></i>
                            Start Module
                        </button>
                        <button class="btn-secondary" onclick="window.location.href='../Anesthetic Gas Dynamics Simulator.html'">
                            <i class="fas fa-info-circle"></i>
                            Details
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive Lectures Section -->
        <section id="lectures" class="lectures-section">
            <div class="section-header">
                <h2>Interactive Lectures</h2>
                <p>Comprehensive presentations with animated visual aids and interactive content</p>
            </div>

            <div class="lectures-preview">
                <div class="lecture-preview-card">
                    <div class="lecture-preview-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h4>Introduction to Vital Signs</h4>
                    <p>15-minute interactive presentation covering the fundamentals of patient monitoring</p>
                    <div class="lecture-meta">
                        <span class="slides-count"><i class="fas fa-images"></i> 12 slides</span>
                        <span class="difficulty beginner">Beginner</span>
                    </div>
                    <button type="button" class="btn-lecture-preview" onclick="window.location.href='lectures.html'">
                        <i class="fas fa-play"></i>
                        View All Lectures
                    </button>
                </div>

                <div class="lecture-preview-card">
                    <div class="lecture-preview-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4>ECG Interpretation Basics</h4>
                    <p>25-minute comprehensive guide to reading and interpreting electrocardiograms</p>
                    <div class="lecture-meta">
                        <span class="slides-count"><i class="fas fa-images"></i> 18 slides</span>
                        <span class="difficulty intermediate">Intermediate</span>
                    </div>
                    <button type="button" class="btn-lecture-preview" onclick="window.location.href='lectures.html'">
                        <i class="fas fa-play"></i>
                        View All Lectures
                    </button>
                </div>

                <div class="lecture-preview-card">
                    <div class="lecture-preview-icon">
                        <i class="fas fa-lungs"></i>
                    </div>
                    <h4>Anesthesia Machine Overview</h4>
                    <p>30-minute detailed exploration of anesthesia machine systems and SPDD model</p>
                    <div class="lecture-meta">
                        <span class="slides-count"><i class="fas fa-images"></i> 22 slides</span>
                        <span class="difficulty beginner">Beginner</span>
                    </div>
                    <button type="button" class="btn-lecture-preview" onclick="window.location.href='lectures.html'">
                        <i class="fas fa-play"></i>
                        View All Lectures
                    </button>
                </div>
            </div>

            <div class="lectures-cta">
                <button type="button" class="btn-primary" onclick="window.location.href='lectures.html'">
                    <i class="fas fa-chalkboard-teacher"></i>
                    Access All Interactive Lectures
                </button>
            </div>
        </section>

        <!-- Quick Access Simulations -->
        <section id="simulations" class="simulations-section">
            <div class="section-header">
                <h2>Interactive Simulations</h2>
                <p>Hands-on practice with virtual medical equipment</p>
            </div>

            <div class="simulations-grid">
                <div class="simulation-card" data-simulation="patient-monitor">
                    <div class="simulation-preview">
                        <i class="fas fa-tv simulation-icon"></i>
                    </div>
                    <h4>Patient Monitor Simulator</h4>
                    <p>Interactive vital signs monitoring with real-time feedback</p>
                    <button class="btn-simulation" onclick="window.location.href='../Patient Monitor Simulation .html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="anesthesia-flowmeter">
                    <div class="simulation-preview">
                        <i class="fas fa-tint simulation-icon"></i>
                    </div>
                    <h4>Anesthesia Flowmeter & Circuit</h4>
                    <p>Gas flow visualization and breathing circuit dynamics</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthesia Machine Flowmeter and Breathing Circuit.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="pneumatic-system">
                    <div class="simulation-preview">
                        <i class="fas fa-cogs simulation-icon"></i>
                    </div>
                    <h4>Pneumatic System Explorer</h4>
                    <p>Interactive component identification and system understanding</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthesia Machine Pneumatic System Explorer.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="ventilator-basics">
                    <div class="simulation-preview">
                        <i class="fas fa-lungs simulation-icon"></i>
                    </div>
                    <h4>Ventilator Basics</h4>
                    <p>Fundamental ventilator parameters and calculations</p>
                    <button class="btn-simulation" onclick="window.location.href='../Ventilator Basics.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="gas-dynamics">
                    <div class="simulation-preview">
                        <i class="fas fa-atom simulation-icon"></i>
                    </div>
                    <h4>Anesthetic Gas Dynamics</h4>
                    <p>Gas concentration changes and time constants</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthetic Gas Dynamics Simulator.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="spdd-model">
                    <div class="simulation-preview">
                        <i class="fas fa-project-diagram simulation-icon"></i>
                    </div>
                    <h4>SPDD Model Explorer</h4>
                    <p>Supply, Processing, Delivery, and Disposal systems</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthesia SPDD Model Explorer.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="anesthesia-monitor">
                    <div class="simulation-preview">
                        <i class="fas fa-desktop simulation-icon"></i>
                    </div>
                    <h4>Anesthesia Monitor Simulator</h4>
                    <p>Complete anesthesia monitoring system with vital signs</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthesia Monitor Simulator.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="anesthesia-ventilator">
                    <div class="simulation-preview">
                        <i class="fas fa-wind simulation-icon"></i>
                    </div>
                    <h4>Anesthesia Ventilator Simulator</h4>
                    <p>Advanced ventilator settings and respiratory mechanics</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthesia Ventilator Simulator.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>

                <div class="simulation-card" data-simulation="vital-signs">
                    <div class="simulation-preview">
                        <i class="fas fa-heartbeat simulation-icon"></i>
                    </div>
                    <h4>Anesthesia Vital Signs Simulator</h4>
                    <p>Real-time vital signs monitoring and interpretation</p>
                    <button class="btn-simulation" onclick="window.location.href='../Anesthesia Vital Signs Simulator.html'">
                        <i class="fas fa-external-link-alt"></i>
                        Launch Simulation
                    </button>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="main-footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Virtual Medical Simulation LMS</h4>
                    <p>Advanced training platform for healthcare professionals</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#modules">Learning Modules</a></li>
                        <li><a href="lectures.html">Interactive Lectures</a></li>
                        <li><a href="interactive-diagrams.html">Interactive Diagrams</a></li>
                        <li><a href="#simulations">Simulations</a></li>
                        <li><a href="#assessments">Assessments</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#contact">Contact Us</a></li>
                        <li><a href="#feedback">Feedback</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Virtual Medical Simulation LMS. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading Simulation...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../JS/main.js"></script>
    <script src="../JS/modules.js"></script>
    <script src="../JS/navigation.js"></script>
</body>
</html>
