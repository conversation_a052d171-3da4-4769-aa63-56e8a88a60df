// ===== ECG ANALYZER SIMULATION ===== //

// ECG State
const ecgState = {
    isRunning: false,
    currentRhythm: 'normal',
    heartRate: 75,
    settings: {
        speed: 50, // mm/s
        gain: 10,  // mm/mV
        filter: 0.5 // Hz
    },
    analysis: {
        rhythm: 'Normal Sinus Rhythm',
        rate: 75,
        prInterval: 0.16,
        qrsDuration: 0.08,
        qtInterval: 0.40,
        axis: 60
    },
    leads: {
        I: { angle: 0, amplitude: 1.0 },
        II: { angle: 60, amplitude: 1.2 },
        III: { angle: 120, amplitude: 0.8 },
        aVR: { angle: -150, amplitude: -0.6 },
        aVL: { angle: -30, amplitude: 0.4 },
        aVF: { angle: 90, amplitude: 1.0 },
        V1: { precordial: true, amplitude: 0.6 },
        V2: { precordial: true, amplitude: 1.4 },
        V3: { precordial: true, amplitude: 1.8 },
        V4: { precordial: true, amplitude: 2.0 },
        V5: { precordial: true, amplitude: 1.6 },
        V6: { precordial: true, amplitude: 1.2 }
    }
};

// Canvas contexts for each lead
const leadCanvases = {};
const leadContexts = {};

// Animation variables
let ecgAnimationId;
let ecgTimeOffset = 0;

// Rhythm patterns
const rhythmPatterns = {
    normal: {
        rate: 75,
        rhythm: 'Normal Sinus Rhythm',
        prInterval: 0.16,
        qrsDuration: 0.08,
        qtInterval: 0.40,
        regularity: 'regular',
        pWaves: true,
        qrsWidth: 'narrow'
    },
    bradycardia: {
        rate: 45,
        rhythm: 'Sinus Bradycardia',
        prInterval: 0.18,
        qrsDuration: 0.08,
        qtInterval: 0.45,
        regularity: 'regular',
        pWaves: true,
        qrsWidth: 'narrow'
    },
    tachycardia: {
        rate: 120,
        rhythm: 'Sinus Tachycardia',
        prInterval: 0.14,
        qrsDuration: 0.08,
        qtInterval: 0.35,
        regularity: 'regular',
        pWaves: true,
        qrsWidth: 'narrow'
    },
    afib: {
        rate: 85,
        rhythm: 'Atrial Fibrillation',
        prInterval: 0,
        qrsDuration: 0.08,
        qtInterval: 0.38,
        regularity: 'irregular',
        pWaves: false,
        qrsWidth: 'narrow'
    },
    vfib: {
        rate: 0,
        rhythm: 'Ventricular Fibrillation',
        prInterval: 0,
        qrsDuration: 0,
        qtInterval: 0,
        regularity: 'chaotic',
        pWaves: false,
        qrsWidth: 'wide'
    },
    asystole: {
        rate: 0,
        rhythm: 'Asystole',
        prInterval: 0,
        qrsDuration: 0,
        qtInterval: 0,
        regularity: 'none',
        pWaves: false,
        qrsWidth: 'none'
    }
};

// Initialize ECG Analyzer
function initializeECGAnalyzer() {
    console.log('Initializing ECG Analyzer...');
    
    // Get all lead canvases
    const canvases = document.querySelectorAll('.ecg-canvas');
    canvases.forEach(canvas => {
        const leadDisplay = canvas.closest('.lead-display');
        if (leadDisplay) {
            const leadName = leadDisplay.getAttribute('data-lead');
            if (leadName) {
                leadCanvases[leadName] = canvas;
                leadContexts[leadName] = canvas.getContext('2d');
            }
        }
    });
    
    // Start ECG
    startECG();
    
    // Update displays
    updateECGDisplay();
    
    console.log('ECG Analyzer initialized');
}

// Start ECG
function startECG() {
    ecgState.isRunning = true;
    animateECG();
}

// Stop ECG
function stopECG() {
    ecgState.isRunning = false;
    if (ecgAnimationId) {
        cancelAnimationFrame(ecgAnimationId);
    }
}

// Update ECG Display
function updateECGDisplay() {
    // Update heart rate display
    const hrValue = document.querySelector('.hr-value');
    if (hrValue) {
        hrValue.textContent = ecgState.heartRate;
    }
    
    // Update analysis panel
    updateAnalysisPanel();
    
    // Update status
    updateECGStatus();
}

// Update Analysis Panel
function updateAnalysisPanel() {
    const pattern = rhythmPatterns[ecgState.currentRhythm];
    
    document.getElementById('rhythmAnalysis').textContent = pattern.rhythm;
    document.getElementById('rateAnalysis').textContent = `${pattern.rate} bpm`;
    document.getElementById('prAnalysis').textContent = pattern.prInterval > 0 ? `${pattern.prInterval.toFixed(2)} sec` : 'N/A';
    document.getElementById('qrsAnalysis').textContent = pattern.qrsDuration > 0 ? `${pattern.qrsDuration.toFixed(2)} sec` : 'N/A';
    document.getElementById('qtAnalysis').textContent = pattern.qtInterval > 0 ? `${pattern.qtInterval.toFixed(2)} sec` : 'N/A';
    document.getElementById('axisAnalysis').textContent = ecgState.analysis.axis > 0 ? `Normal (${ecgState.analysis.axis}°)` : 'Indeterminate';
}

// Update ECG Status
function updateECGStatus() {
    const statusLight = document.getElementById('ecgPowerStatus');
    const statusText = document.querySelector('.ecg-status .status-text');
    
    if (statusLight && statusText) {
        if (ecgState.isRunning) {
            statusLight.classList.add('active');
            statusText.textContent = 'Recording';
        } else {
            statusLight.classList.remove('active');
            statusText.textContent = 'Standby';
        }
    }
}

// Animate ECG
function animateECG() {
    if (!ecgState.isRunning) return;
    
    ecgTimeOffset += 0.02;
    
    // Draw all leads
    Object.keys(leadContexts).forEach(leadName => {
        drawECGLead(leadName);
    });
    
    // Update heart rate with variation
    updateHeartRateVariation();
    
    ecgAnimationId = requestAnimationFrame(animateECG);
}

// Draw ECG Lead
function drawECGLead(leadName) {
    const canvas = leadCanvases[leadName];
    const ctx = leadContexts[leadName];
    
    if (!canvas || !ctx) return;
    
    const width = canvas.width;
    const height = canvas.height;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Draw grid
    drawECGGrid(ctx, width, height);
    
    // Get rhythm pattern
    const pattern = rhythmPatterns[ecgState.currentRhythm];
    
    if (pattern.rate === 0 && ecgState.currentRhythm === 'asystole') {
        // Draw flat line for asystole
        drawAsystole(ctx, width, height);
        return;
    }
    
    if (ecgState.currentRhythm === 'vfib') {
        // Draw chaotic waveform for V-Fib
        drawVFib(ctx, width, height);
        return;
    }
    
    // Draw normal ECG waveform
    drawNormalECG(ctx, width, height, leadName, pattern);
}

// Draw ECG Grid
function drawECGGrid(ctx, width, height) {
    ctx.strokeStyle = 'rgba(239, 68, 68, 0.2)';
    ctx.lineWidth = 0.5;
    
    // Large squares (5mm)
    const largeSquare = 20;
    for (let x = 0; x < width; x += largeSquare) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    for (let y = 0; y < height; y += largeSquare) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
    
    // Small squares (1mm)
    ctx.strokeStyle = 'rgba(239, 68, 68, 0.1)';
    const smallSquare = 4;
    for (let x = 0; x < width; x += smallSquare) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    for (let y = 0; y < height; y += smallSquare) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
}

// Draw Normal ECG
function drawNormalECG(ctx, width, height, leadName, pattern) {
    ctx.strokeStyle = '#00ff88';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const heartRate = pattern.rate;
    const beatInterval = 60 / heartRate; // seconds per beat
    const pixelsPerSecond = ecgState.settings.speed; // pixels per second
    const leadInfo = ecgState.leads[leadName];
    
    let irregularityFactor = 1;
    if (pattern.regularity === 'irregular') {
        irregularityFactor = 0.8 + Math.random() * 0.4; // 20% variation
    }
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + ecgTimeOffset;
        const adjustedBeatInterval = beatInterval * irregularityFactor;
        const beatPhase = (time % adjustedBeatInterval) / adjustedBeatInterval;
        
        let y = height / 2; // baseline
        let amplitude = leadInfo.amplitude;
        
        // Generate ECG waveform based on lead
        if (pattern.pWaves && beatPhase < 0.15) {
            // P wave
            const pPhase = beatPhase / 0.15;
            y += Math.sin(pPhase * Math.PI) * 8 * amplitude;
        } else if (beatPhase >= 0.2 && beatPhase < 0.35) {
            // QRS complex
            const qrsPhase = (beatPhase - 0.2) / 0.15;
            if (leadName === 'aVR') {
                // aVR typically shows inverted QRS
                y -= generateQRSWaveform(qrsPhase) * amplitude;
            } else {
                y += generateQRSWaveform(qrsPhase) * amplitude;
            }
        } else if (beatPhase >= 0.5 && beatPhase < 0.8) {
            // T wave
            const tPhase = (beatPhase - 0.5) / 0.3;
            y += Math.sin(tPhase * Math.PI) * 15 * amplitude;
        }
        
        // Add some noise for realism
        y += (Math.random() - 0.5) * 2;
        
        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    
    ctx.stroke();
}

// Generate QRS Waveform
function generateQRSWaveform(phase) {
    if (phase < 0.2) {
        // Q wave
        return -phase * 20;
    } else if (phase < 0.6) {
        // R wave
        return (phase - 0.2) * 100;
    } else {
        // S wave
        return -(phase - 0.6) * 50;
    }
}

// Draw Asystole
function drawAsystole(ctx, width, height) {
    ctx.strokeStyle = '#00ff88';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2);
    ctx.stroke();
}

// Draw V-Fib
function drawVFib(ctx, width, height) {
    ctx.strokeStyle = '#00ff88';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let x = 0; x < width; x++) {
        const time = (x / 50) + ecgTimeOffset;
        const y = height / 2 + Math.sin(time * 20) * 30 * Math.random() + 
                  Math.sin(time * 35) * 20 * Math.random() +
                  Math.sin(time * 50) * 15 * Math.random();
        
        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    
    ctx.stroke();
}

// Update Heart Rate Variation
function updateHeartRateVariation() {
    const pattern = rhythmPatterns[ecgState.currentRhythm];
    const baseRate = pattern.rate;
    
    if (pattern.regularity === 'regular') {
        // Small physiological variation
        ecgState.heartRate = Math.round(baseRate + Math.sin(ecgTimeOffset * 0.1) * 3);
    } else if (pattern.regularity === 'irregular') {
        // Larger variation for irregular rhythms
        ecgState.heartRate = Math.round(baseRate + (Math.random() - 0.5) * 20);
    } else {
        ecgState.heartRate = baseRate;
    }
    
    // Update display
    const hrValue = document.querySelector('.hr-value');
    if (hrValue) {
        hrValue.textContent = ecgState.heartRate;
    }
}

// Select Rhythm
function selectRhythm(rhythm) {
    ecgState.currentRhythm = rhythm;
    
    // Update button states
    const rhythmButtons = document.querySelectorAll('.rhythm-btn');
    rhythmButtons.forEach(btn => {
        btn.classList.remove('active');
    });
    
    const activeButton = document.querySelector(`[onclick="selectRhythm('${rhythm}')"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
    
    // Update analysis
    updateAnalysisPanel();
    
    // Show rhythm change message
    showRhythmMessage(rhythmPatterns[rhythm].rhythm);
}

// Show Rhythm Message
function showRhythmMessage(rhythmName) {
    let messageDiv = document.getElementById('rhythmMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'rhythmMessage';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--module-gradient-primary);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = `Rhythm Changed: ${rhythmName}`;
    messageDiv.style.display = 'block';
    
    // Hide after 3 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.display = 'none';
        }
    }, 3000);
}

// Analysis Tools
function measureInterval() {
    showToolMessage('Measurement tool activated - Click and drag on ECG to measure intervals');
}

function calculateRate() {
    const pattern = rhythmPatterns[ecgState.currentRhythm];
    showToolMessage(`Heart Rate: ${pattern.rate} bpm (calculated using 300/large squares method)`);
}

function showCaliper() {
    showToolMessage('Digital calipers activated - Use to measure precise intervals');
}

function printECG() {
    showToolMessage('ECG report generated - 12-lead ECG with analysis');
}

// Show Tool Message
function showToolMessage(message) {
    let messageDiv = document.getElementById('toolMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'toolMessage';
        messageDiv.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--module-gradient-secondary);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 500;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 20px rgba(139, 92, 246, 0.4);
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    
    // Hide after 4 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.display = 'none';
        }
    }, 4000);
}

// Settings Functions
function changeSpeed(speed) {
    ecgState.settings.speed = parseInt(speed);
    showToolMessage(`Paper speed changed to ${speed} mm/s`);
}

function changeGain(gain) {
    ecgState.settings.gain = parseInt(gain);
    showToolMessage(`Gain changed to ${gain} mm/mV`);
}

function changeFilter(filter) {
    ecgState.settings.filter = parseFloat(filter);
    showToolMessage(`Filter changed to ${filter}-150 Hz`);
}

// Start ECG Animations
function startECGAnimations() {
    // Animate heart rate display
    const hrDisplay = document.querySelector('.heart-rate-display');
    if (hrDisplay) {
        hrDisplay.style.animation = 'heartbeatPulse 1s ease-in-out infinite';
    }
}

// Export functions for global access
window.initializeECGAnalyzer = initializeECGAnalyzer;
window.selectRhythm = selectRhythm;
window.measureInterval = measureInterval;
window.calculateRate = calculateRate;
window.showCaliper = showCaliper;
window.printECG = printECG;
window.changeSpeed = changeSpeed;
window.changeGain = changeGain;
window.changeFilter = changeFilter;
window.startECGAnimations = startECGAnimations;
