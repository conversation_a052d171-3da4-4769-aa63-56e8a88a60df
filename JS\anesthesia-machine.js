// ===== ANESTHESIA MACHINE SIMULATION ===== //

// Machine State
const machineState = {
    isRunning: false,
    gasFlows: {
        o2: { flow: 3.0, pressure: 50 },
        n2o: { flow: 2.0, pressure: 50 },
        air: { flow: 1.5, pressure: 50 }
    },
    vaporizer: {
        agent: 'sevoflurane',
        concentration: 1.5,
        enabled: true
    },
    ventilator: {
        mode: 'VCV',
        tidalVolume: 500,
        respiratoryRate: 12,
        peep: 5,
        fio2: 50,
        inspiratoryTime: 1.0,
        pressure: 0,
        flow: 0
    },
    alarms: {
        enabled: true,
        gasSupply: true,
        ventilator: true,
        anesthesia: true
    },
    safety: {
        emergencyO2: false,
        gasShutoff: false,
        hypoxicGuard: true
    }
};

// Canvas contexts for waveforms
let pressureCanvas, flowCanvas;
let pressureCtx, flowCtx;

// Animation variables
let machineAnimationId;
let ventCycleTime = 0;

// Initialize Anesthesia Machine
function initializeAnesthesiaMachine() {
    console.log('Initializing Anesthesia Machine...');
    
    // Get canvas elements
    pressureCanvas = document.getElementById('pressureWaveform');
    flowCanvas = document.getElementById('flowWaveform');
    
    if (pressureCanvas) pressureCtx = pressureCanvas.getContext('2d');
    if (flowCanvas) flowCtx = flowCanvas.getContext('2d');
    
    // Start machine
    startMachine();
    
    // Update displays
    updateMachineDisplay();
    
    console.log('Anesthesia Machine initialized');
}

// Start Machine
function startMachine() {
    machineState.isRunning = true;
    animateMachine();
}

// Stop Machine
function stopMachine() {
    machineState.isRunning = false;
    if (machineAnimationId) {
        cancelAnimationFrame(machineAnimationId);
    }
}

// Update Machine Display
function updateMachineDisplay() {
    // Update gas flow values
    updateFlowmeters();
    
    // Update vaporizer
    updateVaporizer();
    
    // Update ventilator parameters
    updateVentilatorDisplay();
    
    // Update safety status
    updateSafetyStatus();
}

// Update Flowmeters
function updateFlowmeters() {
    const gases = ['o2', 'n2o', 'air'];
    
    gases.forEach(gas => {
        const flowmeter = document.querySelector(`[data-gas="${gas}"]`);
        if (flowmeter) {
            const indicator = flowmeter.querySelector('.flow-indicator');
            const valueDisplay = flowmeter.querySelector('.flow-value');
            
            if (indicator && valueDisplay) {
                const flow = machineState.gasFlows[gas].flow;
                const percentage = Math.min((flow / 10) * 100, 100);
                indicator.style.bottom = `${percentage}%`;
                valueDisplay.textContent = `${flow.toFixed(1)} L/min`;
                
                // Add flow animation
                indicator.style.animation = 'flowBubble 1s ease-in-out infinite';
            }
        }
    });
}

// Update Vaporizer
function updateVaporizer() {
    const dialPointer = document.querySelector('.dial-pointer');
    const concentrationDisplay = document.querySelector('.concentration-display');
    
    if (dialPointer && concentrationDisplay) {
        const concentration = machineState.vaporizer.concentration;
        const angle = (concentration / 5) * 150; // 0-5% maps to 0-150 degrees
        dialPointer.style.transform = `rotate(${angle}deg)`;
        concentrationDisplay.textContent = `${concentration.toFixed(1)}%`;
    }
}

// Update Ventilator Display
function updateVentilatorDisplay() {
    const parameters = document.querySelectorAll('.parameter .value');
    
    if (parameters.length >= 4) {
        parameters[0].textContent = `${machineState.ventilator.tidalVolume} mL`;
        parameters[1].textContent = `${machineState.ventilator.respiratoryRate} /min`;
        parameters[2].textContent = `${machineState.ventilator.peep} cmH₂O`;
        parameters[3].textContent = `${machineState.ventilator.fio2}%`;
    }
}

// Update Safety Status
function updateSafetyStatus() {
    const statusLight = document.getElementById('powerStatus');
    const statusText = document.querySelector('.status-text');
    
    if (statusLight && statusText) {
        if (machineState.isRunning && !machineState.safety.gasShutoff) {
            statusLight.classList.add('active');
            statusText.textContent = 'System Ready';
        } else {
            statusLight.classList.remove('active');
            statusText.textContent = 'System Standby';
        }
    }
}

// Animate Machine
function animateMachine() {
    if (!machineState.isRunning) return;
    
    ventCycleTime += 0.02;
    
    // Draw ventilator waveforms
    drawPressureWaveform();
    drawFlowWaveform();
    
    // Update ventilator cycle
    updateVentilatorCycle();
    
    machineAnimationId = requestAnimationFrame(animateMachine);
}

// Draw Pressure Waveform
function drawPressureWaveform() {
    if (!pressureCtx || !pressureCanvas) return;
    
    const width = pressureCanvas.width;
    const height = pressureCanvas.height;
    
    // Clear canvas
    pressureCtx.clearRect(0, 0, width, height);
    
    // Set up drawing
    pressureCtx.strokeStyle = '#3b82f6';
    pressureCtx.lineWidth = 2;
    pressureCtx.beginPath();
    
    const rr = machineState.ventilator.respiratoryRate;
    const cycleTime = 60 / rr; // seconds per breath
    const pixelsPerSecond = width / 20; // 20 seconds visible
    
    const peep = machineState.ventilator.peep;
    const pip = peep + 15; // Peak inspiratory pressure
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + ventCycleTime;
        const cyclePhase = (time % cycleTime) / cycleTime;
        
        let pressure;
        if (cyclePhase < 0.33) {
            // Inspiration
            pressure = peep + (pip - peep) * Math.sin(cyclePhase * Math.PI / 0.33);
        } else if (cyclePhase < 0.4) {
            // Plateau
            pressure = pip;
        } else {
            // Expiration
            pressure = peep + (pip - peep) * Math.exp(-(cyclePhase - 0.4) * 8);
        }
        
        const y = height - (pressure / 30) * height;
        
        if (x === 0) {
            pressureCtx.moveTo(x, y);
        } else {
            pressureCtx.lineTo(x, y);
        }
    }
    
    pressureCtx.stroke();
    
    // Draw grid
    drawWaveformGrid(pressureCtx, width, height, '#3b82f6');
}

// Draw Flow Waveform
function drawFlowWaveform() {
    if (!flowCtx || !flowCanvas) return;
    
    const width = flowCanvas.width;
    const height = flowCanvas.height;
    
    // Clear canvas
    flowCtx.clearRect(0, 0, width, height);
    
    // Set up drawing
    flowCtx.strokeStyle = '#10b981';
    flowCtx.lineWidth = 2;
    flowCtx.beginPath();
    
    const rr = machineState.ventilator.respiratoryRate;
    const cycleTime = 60 / rr;
    const pixelsPerSecond = width / 20;
    
    const peakFlow = 60; // L/min
    
    for (let x = 0; x < width; x++) {
        const time = (x / pixelsPerSecond) + ventCycleTime;
        const cyclePhase = (time % cycleTime) / cycleTime;
        
        let flow;
        if (cyclePhase < 0.33) {
            // Inspiratory flow
            flow = peakFlow * Math.sin(cyclePhase * Math.PI / 0.33);
        } else if (cyclePhase < 0.4) {
            // End inspiration
            flow = 0;
        } else {
            // Expiratory flow
            flow = -peakFlow * 0.6 * Math.exp(-(cyclePhase - 0.4) * 6);
        }
        
        const y = height / 2 - (flow / 80) * height;
        
        if (x === 0) {
            flowCtx.moveTo(x, y);
        } else {
            flowCtx.lineTo(x, y);
        }
    }
    
    flowCtx.stroke();
    
    // Draw zero line
    flowCtx.strokeStyle = '#64748b';
    flowCtx.lineWidth = 1;
    flowCtx.beginPath();
    flowCtx.moveTo(0, height / 2);
    flowCtx.lineTo(width, height / 2);
    flowCtx.stroke();
    
    // Draw grid
    drawWaveformGrid(flowCtx, width, height, '#10b981');
}

// Draw Waveform Grid
function drawWaveformGrid(ctx, width, height, color) {
    ctx.strokeStyle = color;
    ctx.globalAlpha = 0.1;
    ctx.lineWidth = 0.5;
    
    // Vertical lines
    for (let x = 0; x < width; x += 40) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
    }
    
    // Horizontal lines
    for (let y = 0; y < height; y += 20) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
    }
    
    ctx.globalAlpha = 1;
}

// Update Ventilator Cycle
function updateVentilatorCycle() {
    const rr = machineState.ventilator.respiratoryRate;
    const cycleTime = 60 / rr;
    const cyclePhase = (ventCycleTime % cycleTime) / cycleTime;
    
    if (cyclePhase < 0.33) {
        // Inspiration
        machineState.ventilator.pressure = machineState.ventilator.peep + 15;
        machineState.ventilator.flow = 60;
    } else {
        // Expiration
        machineState.ventilator.pressure = machineState.ventilator.peep;
        machineState.ventilator.flow = -30;
    }
}

// Adjust Gas Flow
function adjustFlow(gas, direction) {
    const step = 0.5;
    const current = machineState.gasFlows[gas].flow;
    
    if (direction === 'up' && current < 10) {
        machineState.gasFlows[gas].flow = Math.min(current + step, 10);
    } else if (direction === 'down' && current > 0) {
        machineState.gasFlows[gas].flow = Math.max(current - step, 0);
    }
    
    // Apply safety checks
    applySafetyChecks();
    
    // Update display
    updateFlowmeters();
}

// Adjust Ventilator
function adjustVentilator(parameter, direction) {
    const step = parameter === 'tv' ? 50 : 1;
    
    if (parameter === 'tv') {
        const current = machineState.ventilator.tidalVolume;
        if (direction === 'up' && current < 1000) {
            machineState.ventilator.tidalVolume = Math.min(current + step, 1000);
        } else if (direction === 'down' && current > 200) {
            machineState.ventilator.tidalVolume = Math.max(current - step, 200);
        }
    } else if (parameter === 'rr') {
        const current = machineState.ventilator.respiratoryRate;
        if (direction === 'up' && current < 30) {
            machineState.ventilator.respiratoryRate = Math.min(current + step, 30);
        } else if (direction === 'down' && current > 6) {
            machineState.ventilator.respiratoryRate = Math.max(current - step, 6);
        }
    }
    
    updateVentilatorDisplay();
}

// Apply Safety Checks
function applySafetyChecks() {
    const o2Flow = machineState.gasFlows.o2.flow;
    const n2oFlow = machineState.gasFlows.n2o.flow;
    const totalFlow = o2Flow + n2oFlow + machineState.gasFlows.air.flow;
    
    // Hypoxic guard - ensure minimum 25% O2
    if (totalFlow > 0) {
        const o2Percentage = (o2Flow / totalFlow) * 100;
        if (o2Percentage < 25 && machineState.safety.hypoxicGuard) {
            // Automatically adjust flows to maintain 25% O2
            const requiredO2 = totalFlow * 0.25;
            if (o2Flow < requiredO2) {
                machineState.gasFlows.o2.flow = requiredO2;
                machineState.gasFlows.n2o.flow = Math.max(0, totalFlow - requiredO2 - machineState.gasFlows.air.flow);
            }
        }
    }
    
    // Update FiO2
    if (totalFlow > 0) {
        machineState.ventilator.fio2 = Math.round((o2Flow / totalFlow) * 100);
    }
}

// Emergency Oxygen
function emergencyO2() {
    machineState.safety.emergencyO2 = true;
    machineState.gasFlows.o2.flow = 10;
    machineState.gasFlows.n2o.flow = 0;
    machineState.gasFlows.air.flow = 0;
    machineState.ventilator.fio2 = 100;
    
    updateMachineDisplay();
    
    // Show emergency message
    showEmergencyMessage('Emergency O₂ Activated - 100% Oxygen Flow');
    
    // Reset after 30 seconds
    setTimeout(() => {
        machineState.safety.emergencyO2 = false;
        showEmergencyMessage('Emergency O₂ Deactivated');
    }, 30000);
}

// Gas Shutoff
function gasShutoff() {
    machineState.safety.gasShutoff = !machineState.safety.gasShutoff;
    
    if (machineState.safety.gasShutoff) {
        machineState.gasFlows.o2.flow = 0;
        machineState.gasFlows.n2o.flow = 0;
        machineState.gasFlows.air.flow = 0;
        showEmergencyMessage('Gas Supply Shut Off');
    } else {
        machineState.gasFlows.o2.flow = 3.0;
        machineState.gasFlows.n2o.flow = 2.0;
        machineState.gasFlows.air.flow = 1.5;
        showEmergencyMessage('Gas Supply Restored');
    }
    
    updateMachineDisplay();
}

// Show Emergency Message
function showEmergencyMessage(message) {
    // Create or update emergency message display
    let messageDiv = document.getElementById('emergencyMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'emergencyMessage';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.4);
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = message;
    messageDiv.style.display = 'block';
    
    // Hide after 5 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.display = 'none';
        }
    }, 5000);
}

// Start Machine Animations
function startMachineAnimations() {
    // Animate flowmeter bubbles
    const flowIndicators = document.querySelectorAll('.flow-indicator');
    flowIndicators.forEach((indicator, index) => {
        indicator.style.animationDelay = `${index * 0.2}s`;
    });
    
    // Animate vaporizer dial
    const dialPointer = document.querySelector('.dial-pointer');
    if (dialPointer) {
        dialPointer.style.transition = 'transform 0.3s ease';
    }
}

// Export functions for global access
window.initializeAnesthesiaMachine = initializeAnesthesiaMachine;
window.adjustFlow = adjustFlow;
window.adjustVentilator = adjustVentilator;
window.emergencyO2 = emergencyO2;
window.gasShutoff = gasShutoff;
window.startMachineAnimations = startMachineAnimations;
