// ===== GAS FLOW SIMULATOR ===== //

// Gas Flow State
const gasFlowState = {
    isRunning: true,
    flows: {
        oxygen: 3.0,      // L/min
        nitrous: 2.0,     // L/min
        air: 1.0          // L/min
    },
    maxFlows: {
        oxygen: 15.0,
        nitrous: 15.0,
        air: 15.0
    },
    safetySettings: {
        hypoxicGuard: true,
        proportioning: true,
        minimumO2: 25  // percentage
    },
    environment: {
        temperature: 22,  // Celsius
        pressure: 101,    // kPa
        humidity: 50      // percentage
    },
    flowType: 'laminar',
    reynoldsNumber: 1200
};

// Canvas context
let flowVisualizationCanvas, flowVisualizationCtx;

// Animation variables
let flowAnimationId;
let flowTimeOffset = 0;

// Flow scenarios
const flowScenarios = {
    normal: {
        oxygen: 3.0,
        nitrous: 2.0,
        air: 1.0,
        name: 'Normal Operating Flow'
    },
    turbulent: {
        oxygen: 8.0,
        nitrous: 6.0,
        air: 4.0,
        name: 'High Flow - Turbulent'
    },
    obstruction: {
        oxygen: 1.5,
        nitrous: 1.0,
        air: 0.5,
        name: 'Partial Obstruction'
    },
    leak: {
        oxygen: 5.0,
        nitrous: 3.0,
        air: 2.0,
        name: 'System Leak Detected'
    }
};

// Initialize Gas Flow Simulator
function initializeGasFlowSimulator() {
    console.log('Initializing Gas Flow Simulator...');
    
    // Get canvas element
    flowVisualizationCanvas = document.getElementById('flowVisualization');
    if (flowVisualizationCanvas) {
        flowVisualizationCtx = flowVisualizationCanvas.getContext('2d');
    }
    
    // Update displays
    updateFlowDisplays();
    updateGasMixture();
    updateSafetyIndicators();
    
    // Start animation
    animateFlowSystem();
    
    console.log('Gas Flow Simulator initialized');
}

// Update Flow Displays
function updateFlowDisplays() {
    // Update flowmeter readings
    document.getElementById('oxygenFlow').textContent = `${gasFlowState.flows.oxygen.toFixed(1)} L/min`;
    document.getElementById('nitrousFlow').textContent = `${gasFlowState.flows.nitrous.toFixed(1)} L/min`;
    document.getElementById('airFlow').textContent = `${gasFlowState.flows.air.toFixed(1)} L/min`;
    
    // Update flowmeter indicators
    updateFlowmeterIndicators();
    
    // Update flow parameters
    updateFlowParameters();
}

// Update Flowmeter Indicators
function updateFlowmeterIndicators() {
    const gases = ['oxygen', 'nitrous', 'air'];
    
    gases.forEach(gas => {
        const indicator = document.getElementById(`${gas}Indicator`);
        if (indicator) {
            const flow = gasFlowState.flows[gas];
            const maxFlow = gasFlowState.maxFlows[gas];
            const percentage = (flow / maxFlow) * 80; // 80% of tube height
            indicator.style.bottom = `${20 + percentage}%`;
        }
    });
}

// Update Gas Mixture
function updateGasMixture() {
    const totalFlow = gasFlowState.flows.oxygen + gasFlowState.flows.nitrous + gasFlowState.flows.air;
    
    if (totalFlow > 0) {
        const oxygenPercent = Math.round((gasFlowState.flows.oxygen / totalFlow) * 100);
        const nitrousPercent = Math.round((gasFlowState.flows.nitrous / totalFlow) * 100);
        const airPercent = Math.round((gasFlowState.flows.air / totalFlow) * 100);
        
        document.getElementById('oxygenPercentage').textContent = `${oxygenPercent}%`;
        document.getElementById('nitrousPercentage').textContent = `${nitrousPercent}%`;
        document.getElementById('airPercentage').textContent = `${airPercent}%`;
        document.getElementById('totalFlow').textContent = `${totalFlow.toFixed(1)} L/min`;
        
        // Check safety limits
        checkSafetyLimits(oxygenPercent);
    }
}

// Check Safety Limits
function checkSafetyLimits(oxygenPercent) {
    const hypoxicGuard = document.getElementById('hypoxicGuard');
    const proportioningSystem = document.getElementById('proportioningSystem');
    
    if (gasFlowState.safetySettings.hypoxicGuard) {
        if (oxygenPercent < gasFlowState.safetySettings.minimumO2) {
            // Activate hypoxic guard
            if (hypoxicGuard) {
                hypoxicGuard.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>Hypoxic Guard: ALARM</span>';
                hypoxicGuard.style.color = '#ef4444';
            }
            
            // Automatically adjust flows
            adjustFlowsForSafety();
        } else {
            if (hypoxicGuard) {
                hypoxicGuard.innerHTML = '<i class="fas fa-shield-alt"></i><span>Hypoxic Guard: Active</span>';
                hypoxicGuard.style.color = '#22c55e';
            }
        }
    }
}

// Adjust Flows for Safety
function adjustFlowsForSafety() {
    if (gasFlowState.safetySettings.hypoxicGuard) {
        const totalFlow = gasFlowState.flows.oxygen + gasFlowState.flows.nitrous + gasFlowState.flows.air;
        const requiredO2Flow = (totalFlow * gasFlowState.safetySettings.minimumO2) / 100;
        
        if (gasFlowState.flows.oxygen < requiredO2Flow) {
            // Reduce N2O flow to maintain minimum O2
            const excessN2O = requiredO2Flow - gasFlowState.flows.oxygen;
            gasFlowState.flows.nitrous = Math.max(0, gasFlowState.flows.nitrous - excessN2O);
            gasFlowState.flows.oxygen = requiredO2Flow;
            
            updateFlowDisplays();
            updateGasMixture();
            
            showFlowMessage('Safety system activated: Flows adjusted to maintain minimum O₂');
        }
    }
}

// Update Safety Indicators
function updateSafetyIndicators() {
    const hypoxicGuard = document.getElementById('hypoxicGuard');
    const proportioningSystem = document.getElementById('proportioningSystem');
    
    if (hypoxicGuard) {
        hypoxicGuard.innerHTML = gasFlowState.safetySettings.hypoxicGuard ? 
            '<i class="fas fa-shield-alt"></i><span>Hypoxic Guard: Active</span>' :
            '<i class="fas fa-shield-alt"></i><span>Hypoxic Guard: Disabled</span>';
    }
    
    if (proportioningSystem) {
        proportioningSystem.innerHTML = gasFlowState.safetySettings.proportioning ? 
            '<i class="fas fa-balance-scale"></i><span>Link-25: Engaged</span>' :
            '<i class="fas fa-balance-scale"></i><span>Link-25: Disabled</span>';
    }
}

// Update Flow Parameters
function updateFlowParameters() {
    // Calculate Reynolds number
    const velocity = 2.0; // m/s (example)
    const diameter = 0.01; // m (10mm)
    const density = 1.2; // kg/m³
    const viscosity = 0.000018; // Pa·s
    
    gasFlowState.reynoldsNumber = Math.round((density * velocity * diameter) / viscosity);
    
    // Determine flow type
    if (gasFlowState.reynoldsNumber < 2000) {
        gasFlowState.flowType = 'Laminar';
    } else if (gasFlowState.reynoldsNumber > 4000) {
        gasFlowState.flowType = 'Turbulent';
    } else {
        gasFlowState.flowType = 'Transitional';
    }
    
    // Update display
    document.getElementById('reynoldsNumber').textContent = gasFlowState.reynoldsNumber;
    document.getElementById('flowType').textContent = gasFlowState.flowType;
    document.getElementById('currentFlowType').textContent = gasFlowState.flowType;
    
    // Calculate pressure drop
    const pressureDrop = calculatePressureDrop();
    document.getElementById('pressureDrop').textContent = `${pressureDrop.toFixed(1)} cmH₂O`;
}

// Calculate Pressure Drop
function calculatePressureDrop() {
    const totalFlow = gasFlowState.flows.oxygen + gasFlowState.flows.nitrous + gasFlowState.flows.air;
    const length = 0.5; // m
    const diameter = 0.01; // m
    const viscosity = 0.000018; // Pa·s
    
    // Poiseuille's law for laminar flow
    if (gasFlowState.flowType === 'Laminar') {
        const flowRate = totalFlow / 60000; // Convert L/min to m³/s
        const radius = diameter / 2;
        return (8 * viscosity * length * flowRate) / (Math.PI * Math.pow(radius, 4)) / 98.07; // Convert Pa to cmH2O
    } else {
        // Turbulent flow approximation
        return totalFlow * 0.5; // Simplified calculation
    }
}

// Animate Flow System
function animateFlowSystem() {
    flowTimeOffset += 0.02;
    
    // Draw flow visualization
    drawFlowVisualization();
    
    flowAnimationId = requestAnimationFrame(animateFlowSystem);
}

// Draw Flow Visualization
function drawFlowVisualization() {
    if (!flowVisualizationCtx || !flowVisualizationCanvas) return;
    
    const width = flowVisualizationCanvas.width;
    const height = flowVisualizationCanvas.height;
    
    // Clear canvas
    flowVisualizationCtx.clearRect(0, 0, width, height);
    
    // Draw tube
    flowVisualizationCtx.strokeStyle = '#64748b';
    flowVisualizationCtx.lineWidth = 3;
    flowVisualizationCtx.beginPath();
    flowVisualizationCtx.rect(50, height/2 - 30, width - 100, 60);
    flowVisualizationCtx.stroke();
    
    // Draw flow pattern
    if (gasFlowState.flowType === 'Laminar') {
        drawLaminarFlow(width, height);
    } else {
        drawTurbulentFlow(width, height);
    }
    
    // Draw flow direction arrows
    drawFlowArrows(width, height);
}

// Draw Laminar Flow
function drawLaminarFlow(width, height) {
    flowVisualizationCtx.strokeStyle = '#3b82f6';
    flowVisualizationCtx.lineWidth = 2;
    
    const centerY = height / 2;
    const tubeRadius = 30;
    
    // Draw parabolic velocity profile
    for (let i = 0; i < 5; i++) {
        const y = centerY - tubeRadius + (i * tubeRadius / 2);
        const distanceFromCenter = Math.abs(y - centerY);
        const velocity = 1 - Math.pow(distanceFromCenter / tubeRadius, 2);
        
        flowVisualizationCtx.beginPath();
        for (let x = 60; x < width - 60; x += 20) {
            const flowX = x + velocity * 15 * Math.sin(flowTimeOffset * 2 + x * 0.1);
            if (x === 60) {
                flowVisualizationCtx.moveTo(flowX, y);
            } else {
                flowVisualizationCtx.lineTo(flowX, y);
            }
        }
        flowVisualizationCtx.stroke();
    }
}

// Draw Turbulent Flow
function drawTurbulentFlow(width, height) {
    flowVisualizationCtx.strokeStyle = '#ef4444';
    flowVisualizationCtx.lineWidth = 1.5;
    
    const centerY = height / 2;
    
    // Draw chaotic flow lines
    for (let i = 0; i < 8; i++) {
        const y = centerY - 25 + (i * 50 / 7);
        
        flowVisualizationCtx.beginPath();
        for (let x = 60; x < width - 60; x += 10) {
            const turbulence = Math.sin(flowTimeOffset * 5 + x * 0.2 + i) * 10;
            const flowY = y + turbulence;
            
            if (x === 60) {
                flowVisualizationCtx.moveTo(x, flowY);
            } else {
                flowVisualizationCtx.lineTo(x, flowY);
            }
        }
        flowVisualizationCtx.stroke();
    }
}

// Draw Flow Arrows
function drawFlowArrows(width, height) {
    flowVisualizationCtx.fillStyle = '#10b981';
    flowVisualizationCtx.strokeStyle = '#10b981';
    flowVisualizationCtx.lineWidth = 2;
    
    const arrowY = height / 2;
    const arrowSpacing = 100;
    
    for (let x = 100; x < width - 100; x += arrowSpacing) {
        // Arrow shaft
        flowVisualizationCtx.beginPath();
        flowVisualizationCtx.moveTo(x, arrowY);
        flowVisualizationCtx.lineTo(x + 30, arrowY);
        flowVisualizationCtx.stroke();
        
        // Arrow head
        flowVisualizationCtx.beginPath();
        flowVisualizationCtx.moveTo(x + 30, arrowY);
        flowVisualizationCtx.lineTo(x + 25, arrowY - 5);
        flowVisualizationCtx.lineTo(x + 25, arrowY + 5);
        flowVisualizationCtx.closePath();
        flowVisualizationCtx.fill();
    }
}

// Adjust Flow
function adjustFlow(gas, direction) {
    const increment = 0.5;
    const currentFlow = gasFlowState.flows[gas];
    const maxFlow = gasFlowState.maxFlows[gas];
    
    if (direction === 'up' && currentFlow < maxFlow) {
        gasFlowState.flows[gas] = Math.min(maxFlow, currentFlow + increment);
    } else if (direction === 'down' && currentFlow > 0) {
        gasFlowState.flows[gas] = Math.max(0, currentFlow - increment);
    }
    
    // Apply proportioning if enabled
    if (gasFlowState.safetySettings.proportioning && gas === 'nitrous') {
        applyProportioning();
    }
    
    updateFlowDisplays();
    updateGasMixture();
    updateFlowParameters();
}

// Apply Proportioning (Link-25)
function applyProportioning() {
    const n2oFlow = gasFlowState.flows.nitrous;
    const requiredO2 = n2oFlow * 0.25; // 25% minimum O2
    
    if (gasFlowState.flows.oxygen < requiredO2) {
        gasFlowState.flows.oxygen = requiredO2;
        showFlowMessage('Link-25 activated: O₂ flow increased to maintain 25% minimum');
    }
}

// Load Flow Scenario
function loadFlowScenario(scenario) {
    const scenarioData = flowScenarios[scenario];
    if (scenarioData) {
        gasFlowState.flows.oxygen = scenarioData.oxygen;
        gasFlowState.flows.nitrous = scenarioData.nitrous;
        gasFlowState.flows.air = scenarioData.air;
        
        updateFlowDisplays();
        updateGasMixture();
        updateFlowParameters();
        
        showFlowMessage(`Loaded scenario: ${scenarioData.name}`);
    }
}

// Toggle Safety Systems
function toggleHypoxicGuard() {
    gasFlowState.safetySettings.hypoxicGuard = !gasFlowState.safetySettings.hypoxicGuard;
    updateSafetyIndicators();
    
    const status = gasFlowState.safetySettings.hypoxicGuard ? 'ON' : 'OFF';
    document.getElementById('hypoxicGuardStatus').textContent = `Hypoxic Guard: ${status}`;
    
    showFlowMessage(`Hypoxic Guard: ${status}`);
}

function toggleProportioning() {
    gasFlowState.safetySettings.proportioning = !gasFlowState.safetySettings.proportioning;
    updateSafetyIndicators();
    
    const status = gasFlowState.safetySettings.proportioning ? 'ON' : 'OFF';
    document.getElementById('proportioningStatus').textContent = `Link-25: ${status}`;
    
    showFlowMessage(`Link-25 Proportioning: ${status}`);
}

// Emergency Shutoff
function emergencyShutoff() {
    gasFlowState.flows.oxygen = 0;
    gasFlowState.flows.nitrous = 0;
    gasFlowState.flows.air = 0;
    
    updateFlowDisplays();
    updateGasMixture();
    updateFlowParameters();
    
    showFlowMessage('EMERGENCY SHUTOFF ACTIVATED - All gas flows stopped');
}

// Environmental Controls
function setTemperature(temp) {
    gasFlowState.environment.temperature = parseInt(temp);
    document.getElementById('temperatureValue').textContent = `${temp}°C`;
    updateFlowParameters();
}

function setPressure(pressure) {
    gasFlowState.environment.pressure = parseInt(pressure);
    document.getElementById('pressureValue').textContent = `${pressure} kPa`;
    updateFlowParameters();
}

function setHumidity(humidity) {
    gasFlowState.environment.humidity = parseInt(humidity);
    document.getElementById('humidityValue').textContent = `${humidity}%`;
    updateFlowParameters();
}

// Poiseuille's Law Calculator
function calculatePoiseuille() {
    const radius = parseFloat(document.getElementById('tubeRadius').value) / 1000; // Convert mm to m
    const length = parseFloat(document.getElementById('tubeLength').value) / 100; // Convert cm to m
    const pressureGradient = parseFloat(document.getElementById('pressureGradient').value) * 98.07; // Convert cmH2O to Pa
    const viscosity = 0.000018; // Pa·s for air
    
    // Poiseuille's law: Q = (π * r^4 * ΔP) / (8 * μ * L)
    const flowRate = (Math.PI * Math.pow(radius, 4) * pressureGradient) / (8 * viscosity * length);
    const flowRateLMin = flowRate * 60000; // Convert m³/s to L/min
    
    document.getElementById('calculatedFlow').textContent = `${flowRateLMin.toFixed(1)} L/min`;
    
    // Update display values
    document.getElementById('radiusValue').textContent = `${document.getElementById('tubeRadius').value} mm`;
    document.getElementById('lengthValue').textContent = `${document.getElementById('tubeLength').value} cm`;
    document.getElementById('gradientValue').textContent = `${document.getElementById('pressureGradient').value} cmH₂O`;
}

// Show Flow Message
function showFlowMessage(message) {
    let messageDiv = document.getElementById('flowMessage');
    if (!messageDiv) {
        messageDiv = document.createElement('div');
        messageDiv.id = 'flowMessage';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--module-gradient-accent);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(6, 182, 212, 0.4);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        document.body.appendChild(messageDiv);
    }
    
    messageDiv.textContent = message;
    messageDiv.style.transform = 'translateX(0)';
    
    // Hide after 4 seconds
    setTimeout(() => {
        if (messageDiv) {
            messageDiv.style.transform = 'translateX(100%)';
        }
    }, 4000);
}

// Start Flow Animations
function startFlowAnimations() {
    // Animate status indicators
    const statusLight = document.getElementById('flowPowerStatus');
    if (statusLight) {
        statusLight.style.animation = 'pulse 2s ease-in-out infinite';
    }
}

// Export functions for global access
window.initializeGasFlowSimulator = initializeGasFlowSimulator;
window.adjustFlow = adjustFlow;
window.loadFlowScenario = loadFlowScenario;
window.toggleHypoxicGuard = toggleHypoxicGuard;
window.toggleProportioning = toggleProportioning;
window.emergencyShutoff = emergencyShutoff;
window.setTemperature = setTemperature;
window.setPressure = setPressure;
window.setHumidity = setHumidity;
window.calculatePoiseuille = calculatePoiseuille;
window.startFlowAnimations = startFlowAnimations;
