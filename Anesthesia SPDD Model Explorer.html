<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive SPDD Model</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .spdd-diagram {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background-color: #e9ecef;
        }

        .spdd-section {
            flex-grow: 1;
            flex-basis: 180px; /* Minimum width before wrapping */
            padding: 20px;
            margin: 5px;
            text-align: center;
            font-weight: bold;
            color: #fff;
            cursor: pointer;
            border-radius: 6px;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            min-height: 60px; /* Ensure consistent height */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.1em;
        }

        .spdd-section:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .spdd-section.active {
            box-shadow: 0 0 0 3px #007bff, 0 4px 10px rgba(0,0,0,0.2); /* Highlight active section */
            transform: translateY(-2px);
        }

        #supply { background-color: #3498db; /* Blue */ }
        #processing { background-color: #2ecc71; /* Green */ }
        #delivery { background-color: #f1c40f; /* Yellow */ color: #333; } /* Darker text for yellow */
        #disposal { background-color: #e74c3c; /* Red */ }

        .info-display {
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
            min-height: 200px;
        }

        .info-display h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .info-images {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-images img {
            max-width: 100%; /* Make images responsive */
            height: auto;
            max-height: 180px; /* Control max height */
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            padding: 5px;
        }
        
        .info-text {
            font-size: 1em;
            color: #555;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .spdd-diagram {
                flex-direction: column;
            }
            .spdd-section {
                flex-basis: auto; /* Allow full width in column layout */
                margin: 10px 0; /* Adjust margin for vertical layout */
            }
            .info-images img {
                max-width: 45%; /* Two images per row on smaller screens if they fit */
                max-height: 150px;
            }
        }
        @media (max-width: 480px) {
             h1 {
                font-size: 1.5em;
             }
            .info-images img {
                max-width: 90%; /* Stack images on very small screens */
                max-height: 120px;
            }
            .spdd-section {
                font-size: 1em;
                padding: 15px;
            }
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>Anesthesia SPDD Model Explorer</h1>

        <div class="spdd-diagram">
            <div class="spdd-section" id="supply" data-section="supply">Supply</div>
            <div class="spdd-section" id="processing" data-section="processing">Processing</div>
            <div class="spdd-section" id="delivery" data-section="delivery">Delivery</div>
            <div class="spdd-section" id="disposal" data-section="disposal">Disposal</div>
        </div>

        <div class="info-display" id="info-display-area">
            <p>Click on a section of the SPDD model above to learn more.</p>
        </div>
    </div>

    <script>
        const spddData = {
            supply: {
                title: "Supply",
                images: [
                    { src: "https://via.placeholder.com/120x180/3498db/FFFFFF?text=Gas+Cylinder", alt: "Gas Cylinder with Pressure Gauge" },
                    { src: "https://via.placeholder.com/180x90/3498db/FFFFFF?text=Pipeline+Outlet", alt: "Pipeline Outlet" }
                ],
                text: "Gas is supplied to the anesthesia machine primarily via two sources: <strong>gas cylinders</strong> (often high-pressure backup or for portable use) and <strong>pipelines</strong> (the main source in most hospitals, delivering gas at a lower, regulated pressure)."
            },
            processing: {
                title: "Processing",
                images: [
                    { src: "https://via.placeholder.com/150x150/2ecc71/FFFFFF?text=Flowmeters", alt: "Flowmeters" },
                    { src: "https://via.placeholder.com/150x150/2ecc71/FFFFFF?text=Vaporizers", alt: "Vaporizers" }
                ],
                text: "Inside the anesthesia machine, gases are processed. <strong>Flowmeters</strong> control the rate of flow for each gas (e.g., oxygen, nitrous oxide, air). <strong>Vaporizers</strong> add a precise concentration of volatile anesthetic agent (e.g., Sevoflurane, Isoflurane) to the fresh gas flow."
            },
            delivery: {
                title: "Delivery",
                images: [
                    { src: "https://via.placeholder.com/150x150/f1c40f/333333?text=Anesthesia+Mask", alt: "Anesthesia Mask and Circuit" }
                ],
                text: "The processed gas mixture is delivered to the patient through a <strong>breathing circuit</strong>. This circuit typically connects to an <strong>anesthesia mask</strong>, laryngeal mask airway (LMA), or an endotracheal tube."
            },
            disposal: {
                title: "Disposal",
                images: [
                    { src: "https://via.placeholder.com/180x120/e74c3c/FFFFFF?text=Scavenging+System", alt: "Scavenging System Interface" }
                ],
                text: "Excess gases and any exhaled anesthetic agents are removed from the breathing circuit and the operating room environment through a <strong>scavenging system</strong>. This is crucial for patient safety and to prevent occupational exposure for healthcare staff."
            }
        };

        const infoDisplayArea = document.getElementById('info-display-area');
        const spddSections = document.querySelectorAll('.spdd-section');

        function displayInfo(sectionKey) {
            const data = spddData[sectionKey];
            if (!data) {
                infoDisplayArea.innerHTML = "<p>Information not found for this section.</p>";
                return;
            }

            infoDisplayArea.innerHTML = ''; // Clear previous content

            const titleElement = document.createElement('h3');
            titleElement.textContent = data.title;
            infoDisplayArea.appendChild(titleElement);

            if (data.images && data.images.length > 0) {
                const imagesContainer = document.createElement('div');
                imagesContainer.className = 'info-images';
                data.images.forEach(imgData => {
                    const img = document.createElement('img');
                    img.src = imgData.src;
                    img.alt = imgData.alt;
                    imagesContainer.appendChild(img);
                });
                infoDisplayArea.appendChild(imagesContainer);
            }

            const textElement = document.createElement('p');
            textElement.className = 'info-text';
            textElement.innerHTML = data.text; // Use innerHTML for potential <strong> tags
            infoDisplayArea.appendChild(textElement);
        }

        spddSections.forEach(section => {
            section.addEventListener('click', () => {
                // Remove 'active' class from all sections
                spddSections.forEach(s => s.classList.remove('active'));
                // Add 'active' class to the clicked section
                section.classList.add('active');
                
                const sectionKey = section.dataset.section;
                displayInfo(sectionKey);
            });
        });

    </script>

</body>
</html>
