<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine Pneumatics Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f0f0f0;
            color: #333;
        }

        .controls {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .controls button {
            padding: 8px 15px;
            margin: 0 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            font-size: 14px;
        }
        .controls button.active {
            background-color: #0056b3;
        }
        .controls button:hover {
            opacity: 0.9;
        }
        .controls .start-gas-btn {
            background-color: #28a745; /* Green for O2 */
        }
        .controls .start-gas-btn.n2o {
            background-color: #17a2b8; /* Blue for N2O */
        }


        #app-container {
            display: flex;
            flex-wrap: wrap; /* Allow wrapping for smaller screens */
            width: 100%;
            max-width: 1200px;
        }

        #diagram-container {
            flex: 3; /* Takes more space */
            min-width: 600px; /* Minimum width for the diagram */
            padding-right: 10px;
            position: relative; /* For flow meter sliders */
        }

        #diagram {
            width: 100%;
            height: auto;
            border: 1px solid #ccc;
            background-color: #fff;
            border-radius: 8px;
        }

        .component {
            cursor: pointer;
            transition: filter 0.2s ease-in-out;
        }
        .component:hover rect, .component:hover circle {
            filter: drop-shadow(0px 0px 3px rgba(0,0,0,0.5));
        }
        .component.highlight-next rect, .component.highlight-next circle {
            stroke: #ffc107; /* Yellow */
            stroke-width: 3px;
        }
        .component.selected rect, .component.selected circle {
            stroke: #dc3545; /* Red */
            stroke-width: 3px;
        }

        .component-label {
            font-size: 10px;
            text-anchor: middle;
            dominant-baseline: middle;
            pointer-events: none;
            fill: #333;
        }
        .component-label tspan {
            pointer-events: none;
        }

        .flow-line {
            stroke: #ccc;
            stroke-width: 2;
            fill: none;
            transition: stroke 0.3s, stroke-width 0.3s;
        }
        .flow-line.active-o2 {
            stroke: #28a745; /* Green */
            stroke-width: 4;
        }
        .flow-line.active-n2o {
            stroke: #007bff; /* Blue */
            stroke-width: 4;
        }
        .flow-line.active-neutral {
            stroke: #fd7e14; /* Orange for mixed/neutral */
            stroke-width: 4;
        }

        .shake {
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }
        @keyframes shake {
            10%, 90% { transform: translate3d(-1px, 0, 0); }
            20%, 80% { transform: translate3d(2px, 0, 0); }
            30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
            40%, 60% { transform: translate3d(4px, 0, 0); }
        }

        #info-panel {
            flex: 1; /* Takes less space */
            min-width: 280px; /* Minimum width for info */
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 10px; /* Add margin for smaller screens when it wraps */
        }
        #info-panel h3 {
            margin-top: 0;
            color: #0056b3;
        }
        #info-panel p {
            font-size: 14px;
            line-height: 1.6;
        }
        #component-description-text {
            min-height: 80px;
            border: 1px dashed #eee;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        #gas-status-text {
            font-style: italic;
            color: #555;
            margin-top: 5px;
            font-size: 13px;
        }

        .safety-info {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .safety-info h4 {
            color: #dc3545;
        }

        .flow-meter-control {
            position: absolute; /* Position relative to diagram-container */
            display: flex;
            flex-direction: column;
            align-items: center;
            background: rgba(255,255,255,0.8);
            padding: 5px;
            border-radius: 4px;
            box-shadow: 0 0 3px rgba(0,0,0,0.2);
        }
        .flow-meter-control label {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .flow-meter-control input[type="range"] {
            writing-mode: bt-lr; /* vertical slider */
            -webkit-appearance: slider-vertical;
            width: 20px;
            height: 70px;
            padding: 0;
            margin: 0;
        }
        .flow-meter-control span {
            font-size: 10px;
            margin-top: 2px;
        }

        /* Responsive adjustments */
        @media (max-width: 900px) {
            #app-container {
                flex-direction: column;
            }
            #diagram-container {
                padding-right: 0;
                min-width: 100%; /* Full width on smaller screens */
            }
            #info-panel {
                min-width: 100%; /* Full width on smaller screens */
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>

    <div class="controls">
        <button id="guidedModeBtn" class="active">Guided Mode</button>
        <button id="freeModeBtn">Free Mode</button>
        <button id="resetBtn">Reset</button>
        <span id="guidedModePrompts" style="margin-left: 10px; font-style: italic;"></span>
    </div>

    <div id="app-container">
        <div id="diagram-container">
            <svg id="diagram" viewBox="0 0 800 550" preserveAspectRatio="xMidYMid meet"></svg>
            <!-- Flow meter controls will be dynamically added here -->
        </div>

        <div id="info-panel">
            <h3>Component Information</h3>
            <div id="component-description-text">Select a component to see its description.</div>
            <div id="gas-status-text"></div>

            <div class="safety-info">
                <h4>Safety Features Explained</h4>
                <p><strong>Fail-Safe Valve:</strong> Ensures that if oxygen pressure is lost or falls below a safe minimum, the supply of nitrous oxide (and other hypoxic gases) is cut off. This prevents accidental delivery of a 100% N2O mixture.</p>
                <p><strong>Oxygen Supply Failure Alarm:</strong> An audible and/or visual alarm that activates when the oxygen pipeline pressure drops significantly (e.g., below 30 psi). It alerts clinicians to an impending O2 supply failure.</p>
                <p><strong>Hypoxic Guard System:</strong> Prevents delivery of a fresh gas mixture with less than 21-25% oxygen. It links the oxygen and nitrous oxide flow control valves (mechanically, pneumatically, or electronically) to maintain a minimum O2:N2O ratio.</p>
            </div>
        </div>
    </div>

    <script>
        const svgNS = "http://www.w3.org/2000/svg";
        const diagram = document.getElementById('diagram');
        const diagramContainer = document.getElementById('diagram-container');
        const descriptionTextElement = document.getElementById('component-description-text');
        const gasStatusTextElement = document.getElementById('gas-status-text');
        const guidedModeBtn = document.getElementById('guidedModeBtn');
        const freeModeBtn = document.getElementById('freeModeBtn');
        const resetBtn = document.getElementById('resetBtn');
        const guidedModePrompts = document.getElementById('guidedModePrompts');

        let currentMode = 'guided'; // 'guided' or 'free'
        let activeGasPath = null; // 'o2', 'n2o', 'o2Flush', 'auxO2'
        let currentGuidedStep = 0;
        let o2Flow = 0; // L/min
        let n2oFlow = 0; // L/min
        let o2PressureAvailable = false; // Simulates if O2 regulator is active

        const componentRadius = 15;
        const componentWidth = 90;
        const componentHeight = 50;

        const components = {
            'o2Supply': { id: 'o2Supply', label: 'O2 Supply', shortLabel: 'O2\nSupply', description: 'Source of Oxygen (pipeline or cylinder). Provides high-pressure oxygen.', type: 'o2', x: 50, y: 100, next: ['o2Regulator'], isSupply: true, color: '#28a745' },
            'n2oSupply': { id: 'n2oSupply', label: 'N2O Supply', shortLabel: 'N2O\nSupply', description: 'Source of Nitrous Oxide (pipeline or cylinder). Provides high-pressure nitrous oxide.', type: 'n2o', x: 50, y: 400, next: ['n2oRegulator'], isSupply: true, color: '#007bff' },

            'o2Regulator': { id: 'o2Regulator', label: 'O2 Pressure Regulator', shortLabel: 'O2\nRegulator', description: 'Reduces high O2 pressure to a lower, constant working pressure (40-55 psi).', type: 'o2', x: 180, y: 100, prev: ['o2Supply'], next: ['o2FlowMeter', 'o2FlushValve', 'auxO2FlowMeter'], color: '#28a745' },
            'n2oRegulator': { id: 'n2oRegulator', label: 'N2O Pressure Regulator', shortLabel: 'N2O\nRegulator', description: 'Reduces high N2O pressure to a lower, constant working pressure (40-55 psi).', type: 'n2o', x: 180, y: 400, prev: ['n2oSupply'], next: ['failSafeValve'], color: '#007bff' },
            
            'failSafeValve': { id: 'failSafeValve', label: 'Fail-Safe Valve', shortLabel: 'Fail-Safe\nValve', description: 'N2O (and other non-O2 gas) shut-off valve that closes if O2 pressure is lost. Requires O2 pressure to allow N2O flow.', type: 'n2o', x: 280, y: 400, prev: ['n2oRegulator'], next: ['hypoxicGuard'], dependsOnO2Pressure: true, color: '#007bff' },
            
            'o2FlowMeter': { id: 'o2FlowMeter', label: 'O2 Flow Meter', shortLabel: 'O2\nFlow Meter', description: 'Measures and controls O2 flow rate. O2 is added last to the manifold (closest to vaporizer).', type: 'o2', x: 380, y: 100, prev: ['o2Regulator'], next: ['vaporizerEntry'], isFlowMeter: true, gas: 'O2', color: '#28a745' },
            'n2oFlowMeter': { id: 'n2oFlowMeter', label: 'N2O Flow Meter', shortLabel: 'N2O\nFlow Meter', description: 'Measures and controls N2O flow rate. Linked to O2 flow by hypoxic guard.', type: 'n2o', x: 380, y: 320, prev: ['hypoxicGuard'], next: ['vaporizerEntry'], isFlowMeter: true, gas: 'N2O', color: '#007bff' },
            
            'hypoxicGuard': { id: 'hypoxicGuard', label: 'Hypoxic Guard', shortLabel: 'Hypoxic\nGuard', description: 'System linking O2 and N2O flows to ensure minimum 21-25% O2. If O2 flow is too low, N2O flow is limited or cut.', type: 'neutral', x: 330, y: 360, prev: ['failSafeValve'], next: ['n2oFlowMeter'], dependsOnO2Flow: true, color: '#6c757d' },

            'vaporizerEntry': { id: 'vaporizerEntry', label: 'Vaporizer Manifold', shortLabel: 'To\nVaporizer', description: 'Point where metered gases (O2, N2O) mix before entering the vaporizer.', type: 'neutral', x: 500, y: 210, prev: ['o2FlowMeter', 'n2oFlowMeter'], next: ['vaporizer'], color: '#6c757d' },
            'vaporizer': { id: 'vaporizer', label: 'Vaporizer', shortLabel: 'Vaporizer', description: 'Adds a precise amount of volatile anesthetic agent to the fresh gas flow.', type: 'neutral', x: 600, y: 210, prev: ['vaporizerEntry'], next: ['cgoCheckValve'], color: '#6c757d' },

            'o2FlushValve': { id: 'o2FlushValve', label: 'O2 Flush Valve', shortLabel: 'O2 Flush', description: 'Delivers high flow (35-75 L/min) of 100% O2 directly to CGO, bypassing flowmeters/vaporizers.', type: 'o2', x: 300, y: 210, prev: ['o2Regulator'], next: ['cgoCheckValve'], color: '#28a745' },
            
            'cgoCheckValve': { id: 'cgoCheckValve', label: 'Check Valve', shortLabel: 'Check\nValve', description: 'Prevents backflow from breathing circuit into machine/vaporizer.', type: 'neutral', x: 700, y: 210, prev: ['vaporizer', 'o2FlushValve'], next: ['commonGasOutlet'], color: '#6c757d' },
            'commonGasOutlet': { id: 'commonGasOutlet', label: 'Common Gas Outlet', shortLabel: 'CGO', description: 'Point where mixed gases exit to the patient breathing circuit.', type: 'neutral', x: 700, y: 300, prev: ['cgoCheckValve'], isOutlet: true, color: '#6c757d' },

            'auxO2FlowMeter': { id: 'auxO2FlowMeter', label: 'Auxiliary O2', shortLabel: 'Aux O2\nFlow Meter', description: 'Independent O2 source for supplemental oxygen.', type: 'o2', x: 180, y: 30, prev: ['o2Regulator'], next: ['auxO2Outlet'], isFlowMeter: true, gas: 'AuxO2', color: '#28a745' },
            'auxO2Outlet': { id: 'auxO2Outlet', label: 'Aux O2 Outlet', shortLabel: 'Aux O2\nOutlet', description: 'Outlet for auxiliary oxygen.', type: 'o2', x: 280, y: 30, prev: ['auxO2FlowMeter'], isOutlet: true, color: '#28a745' }
        };

        const flowPaths = {
            o2: ['o2Supply', 'o2Regulator', 'o2FlowMeter', 'vaporizerEntry', 'vaporizer', 'cgoCheckValve', 'commonGasOutlet'],
            n2o: ['n2oSupply', 'n2oRegulator', 'failSafeValve', 'hypoxicGuard', 'n2oFlowMeter', 'vaporizerEntry', 'vaporizer', 'cgoCheckValve', 'commonGasOutlet'],
            o2Flush: ['o2Supply', 'o2Regulator', 'o2FlushValve', 'cgoCheckValve', 'commonGasOutlet'],
            auxO2: ['o2Supply', 'o2Regulator', 'auxO2FlowMeter', 'auxO2Outlet']
        };
        
        let selectedComponentsFreeMode = []; // For Free Mode path tracking

        function drawComponent(component) {
            const group = document.createElementNS(svgNS, 'g');
            group.setAttribute('id', `svg_${component.id}`);
            group.classList.add('component');
            group.setAttribute('data-id', component.id);

            const rect = document.createElementNS(svgNS, 'rect');
            rect.setAttribute('x', component.x - componentWidth / 2);
            rect.setAttribute('y', component.y - componentHeight / 2);
            rect.setAttribute('width', componentWidth);
            rect.setAttribute('height', componentHeight);
            rect.setAttribute('rx', 5); // Rounded corners
            rect.setAttribute('ry', 5);
            rect.setAttribute('fill', component.color);
            rect.setAttribute('stroke', '#333');
            rect.setAttribute('stroke-width', 1);
            group.appendChild(rect);

            const text = document.createElementNS(svgNS, 'text');
            text.setAttribute('x', component.x);
            text.setAttribute('y', component.y);
            text.classList.add('component-label');
            
            const shortLabelParts = component.shortLabel.split('\n');
            shortLabelParts.forEach((part, index) => {
                const tspan = document.createElementNS(svgNS, 'tspan');
                tspan.setAttribute('x', component.x);
                tspan.setAttribute('dy', index === 0 && shortLabelParts.length > 1 ? '-0.3em' : (index > 0 ? '1.2em' : '0.3em')); // Adjust based on number of lines
                if (shortLabelParts.length === 1) tspan.setAttribute('dy', '0.3em');
                tspan.textContent = part;
                text.appendChild(tspan);
            });
            group.appendChild(text);

            group.addEventListener('click', () => handleComponentClick(component.id));
            diagram.appendChild(group);

            if (component.isFlowMeter) {
                createFlowMeterControl(component);
            }
        }

        function drawLine(id1, id2, customPath) {
            const comp1 = components[id1];
            const comp2 = components[id2];
            if (!comp1 || !comp2) return;

            const path = document.createElementNS(svgNS, 'path');
            let d;
            if (customPath) {
                d = customPath;
            } else {
                // Simple straight line logic, adjust for better routing if needed
                // Exit right side of comp1, enter left side of comp2
                let x1 = comp1.x + componentWidth / 2;
                let y1 = comp1.y;
                let x2 = comp2.x - componentWidth / 2;
                let y2 = comp2.y;

                // Adjust entry/exit points based on relative positions
                if (comp2.x < comp1.x - componentWidth) { // comp2 is to the left
                    x1 = comp1.x - componentWidth / 2;
                    x2 = comp2.x + componentWidth / 2;
                } else if (comp2.x > comp1.x + componentWidth) { // comp2 is to the right
                    // Default is fine
                } else { // comp2 is vertically aligned
                    x1 = comp1.x;
                    x2 = comp2.x;
                    if (comp2.y > comp1.y) { // comp2 is below
                        y1 = comp1.y + componentHeight / 2;
                        y2 = comp2.y - componentHeight / 2;
                    } else { // comp2 is above
                        y1 = comp1.y - componentHeight / 2;
                        y2 = comp2.y + componentHeight / 2;
                    }
                }
                 d = `M${x1},${y1} L${x2},${y2}`;
                 // For more complex paths, use C (cubic bezier) or Q (quadratic bezier)
                 // Example: Simple elbow for non-straight connections
                 if (Math.abs(y1 - y2) > 10 && Math.abs(x1-x2) > 10) { // If not mostly straight
                     d = `M${x1},${y1} L${x1 + (x2-x1)/2},${y1} L${x1 + (x2-x1)/2},${y2} L${x2},${y2}`;
                 }
            }
           
            path.setAttribute('d', d);
            path.setAttribute('id', `line_${id1}_${id2}`);
            path.classList.add('flow-line');
            diagram.insertBefore(path, diagram.firstChild); // Draw lines behind components
        }
        
        function getLineId(id1, id2) {
            // Ensure consistent ID order for bidirectional check if needed, but here it's directional.
            return `line_${id1}_${id2}`;
        }

        function drawDiagram() {
            diagram.innerHTML = ''; // Clear previous diagram
            // Draw lines first
            Object.values(components).forEach(comp => {
                if (comp.next) {
                    comp.next.forEach(nextId => {
                        // Custom paths for specific connections to make diagram cleaner
                        let customD = null;
                        if (comp.id === 'o2Regulator' && nextId === 'o2FlowMeter') customD = `M${components.o2Regulator.x + componentWidth/2},${components.o2Regulator.y} L${components.o2FlowMeter.x - componentWidth/2},${components.o2FlowMeter.y}`;
                        else if (comp.id === 'o2Regulator' && nextId === 'auxO2FlowMeter') customD = `M${components.o2Regulator.x},${components.o2Regulator.y - componentHeight/2} L${components.auxO2FlowMeter.x},${components.auxO2FlowMeter.y + componentHeight/2}`;
                        else if (comp.id === 'auxO2FlowMeter' && nextId === 'auxO2Outlet') customD = `M${components.auxO2FlowMeter.x + componentWidth/2},${components.auxO2FlowMeter.y} L${components.auxO2Outlet.x - componentWidth/2},${components.auxO2Outlet.y}`;
                        
                        else if (comp.id === 'n2oRegulator' && nextId === 'failSafeValve') customD = `M${components.n2oRegulator.x + componentWidth/2},${components.n2oRegulator.y} L${components.failSafeValve.x - componentWidth/2},${components.failSafeValve.y}`;
                        else if (comp.id === 'failSafeValve' && nextId === 'hypoxicGuard') customD = `M${components.failSafeValve.x + componentWidth/2},${components.failSafeValve.y} L${components.hypoxicGuard.x - componentWidth/2 +10},${components.hypoxicGuard.y}`; // +10 to aim for side
                        else if (comp.id === 'hypoxicGuard' && nextId === 'n2oFlowMeter') customD = `M${components.hypoxicGuard.x + componentWidth/2 -10},${components.hypoxicGuard.y} L${components.n2oFlowMeter.x - componentWidth/2},${components.n2oFlowMeter.y}`; // -10 to aim for side
                        
                        else if (comp.id === 'o2FlowMeter' && nextId === 'vaporizerEntry') customD = `M${components.o2FlowMeter.x + componentWidth/2},${components.o2FlowMeter.y} C ${components.o2FlowMeter.x + componentWidth},${components.o2FlowMeter.y} ${components.vaporizerEntry.x - componentWidth},${components.vaporizerEntry.y} ${components.vaporizerEntry.x - componentWidth/2},${components.vaporizerEntry.y}`;
                        else if (comp.id === 'n2oFlowMeter' && nextId === 'vaporizerEntry') customD = `M${components.n2oFlowMeter.x + componentWidth/2},${components.n2oFlowMeter.y} C ${components.n2oFlowMeter.x + componentWidth},${components.n2oFlowMeter.y} ${components.vaporizerEntry.x - componentWidth},${components.vaporizerEntry.y} ${components.vaporizerEntry.x - componentWidth/2},${components.vaporizerEntry.y}`;
                        
                        else if (comp.id === 'vaporizerEntry' && nextId === 'vaporizer') customD = `M${components.vaporizerEntry.x + componentWidth/2},${components.vaporizerEntry.y} L${components.vaporizer.x - componentWidth/2},${components.vaporizer.y}`;
                        else if (comp.id === 'vaporizer' && nextId === 'cgoCheckValve') customD = `M${components.vaporizer.x + componentWidth/2},${components.vaporizer.y} L${components.cgoCheckValve.x - componentWidth/2},${components.cgoCheckValve.y}`;
                        
                        else if (comp.id === 'o2Regulator' && nextId === 'o2FlushValve') customD = `M${components.o2Regulator.x + componentWidth/2},${components.o2Regulator.y} L${components.o2Regulator.x + componentWidth/2 + 20},${components.o2Regulator.y} L${components.o2Regulator.x + componentWidth/2 + 20},${components.o2FlushValve.y} L${components.o2FlushValve.x - componentWidth/2},${components.o2FlushValve.y}`;
                        else if (comp.id === 'o2FlushValve' && nextId === 'cgoCheckValve') customD = `M${components.o2FlushValve.x + componentWidth/2},${components.o2FlushValve.y} L${components.cgoCheckValve.x - componentWidth/2},${components.cgoCheckValve.y}`;
                        
                        else if (comp.id === 'cgoCheckValve' && nextId === 'commonGasOutlet') customD = `M${components.cgoCheckValve.x},${components.cgoCheckValve.y + componentHeight/2} L${components.commonGasOutlet.x},${components.commonGasOutlet.y - componentHeight/2}`;
                        
                        // Default straight line if no custom path
                        if(!customD) {
                             let x1 = comp.x + componentWidth / 2; let y1 = comp.y;
                             let x2 = components[nextId].x - componentWidth / 2; let y2 = components[nextId].y;
                             if (components[nextId].x < comp.x - componentWidth/2 ) { x1 = comp.x - componentWidth / 2; x2 = components[nextId].x + componentWidth / 2;}
                             else if (Math.abs(components[nextId].x - comp.x) < componentWidth/2) { x1 = comp.x; x2 = components[nextId].x; if(components[nextId].y > comp.y) {y1 = comp.y + componentHeight/2; y2 = components[nextId].y - componentHeight/2;} else {y1 = comp.y - componentHeight/2; y2 = components[nextId].y + componentHeight/2;} }
                             customD = `M${x1},${y1} L${x2},${y2}`;
                        }
                        drawLine(comp.id, nextId, customD);
                    });
                }
            });
            // Draw components
            Object.values(components).forEach(drawComponent);
        }

        function updateDescriptionPanel(componentId, gasStatusMsg = "") {
            const component = components[componentId];
            if (component) {
                descriptionTextElement.innerHTML = `<strong>${component.label}:</strong> ${component.description}`;
            } else {
                descriptionTextElement.textContent = "Select a component to see its description.";
            }
            gasStatusTextElement.textContent = gasStatusMsg;

            // Highlight selected component
            document.querySelectorAll('.component.selected').forEach(el => el.classList.remove('selected'));
            if (componentId) {
                const svgComp = document.getElementById(`svg_${componentId}`);
                if (svgComp) svgComp.classList.add('selected');
            }
        }

        function handleComponentClick(componentId) {
            const component = components[componentId];
            const svgComp = document.getElementById(`svg_${componentId}`);
            let gasStatusMsg = "";

            if (currentMode === 'guided') {
                if (!activeGasPath) { // Start of guided mode path selection
                    if (component.isSupply) {
                        activeGasPath = component.id === 'o2Supply' ? 'o2' : 'n2o';
                        if (component.id === 'o2Supply' && flowPaths.o2Flush.includes(component.id)) { // Allow choosing flush path
                            // This logic is a bit complex, for now, O2 flush is a separate "thing" you can trigger
                            // Or, make user choose "O2 Main Path" or "O2 Flush Path"
                        }
                        currentGuidedStep = 0;
                        const currentPathArray = flowPaths[activeGasPath];
                        if (currentPathArray[currentGuidedStep] === componentId) {
                            advanceGuidedMode(componentId);
                        }
                    } else {
                        promptGuidedStart();
                    }
                } else { // Mid-guided mode
                    const currentPathArray = flowPaths[activeGasPath];
                    const expectedComponentId = currentPathArray[currentGuidedStep];
                    if (componentId === expectedComponentId) {
                        advanceGuidedMode(componentId);
                    } else {
                        if (svgComp) {
                            svgComp.classList.add('shake');
                            setTimeout(() => svgComp.classList.remove('shake'), 500);
                        }
                        updateDescriptionPanel(componentId, "Not this one! Follow the highlighted component.");
                        // Re-highlight the correct next component
                        highlightNextComponentInGuide();
                    }
                }
            } else { // Free Mode
                updateDescriptionPanel(componentId);
                // Free mode path highlighting logic (simplified)
                if (component.isSupply) {
                    selectedComponentsFreeMode = [componentId];
                } else if (selectedComponentsFreeMode.length > 0) {
                    const lastSelectedId = selectedComponentsFreeMode[selectedComponentsFreeMode.length - 1];
                    const lastComponent = components[lastSelectedId];
                    if (lastComponent.next && lastComponent.next.includes(componentId)) {
                        selectedComponentsFreeMode.push(componentId);
                    } else {
                        // Invalid step in free mode, could provide feedback
                        selectedComponentsFreeMode = []; // Reset path or allow new start
                        if (component.isSupply) selectedComponentsFreeMode = [componentId]; // Start new path if supply clicked
                    }
                }
                highlightFreeModePath();
            }
        }

        function advanceGuidedMode(selectedComponentId) {
            const currentPathArray = flowPaths[activeGasPath];
            const component = components[selectedComponentId];
            let gasActionDescription = "";

            // Update O2 pressure status
            if (selectedComponentId === 'o2Regulator') o2PressureAvailable = true;

            // Logic for what happens to gas at this component
            switch(selectedComponentId) {
                case 'o2Regulator': case 'n2oRegulator':
                    gasActionDescription = "Gas pressure is reduced to a safe, constant working level.";
                    break;
                case 'failSafeValve':
                    if (o2PressureAvailable) {
                        gasActionDescription = "O2 pressure is adequate, so N2O is allowed to pass.";
                    } else {
                        gasActionDescription = "O2 pressure is INSUFFICIENT. N2O flow is STOPPED here.";
                        // End N2O path here if O2 pressure is off.
                        activeGasPath = null; 
                        guidedModePrompts.textContent = "Guided N2O path terminated due to no O2 pressure for Fail-Safe valve.";
                        updateDescriptionPanel(selectedComponentId, gasActionDescription);
                        return;
                    }
                    break;
                case 'hypoxicGuard':
                    if (o2Flow > 0 || activeGasPath === 'o2') { // Simplified: if O2 path active or O2 flow > 0
                         gasActionDescription = "N2O flow is checked against O2 flow to ensure minimum O2 concentration.";
                    } else {
                        gasActionDescription = "O2 flow is zero or insufficient. N2O flow is STOPPED or severely limited by the hypoxic guard.";
                        activeGasPath = null;
                        guidedModePrompts.textContent = "Guided N2O path terminated by Hypoxic Guard due to no O2 flow.";
                        updateDescriptionPanel(selectedComponentId, gasActionDescription);
                        return;
                    }
                    break;
                case 'o2FlowMeter': case 'n2oFlowMeter': case 'auxO2FlowMeter':
                    gasActionDescription = `Flow rate is now controlled. Current ${component.gas} flow: ${component.gas === 'O2' ? o2Flow : (component.gas === 'N2O' ? n2oFlow : 'N/A')} L/min.`;
                    break;
                case 'vaporizerEntry':
                    gasActionDescription = "Gases are mixed before entering the vaporizer.";
                    break;
                case 'vaporizer':
                    gasActionDescription = "Anesthetic vapor is added to the gas mixture.";
                    break;
                case 'o2FlushValve':
                    gasActionDescription = "High flow O2 bypasses flowmeters and vaporizer.";
                    break;
                case 'cgoCheckValve':
                    gasActionDescription = "Gas passes one-way valve, preventing backflow.";
                    break;
                case 'commonGasOutlet': case 'auxO2Outlet':
                    gasActionDescription = "Gas exits the machine.";
                    guidedModePrompts.textContent = `Path complete! Reached ${component.label}. Select a new supply or mode.`;
                    activeGasPath = null; // End of path
                    break;
            }
            updateDescriptionPanel(selectedComponentId, gasActionDescription);

            // Animate flow line to this component
            if (currentGuidedStep > 0) {
                const prevComponentId = currentPathArray[currentGuidedStep - 1];
                const lineId = getLineId(prevComponentId, selectedComponentId);
                const lineElem = document.getElementById(lineId);
                if (lineElem) {
                    lineElem.classList.remove('active-o2', 'active-n2o', 'active-neutral'); // Clear previous
                    if (component.type === 'o2' || components[prevComponentId].type === 'o2') lineElem.classList.add('active-o2');
                    else if (component.type === 'n2o' || components[prevComponentId].type === 'n2o') lineElem.classList.add('active-n2o');
                    else lineElem.classList.add('active-neutral');
                }
            }
            
            currentGuidedStep++;
            if (activeGasPath && currentGuidedStep < currentPathArray.length) {
                highlightNextComponentInGuide();
            } else if (!activeGasPath) { // Path finished or terminated
                document.querySelectorAll('.component.highlight-next').forEach(el => el.classList.remove('highlight-next'));
            }
        }
        
        function highlightNextComponentInGuide() {
            document.querySelectorAll('.component.highlight-next').forEach(el => el.classList.remove('highlight-next'));
            if (!activeGasPath) return;

            const currentPathArray = flowPaths[activeGasPath];
            if (currentGuidedStep < currentPathArray.length) {
                const nextComponentId = currentPathArray[currentGuidedStep];
                const svgComp = document.getElementById(`svg_${nextComponentId}`);
                if (svgComp) {
                    svgComp.classList.add('highlight-next');
                }
                guidedModePrompts.textContent = `Next, find and click: ${components[nextComponentId].label}`;
            }
        }

        function highlightFreeModePath() {
            // Clear previous highlights
            document.querySelectorAll('.flow-line').forEach(line => line.classList.remove('active-o2', 'active-n2o', 'active-neutral'));
            
            if (selectedComponentsFreeMode.length < 2) return;

            for (let i = 0; i < selectedComponentsFreeMode.length - 1; i++) {
                const comp1Id = selectedComponentsFreeMode[i];
                const comp2Id = selectedComponentsFreeMode[i+1];
                const lineElem = document.getElementById(getLineId(comp1Id, comp2Id));
                if (lineElem) {
                    const comp1Type = components[comp1Id].type;
                    const comp2Type = components[comp2Id].type;
                    if (comp1Type === 'o2' || comp2Type === 'o2') lineElem.classList.add('active-o2');
                    else if (comp1Type === 'n2o' || comp2Type === 'n2o') lineElem.classList.add('active-n2o');
                    else lineElem.classList.add('active-neutral');
                }
            }
            // If path reaches CGO
            const lastCompId = selectedComponentsFreeMode[selectedComponentsFreeMode.length -1];
            if (components[lastCompId].isOutlet) {
                gasStatusTextElement.textContent = "Valid path to an outlet traced!";
            }
        }

        function createFlowMeterControl(component) {
            const controlDiv = document.createElement('div');
            controlDiv.classList.add('flow-meter-control');
            // Position the control near the flow meter component
            // This needs to be adjusted based on your component layout
            controlDiv.style.left = `${component.x + componentWidth / 2 + 5}px`;
            controlDiv.style.top = `${component.y - componentHeight / 2}px`;

            const label = document.createElement('label');
            label.textContent = `${component.gas} Flow`;
            controlDiv.appendChild(label);

            const slider = document.createElement('input');
            slider.type = 'range';
            slider.min = 0;
            slider.max = component.gas === 'O2' ? 10 : 12; // Max flow O2 10L, N2O 12L
            slider.step = component.gas === 'O2' ? 0.1 : 0.5;
            slider.value = 0;
            slider.id = `slider_${component.id}`;
            controlDiv.appendChild(slider);

            const valueSpan = document.createElement('span');
            valueSpan.id = `value_${component.id}`;
            valueSpan.textContent = `0 L/min`;
            controlDiv.appendChild(valueSpan);
            
            slider.addEventListener('input', (e) => {
                const flowValue = parseFloat(e.target.value);
                valueSpan.textContent = `${flowValue.toFixed(1)} L/min`;
                if (component.gas === 'O2') {
                    o2Flow = flowValue;
                    // Hypoxic guard: if O2 flow is 0, N2O should be 0.
                    if (o2Flow === 0 && n2oFlow > 0) {
                        const n2oSlider = document.getElementById('slider_n2oFlowMeter');
                        const n2oValueSpan = document.getElementById('value_n2oFlowMeter');
                        if (n2oSlider) {
                            n2oSlider.value = 0;
                            n2oFlow = 0;
                            if (n2oValueSpan) n2oValueSpan.textContent = `0.0 L/min`;
                            gasStatusTextElement.textContent = "Hypoxic guard: N2O flow cut as O2 flow is zero.";
                        }
                    }
                } else if (component.gas === 'N2O') {
                    if (o2Flow === 0 && flowValue > 0) {
                        e.target.value = 0; // Force N2O to 0 if O2 is 0
                        n2oFlow = 0;
                        valueSpan.textContent = `0.0 L/min`;
                        gasStatusTextElement.textContent = "Hypoxic guard: Cannot set N2O flow, O2 flow is zero.";
                        return;
                    }
                    n2oFlow = flowValue;
                }
                // If in guided mode and at a flowmeter step, update gas status text
                if (currentMode === 'guided' && activeGasPath && flowPaths[activeGasPath][currentGuidedStep-1] === component.id) {
                    let currentGasStatus = gasStatusTextElement.textContent.split(" Current")[0];
                    gasStatusTextElement.textContent = `${currentGasStatus} Current ${component.gas} flow: ${flowValue.toFixed(1)} L/min.`;
                }
            });
            diagramContainer.appendChild(controlDiv);
        }


        function resetApp() {
            activeGasPath = null;
            currentGuidedStep = 0;
            o2Flow = 0;
            n2oFlow = 0;
            o2PressureAvailable = false;
            selectedComponentsFreeMode = [];

            // Reset UI elements
            document.querySelectorAll('.flow-line').forEach(line => line.classList.remove('active-o2', 'active-n2o', 'active-neutral'));
            document.querySelectorAll('.component').forEach(comp => comp.classList.remove('highlight-next', 'selected', 'shake'));
            updateDescriptionPanel(null, "");
            
            // Reset flow meter sliders and displays
            const o2Slider = document.getElementById('slider_o2FlowMeter');
            const o2Value = document.getElementById('value_o2FlowMeter');
            if(o2Slider) o2Slider.value = 0;
            if(o2Value) o2Value.textContent = "0.0 L/min";

            const n2oSlider = document.getElementById('slider_n2oFlowMeter');
            const n2oValue = document.getElementById('value_n2oFlowMeter');
            if(n2oSlider) n2oSlider.value = 0;
            if(n2oValue) n2oValue.textContent = "0.0 L/min";
            
            const auxO2Slider = document.getElementById('slider_auxO2FlowMeter');
            const auxO2Value = document.getElementById('value_auxO2FlowMeter');
            if(auxO2Slider) auxO2Slider.value = 0;
            if(auxO2Value) auxO2Value.textContent = "0.0 L/min";

            if (currentMode === 'guided') {
                promptGuidedStart();
            } else {
                guidedModePrompts.textContent = "";
            }
        }
        
        function promptGuidedStart() {
            guidedModePrompts.innerHTML = `Select a starting supply: <button class="start-gas-btn" onclick="startGuidedPath('o2')">O2 Supply</button> or <button class="start-gas-btn n2o" onclick="startGuidedPath('n2o')">N2O Supply</button> or <button class="start-gas-btn" onclick="startGuidedPath('auxO2')">Aux O2</button> or <button class="start-gas-btn" onclick="startGuidedPath('o2Flush')">O2 Flush</button>`;
        }

        function startGuidedPath(gasType) {
            resetApp(); // Reset lines and highlights before starting a new path
            currentMode = 'guided'; // Ensure mode is set
            activeGasPath = gasType;
            currentGuidedStep = 0;
            const firstComponentId = flowPaths[activeGasPath][0];
            
            // Simulate clicking the first component to start the flow
            handleComponentClick(firstComponentId); 
        }

        guidedModeBtn.addEventListener('click', () => {
            currentMode = 'guided';
            guidedModeBtn.classList.add('active');
            freeModeBtn.classList.remove('active');
            resetApp();
        });

        freeModeBtn.addEventListener('click', () => {
            currentMode = 'free';
            freeModeBtn.classList.add('active');
            guidedModeBtn.classList.remove('active');
            resetApp();
            guidedModePrompts.textContent = "Free Mode: Click components to trace a path.";
        });

        resetBtn.addEventListener('click', resetApp);

        // Initial setup
        drawDiagram();
        if (currentMode === 'guided') {
           promptGuidedStart();
        }
    </script>

</body>
</html>
