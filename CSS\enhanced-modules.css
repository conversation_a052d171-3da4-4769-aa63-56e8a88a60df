/* ===== ENHANCED MODULE PAGES STYLING ===== */

/* CSS Variables for Enhanced Modules */
:root {
    --module-primary: #3b82f6;
    --module-secondary: #8b5cf6;
    --module-accent: #06b6d4;
    --module-success: #10b981;
    --module-warning: #f59e0b;
    --module-danger: #ef4444;
    --module-bg-primary: #ffffff;
    --module-bg-secondary: #f8fafc;
    --module-bg-tertiary: #f1f5f9;
    --module-text-primary: #1e293b;
    --module-text-secondary: #64748b;
    --module-text-muted: #94a3b8;
    --module-border: #e2e8f0;
    --module-shadow-light: rgba(0, 0, 0, 0.1);
    --module-shadow-medium: rgba(0, 0, 0, 0.15);
    --module-gradient-primary: linear-gradient(135deg, var(--module-primary), var(--module-secondary));
    --module-gradient-accent: linear-gradient(135deg, var(--module-accent), var(--module-success));
}

[data-theme="dark"] {
    --module-bg-primary: #0f172a;
    --module-bg-secondary: #1e293b;
    --module-bg-tertiary: #334155;
    --module-text-primary: #f8fafc;
    --module-text-secondary: #cbd5e1;
    --module-text-muted: #94a3b8;
    --module-border: #475569;
    --module-shadow-light: rgba(0, 0, 0, 0.3);
    --module-shadow-medium: rgba(0, 0, 0, 0.4);
}

/* Module Header */
.module-header {
    background: var(--module-gradient-primary);
    padding: 2rem 0;
    box-shadow: 0 4px 20px var(--module-shadow-medium);
    position: relative;
    overflow: hidden;
}

.module-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

.module-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.module-title h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.module-title p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
}

.header-controls {
    display: flex;
    gap: 1rem;
}

.btn-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn-control:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Module Overview */
.module-overview {
    padding: 4rem 0;
    background: var(--module-bg-secondary);
}

.overview-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.overview-card {
    background: var(--module-bg-primary);
    border-radius: 20px;
    padding: 2.5rem;
    border: 1px solid var(--module-border);
    box-shadow: 0 8px 32px var(--module-shadow-light);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--module-gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.overview-card:hover::before {
    transform: scaleX(1);
}

.overview-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px var(--module-shadow-medium);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: var(--module-gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.card-icon i {
    font-size: 2rem;
    color: white;
}

.overview-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--module-text-primary);
    margin-bottom: 1rem;
}

.overview-card p {
    color: var(--module-text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--module-bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: var(--module-gradient-primary);
    border-radius: 4px;
    transition: width 0.6s ease;
}

.progress-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--module-text-secondary);
}

/* Learning Tools Section */
.learning-tools {
    padding: 4rem 0;
    background: var(--module-bg-primary);
}

.tools-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 3rem;
    background: var(--module-gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tool-section {
    margin-bottom: 4rem;
    background: var(--module-bg-secondary);
    border-radius: 24px;
    padding: 3rem;
    border: 1px solid var(--module-border);
    box-shadow: 0 12px 40px var(--module-shadow-light);
}

.tool-header {
    text-align: center;
    margin-bottom: 3rem;
}

.tool-header h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--module-text-primary);
    margin-bottom: 1rem;
}

.tool-header p {
    font-size: 1.1rem;
    color: var(--module-text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Virtual Monitor */
.virtual-monitor {
    background: linear-gradient(135deg, #1e293b, #334155);
    border-radius: 20px;
    padding: 2rem;
    border: 2px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.2);
}

.monitor-screen {
    background: #000;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 3px solid rgba(59, 130, 246, 0.5);
    position: relative;
}

.monitor-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    min-height: 400px;
}

.parameter-label {
    color: #00ff88;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.parameter-value {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-top: 1rem;
}

.parameter-value .value {
    font-size: 2rem;
    font-weight: 800;
    color: #00ff88;
    font-family: 'Courier New', monospace;
}

.parameter-value .unit {
    font-size: 1rem;
    color: #64748b;
    font-weight: 500;
}

/* Animated Icons */
.animated-heartbeat {
    animation: heartbeatPulse 1.5s ease-in-out infinite;
}

@keyframes heartbeatPulse {
    0%, 100% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.2);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.1);
    }
}

.animated-pulse {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

.animated-gauge {
    animation: gaugeRotate 3s ease-in-out infinite;
}

@keyframes gaugeRotate {
    0%, 100% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(15deg);
    }
}

/* Anesthesia Machine Styles */
.anesthesia-machine {
    background: linear-gradient(135deg, #1e293b, #334155);
    border-radius: 24px;
    padding: 2rem;
    border: 2px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.2);
}

.machine-display {
    background: #000;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 3px solid rgba(59, 130, 246, 0.5);
    position: relative;
}

.display-screen {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    min-height: 500px;
}

.screen-header {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.3);
    margin-bottom: 2rem;
}

.screen-header h4 {
    color: #00ff88;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.status-indicators {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #64748b;
    transition: all 0.3s ease;
}

.status-light.active {
    background: #00ff88;
    box-shadow: 0 0 10px #00ff88;
}

.status-text {
    color: #cbd5e1;
    font-weight: 500;
}

/* Gas Monitoring Panel */
.gas-monitoring {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.gas-panel h5,
.vaporizer-panel h5,
.ventilator-panel h5,
.waveform-panel h5 {
    color: #00ff88;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.flowmeter-bank {
    display: flex;
    justify-content: space-around;
    align-items: end;
    gap: 1rem;
}

.flowmeter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.flowmeter-tube {
    width: 40px;
    height: 150px;
    background: linear-gradient(to top, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1));
    border: 2px solid #3b82f6;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.flow-indicator {
    position: absolute;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #00ff88, #3b82f6);
    border-radius: 50%;
    left: 50%;
    transform: translateX(-50%);
    transition: bottom 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
}

@keyframes flowBubble {
    0%, 100% {
        transform: translateX(-50%) scale(1);
    }
    50% {
        transform: translateX(-50%) scale(1.1);
    }
}

.flow-scale {
    position: absolute;
    right: -25px;
    top: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 0.7rem;
    color: #64748b;
}

.gas-label {
    color: #00ff88;
    font-weight: 700;
    font-size: 0.9rem;
}

.flow-value {
    color: #3b82f6;
    font-weight: 600;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
}

/* Vaporizer Controls */
.vaporizer-controls {
    display: flex;
    justify-content: center;
}

.vaporizer-dial {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.dial-face {
    width: 100px;
    height: 100px;
    border: 3px solid #8b5cf6;
    border-radius: 50%;
    position: relative;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05));
}

.dial-pointer {
    position: absolute;
    top: 10px;
    left: 50%;
    width: 3px;
    height: 35px;
    background: #ef4444;
    transform-origin: bottom center;
    border-radius: 2px;
    transition: transform 0.3s ease;
}

.dial-markings {
    position: absolute;
    width: 100%;
    height: 100%;
}

.marking {
    position: absolute;
    top: 5px;
    left: 50%;
    transform-origin: 50px 45px;
    color: #8b5cf6;
    font-size: 0.7rem;
    font-weight: 600;
}

.vaporizer-label {
    color: #8b5cf6;
    font-weight: 600;
    font-size: 0.9rem;
}

.concentration-display {
    color: #ef4444;
    font-weight: 700;
    font-size: 1.2rem;
    font-family: 'Courier New', monospace;
}

/* Ventilator Panel */
.vent-parameters {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.parameter {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.parameter label {
    display: block;
    color: #cbd5e1;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.parameter .value {
    color: #00ff88;
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

/* Waveform Panel */
.waveform-display {
    margin-bottom: 1rem;
}

.waveform-display canvas {
    width: 100%;
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
}

.waveform-label {
    color: #64748b;
    font-size: 0.8rem;
    text-align: center;
    margin-top: 0.5rem;
}

/* Machine Controls */
.machine-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.control-section h5 {
    color: var(--module-text-primary);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.gas-controls,
.vent-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.emergency-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.control-btn {
    background: var(--module-gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.emergency-btn {
    background: var(--module-gradient-accent);
    border: none;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.emergency-btn:hover {
    background: linear-gradient(135deg, var(--module-danger), #dc2626);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
}

/* Responsive Design for Machine */
@media (max-width: 1024px) {
    .display-screen {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .machine-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .flowmeter-bank {
        flex-direction: column;
        align-items: center;
    }

    .vent-parameters {
        grid-template-columns: 1fr;
    }
}
