/* ===== INTERACTIVE DIAGRAMS STYLES ===== */

/* Diagram Categories Navigation */
.diagram-categories {
    background: linear-gradient(135deg, #1e293b, #334155);
    padding: 2rem 0;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.categories-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.category-tabs {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1.5rem;
}

.category-tab {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    color: #cbd5e1;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    min-width: 140px;
    font-weight: 600;
}

.category-tab:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.category-tab.active {
    background: var(--module-gradient-primary);
    border-color: transparent;
    color: white;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4);
}

.category-tab i {
    font-size: 1.5rem;
}

.category-tab span {
    font-size: 0.9rem;
    text-align: center;
}

/* Diagram Type Selector */
.diagram-type-selector {
    background: rgba(59, 130, 246, 0.05);
    padding: 1.5rem 0;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.selector-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.type-buttons {
    display: flex;
    gap: 0.5rem;
}

.type-btn {
    background: rgba(139, 92, 246, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.3);
    color: #8b5cf6;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.type-btn:hover {
    background: rgba(139, 92, 246, 0.2);
    transform: translateY(-1px);
}

.type-btn.active {
    background: var(--module-gradient-secondary);
    border-color: transparent;
    color: white;
    box-shadow: 0 2px 12px rgba(139, 92, 246, 0.4);
}

.diagram-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.control-btn {
    background: rgba(6, 182, 212, 0.1);
    border: 1px solid rgba(6, 182, 212, 0.3);
    color: #06b6d4;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    font-size: 0.85rem;
}

.control-btn:hover {
    background: rgba(6, 182, 212, 0.2);
    transform: translateY(-1px);
}

/* Main Diagram Display Area */
.diagram-display-area {
    background: var(--module-bg-primary);
    min-height: 70vh;
    padding: 2rem 0;
}

.display-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.diagram-category-panel {
    display: none;
}

.diagram-category-panel.active {
    display: block;
}

.diagram-type-panel {
    display: none;
}

.diagram-type-panel.active {
    display: block;
}

.diagram-header {
    text-align: center;
    margin-bottom: 2rem;
}

.diagram-header h3 {
    color: var(--module-text-primary);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.diagram-header p {
    color: var(--module-text-secondary);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Interactive Diagram Container */
.interactive-diagram-container {
    background: linear-gradient(135deg, #0f172a, #1e293b);
    border-radius: 20px;
    padding: 2rem;
    border: 2px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.interactive-diagram {
    width: 100%;
    height: auto;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    cursor: grab;
    transition: transform 0.3s ease;
}

.interactive-diagram:active {
    cursor: grabbing;
}

/* SVG Component Styles */
.block-component {
    cursor: pointer;
    transition: all 0.3s ease;
}

.block-component:hover {
    filter: url(#glow);
    transform: scale(1.05);
}

.block-component.selected {
    filter: url(#glow);
    transform: scale(1.1);
}

.block-component rect {
    transition: all 0.3s ease;
}

.block-component:hover rect {
    stroke-width: 3;
}

.signal-flow .flow-line {
    transition: all 0.3s ease;
    opacity: 0.8;
}

.signal-flow .flow-line.animated {
    stroke-dasharray: 10;
    animation: flowAnimation 2s linear infinite;
}

@keyframes flowAnimation {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: 20;
    }
}

.signal-labels text {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
}

/* Component Information Panel */
.component-info-panel {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: linear-gradient(135deg, #1e293b, #334155);
    border-left: 2px solid rgba(59, 130, 246, 0.3);
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3);
    transition: right 0.3s ease;
    z-index: 1000;
    overflow-y: auto;
}

.component-info-panel.active {
    right: 0;
}

.info-container {
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.info-header h3 {
    color: #e2e8f0;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
}

.close-info {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #ef4444;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.close-info:hover {
    background: rgba(239, 68, 68, 0.2);
}

.info-content {
    flex: 1;
    overflow-y: auto;
}

#componentDescription {
    margin-bottom: 1.5rem;
}

#componentDescription p {
    color: #cbd5e1;
    line-height: 1.6;
    font-size: 1rem;
}

#componentSpecs {
    background: rgba(59, 130, 246, 0.1);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

#componentSpecs h4 {
    color: #3b82f6;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.spec-item:last-child {
    border-bottom: none;
}

.spec-label {
    color: #cbd5e1;
    font-weight: 500;
}

.spec-value {
    color: #3b82f6;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

#componentConnections {
    background: rgba(139, 92, 246, 0.1);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

#componentConnections h4 {
    color: #8b5cf6;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.connection-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #cbd5e1;
}

.connection-item i {
    color: #8b5cf6;
    width: 16px;
}

/* Zoom Controls */
.zoom-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Animation Controls */
.animation-controls {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    display: flex;
    gap: 0.5rem;
}

.animation-btn {
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.4);
    color: #3b82f6;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.animation-btn:hover {
    background: rgba(59, 130, 246, 0.3);
}

.animation-btn.active {
    background: #3b82f6;
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .selector-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .type-buttons {
        justify-content: center;
    }
    
    .diagram-controls {
        justify-content: center;
    }
    
    .component-info-panel {
        width: 100%;
        right: -100%;
    }
    
    .category-tabs {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

@media (max-width: 768px) {
    .categories-container,
    .display-container {
        padding: 0 1rem;
    }
    
    .interactive-diagram-container {
        padding: 1rem;
    }
    
    .category-tab {
        min-width: 100px;
        padding: 0.75rem 1rem;
    }
    
    .category-tab i {
        font-size: 1.2rem;
    }
    
    .category-tab span {
        font-size: 0.8rem;
    }
    
    .type-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    .control-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* Dark Theme Adjustments */
[data-theme="dark"] .diagram-categories {
    background: linear-gradient(135deg, #0f172a, #1e293b);
}

[data-theme="dark"] .diagram-type-selector {
    background: rgba(59, 130, 246, 0.03);
}

[data-theme="dark"] .interactive-diagram-container {
    background: linear-gradient(135deg, #020617, #0f172a);
    border-color: rgba(59, 130, 246, 0.3);
}

/* Loading Animation */
.diagram-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
    color: var(--module-text-secondary);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
