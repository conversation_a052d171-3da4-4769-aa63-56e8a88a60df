// ===== LECTURE SYSTEM JAVASCRIPT =====

// Lecture State Management
const lectureState = {
    currentLecture: null,
    currentSlide: 0,
    totalSlides: 0,
    isPlaying: false,
    autoplayEnabled: false,
    autoplaySpeed: 8000, // 8 seconds default
    animationsEnabled: true,
    notesOpen: false,
    fullscreen: false,
    settings: {
        autoplaySpeed: 8,
        animationSpeed: 'normal',
        showAnimations: true,
        showNotes: true
    }
};

// Autoplay Timer
let autoplayTimer = null;

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeLectureSystem();
    loadLectureSettings();
    setupKeyboardControls();
});

function initializeLectureSystem() {
    console.log('Lecture System - Initializing...');
    
    // Load saved settings
    loadLectureSettings();
    
    // Setup event listeners
    setupLectureEventListeners();
    
    console.log('Lecture System initialized');
}

function setupLectureEventListeners() {
    // Settings modal events
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    if (autoplaySpeedSlider) {
        autoplaySpeedSlider.addEventListener('input', updateSpeedDisplay);
    }
    
    // Keyboard events
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Window events
    window.addEventListener('beforeunload', saveLectureProgress);
}

// ===== LECTURE MANAGEMENT =====
function startLecture(lectureId) {
    console.log(`Starting lecture: ${lectureId}`);
    
    // Load lecture data
    const lectureData = getLectureData(lectureId);
    if (!lectureData) {
        console.error('Lecture data not found:', lectureId);
        return;
    }
    
    // Set current lecture
    lectureState.currentLecture = lectureData;
    lectureState.currentSlide = 0;
    lectureState.totalSlides = lectureData.slides.length;
    
    // Show lecture viewer
    showLectureViewer();
    
    // Initialize lecture display
    initializeLectureDisplay();
    
    // Load first slide
    loadSlide(0);
    
    // Track lecture start
    trackLectureEvent('lecture_started', lectureId);
}

function showLectureViewer() {
    const lectureSelection = document.getElementById('lecture-selection');
    const lectureViewer = document.getElementById('lecture-viewer');
    
    if (lectureSelection) lectureSelection.style.display = 'none';
    if (lectureViewer) lectureViewer.style.display = 'block';
    
    // Update document title
    document.title = `${lectureState.currentLecture.title} - Virtual Medical Simulation LMS`;
}

function closeLecture() {
    // Save progress
    saveLectureProgress();
    
    // Stop autoplay
    stopAutoplay();
    
    // Hide lecture viewer
    const lectureSelection = document.getElementById('lecture-selection');
    const lectureViewer = document.getElementById('lecture-viewer');
    
    if (lectureSelection) lectureSelection.style.display = 'block';
    if (lectureViewer) lectureViewer.style.display = 'none';
    
    // Reset state
    lectureState.currentLecture = null;
    lectureState.currentSlide = 0;
    lectureState.totalSlides = 0;
    
    // Restore document title
    document.title = 'Interactive Lectures - Virtual Medical Simulation LMS';
    
    // Track lecture end
    trackLectureEvent('lecture_ended', lectureState.currentLecture?.id);
}

function initializeLectureDisplay() {
    const lecture = lectureState.currentLecture;
    
    // Update lecture title
    const titleElement = document.getElementById('currentLectureTitle');
    if (titleElement) {
        titleElement.textContent = lecture.title;
    }
    
    // Generate slide thumbnails
    generateSlideThumbnails();
    
    // Update slide counter
    updateSlideCounter();
    
    // Update progress bar
    updateProgressBar();
}

// ===== SLIDE NAVIGATION =====
function loadSlide(slideIndex) {
    if (!lectureState.currentLecture || slideIndex < 0 || slideIndex >= lectureState.totalSlides) {
        return;
    }
    
    const slide = lectureState.currentLecture.slides[slideIndex];
    const slideContent = document.getElementById('slideContent');
    
    if (!slideContent) return;
    
    // Clear previous content
    slideContent.innerHTML = '';
    
    // Create slide element
    const slideElement = createSlideElement(slide, slideIndex);
    slideContent.appendChild(slideElement);
    
    // Update state
    lectureState.currentSlide = slideIndex;
    
    // Update UI
    updateSlideCounter();
    updateProgressBar();
    updateNavigationButtons();
    updateThumbnailSelection();
    
    // Apply animations if enabled
    if (lectureState.animationsEnabled) {
        applySlideAnimations(slideElement, slide);
    }
    
    // Track slide view
    trackLectureEvent('slide_viewed', lectureState.currentLecture.id, { slideIndex });
}

function createSlideElement(slide, slideIndex) {
    const slideElement = document.createElement('div');
    slideElement.className = 'slide active';
    slideElement.innerHTML = generateSlideHTML(slide, slideIndex);
    
    return slideElement;
}

function generateSlideHTML(slide, slideIndex) {
    let html = '';
    
    // Add slide content based on type
    switch (slide.type) {
        case 'title':
            html = generateTitleSlide(slide);
            break;
        case 'content':
            html = generateContentSlide(slide);
            break;
        case 'visual':
            html = generateVisualSlide(slide);
            break;
        case 'interactive':
            html = generateInteractiveSlide(slide);
            break;
        case 'summary':
            html = generateSummarySlide(slide);
            break;
        default:
            html = generateContentSlide(slide);
    }
    
    return html;
}

function generateTitleSlide(slide) {
    return `
        <div class="slide-title-content">
            <h1 class="fade-in">${slide.title}</h1>
            ${slide.subtitle ? `<h2 class="fade-in-delay">${slide.subtitle}</h2>` : ''}
            ${slide.description ? `<p class="fade-in-delay-2">${slide.description}</p>` : ''}
            ${slide.visual ? generateVisualElement(slide.visual) : ''}
        </div>
    `;
}

function generateContentSlide(slide) {
    let html = `<h2 class="fade-in">${slide.title}</h2>`;
    
    if (slide.content) {
        slide.content.forEach((item, index) => {
            const delay = index + 1;
            if (typeof item === 'string') {
                html += `<p class="fade-in-delay-${Math.min(delay, 3)}">${item}</p>`;
            } else if (item.type === 'list') {
                html += `<ul class="fade-in-delay-${Math.min(delay, 3)}">`;
                item.items.forEach(listItem => {
                    html += `<li>${listItem}</li>`;
                });
                html += '</ul>';
            } else if (item.type === 'highlight') {
                html += `<p class="highlight fade-in-delay-${Math.min(delay, 3)}">${item.text}</p>`;
            }
        });
    }
    
    if (slide.visual) {
        html += generateVisualElement(slide.visual);
    }
    
    return html;
}

function generateVisualSlide(slide) {
    let html = `<h2 class="fade-in">${slide.title}</h2>`;
    
    if (slide.description) {
        html += `<p class="fade-in-delay">${slide.description}</p>`;
    }
    
    html += generateVisualElement(slide.visual);
    
    return html;
}

function generateVisualElement(visual) {
    if (!visual) return '';
    
    switch (visual.type) {
        case 'heartbeat':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="heartbeat-demo">
                        <i class="fas fa-heartbeat heartbeat" style="font-size: 4rem; color: #e74c3c;"></i>
                        <p>Normal Heart Rate: 60-100 bpm</p>
                    </div>
                </div>
            `;
        
        case 'ecg':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="ecg-wave">
                        <div class="ecg-line"></div>
                        <div class="ecg-pulse"></div>
                    </div>
                    <p>Electrocardiogram (ECG) Waveform</p>
                </div>
            `;
        
        case 'blood-pressure':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="pressure-gauge pulse">
                        <div class="pressure-needle"></div>
                    </div>
                    <p>Blood Pressure Measurement</p>
                </div>
            `;
        
        case 'breathing':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="lung-visual breathing" style="width: 200px; height: 150px; background: #3498db; border-radius: 50%; margin: 0 auto; opacity: 0.7;"></div>
                    <p>Respiratory Cycle</p>
                </div>
            `;
        
        case 'gas-flow':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="gas-flow">
                        <div class="gas-bubble"></div>
                        <div class="gas-bubble"></div>
                        <div class="gas-bubble"></div>
                        <div class="gas-bubble"></div>
                    </div>
                    <p>Gas Flow Visualization</p>
                </div>
            `;
        
        case 'flowmeter':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="flowmeter">
                        <div class="flowmeter-ball"></div>
                    </div>
                    <p>Flowmeter Operation</p>
                </div>
            `;
        
        default:
            return `<div class="visual-placeholder fade-in-delay-2">${visual.description || 'Visual Element'}</div>`;
    }
}

function generateInteractiveSlide(slide) {
    let html = `<h2 class="fade-in">${slide.title}</h2>`;
    
    if (slide.description) {
        html += `<p class="fade-in-delay">${slide.description}</p>`;
    }
    
    // Add interactive elements based on slide content
    if (slide.interactive) {
        switch (slide.interactive.type) {
            case 'quiz':
                html += generateQuizElement(slide.interactive);
                break;
            case 'simulation':
                html += generateSimulationElement(slide.interactive);
                break;
            default:
                html += `<div class="interactive-placeholder">${slide.interactive.description}</div>`;
        }
    }
    
    return html;
}

function generateSummarySlide(slide) {
    let html = `<h1 class="fade-in">${slide.title}</h1>`;
    
    if (slide.keyPoints) {
        html += '<div class="summary-points fade-in-delay">';
        html += '<h3>Key Points:</h3>';
        html += '<ul>';
        slide.keyPoints.forEach(point => {
            html += `<li>${point}</li>`;
        });
        html += '</ul>';
        html += '</div>';
    }
    
    if (slide.nextSteps) {
        html += '<div class="next-steps fade-in-delay-2">';
        html += '<h3>Next Steps:</h3>';
        html += `<p>${slide.nextSteps}</p>`;
        html += '</div>';
    }
    
    return html;
}

function nextSlide() {
    if (lectureState.currentSlide < lectureState.totalSlides - 1) {
        loadSlide(lectureState.currentSlide + 1);
        
        // Restart autoplay if enabled
        if (lectureState.autoplayEnabled) {
            startAutoplay();
        }
    }
}

function previousSlide() {
    if (lectureState.currentSlide > 0) {
        loadSlide(lectureState.currentSlide - 1);
        
        // Restart autoplay if enabled
        if (lectureState.autoplayEnabled) {
            startAutoplay();
        }
    }
}

function goToSlide(slideIndex) {
    if (slideIndex >= 0 && slideIndex < lectureState.totalSlides) {
        loadSlide(slideIndex);
        
        // Restart autoplay if enabled
        if (lectureState.autoplayEnabled) {
            startAutoplay();
        }
    }
}

// ===== UI UPDATES =====
function updateSlideCounter() {
    const counter = document.getElementById('slideCounter');
    if (counter) {
        counter.textContent = `${lectureState.currentSlide + 1} / ${lectureState.totalSlides}`;
    }
}

function updateProgressBar() {
    const progressBar = document.getElementById('lectureProgress');
    if (progressBar) {
        const progress = ((lectureState.currentSlide + 1) / lectureState.totalSlides) * 100;
        progressBar.style.width = `${progress}%`;
    }
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    if (prevBtn) {
        prevBtn.disabled = lectureState.currentSlide === 0;
    }
    
    if (nextBtn) {
        nextBtn.disabled = lectureState.currentSlide === lectureState.totalSlides - 1;
    }
}

function generateSlideThumbnails() {
    const thumbnailsContainer = document.getElementById('slideThumbnails');
    if (!thumbnailsContainer || !lectureState.currentLecture) return;
    
    thumbnailsContainer.innerHTML = '';
    
    lectureState.currentLecture.slides.forEach((slide, index) => {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'thumbnail';
        thumbnail.textContent = index + 1;
        thumbnail.onclick = () => goToSlide(index);
        
        if (index === lectureState.currentSlide) {
            thumbnail.classList.add('active');
        }
        
        thumbnailsContainer.appendChild(thumbnail);
    });
}

function updateThumbnailSelection() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach((thumb, index) => {
        thumb.classList.toggle('active', index === lectureState.currentSlide);
    });
}

// ===== AUTOPLAY FUNCTIONALITY =====
function toggleAutoplay() {
    lectureState.autoplayEnabled = !lectureState.autoplayEnabled;
    
    const autoplayIcon = document.getElementById('autoplayIcon');
    if (autoplayIcon) {
        autoplayIcon.className = lectureState.autoplayEnabled ? 'fas fa-pause' : 'fas fa-play';
    }
    
    if (lectureState.autoplayEnabled) {
        startAutoplay();
    } else {
        stopAutoplay();
    }
}

function startAutoplay() {
    stopAutoplay(); // Clear any existing timer
    
    if (lectureState.autoplayEnabled && lectureState.currentSlide < lectureState.totalSlides - 1) {
        autoplayTimer = setTimeout(() => {
            nextSlide();
        }, lectureState.autoplaySpeed);
    }
}

function stopAutoplay() {
    if (autoplayTimer) {
        clearTimeout(autoplayTimer);
        autoplayTimer = null;
    }
}

// ===== NOTES FUNCTIONALITY =====
function toggleNotes() {
    const notesPanel = document.getElementById('notesPanel');
    if (notesPanel) {
        lectureState.notesOpen = !lectureState.notesOpen;
        notesPanel.classList.toggle('open', lectureState.notesOpen);
        
        if (lectureState.notesOpen) {
            loadNotes();
        }
    }
}

function saveNotes() {
    const notesTextarea = document.getElementById('notesTextarea');
    if (notesTextarea && lectureState.currentLecture) {
        const notes = notesTextarea.value;
        const lectureId = lectureState.currentLecture.id;
        
        localStorage.setItem(`lecture_notes_${lectureId}`, notes);
        
        // Show save confirmation
        showNotification('Notes saved successfully!', 'success');
    }
}

function loadNotes() {
    const notesTextarea = document.getElementById('notesTextarea');
    if (notesTextarea && lectureState.currentLecture) {
        const lectureId = lectureState.currentLecture.id;
        const savedNotes = localStorage.getItem(`lecture_notes_${lectureId}`) || '';
        notesTextarea.value = savedNotes;
    }
}

function exportNotes() {
    const notesTextarea = document.getElementById('notesTextarea');
    if (notesTextarea && lectureState.currentLecture) {
        const notes = notesTextarea.value;
        const lectureTitle = lectureState.currentLecture.title;
        
        const blob = new Blob([notes], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${lectureTitle}_notes.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('Notes exported successfully!', 'success');
    }
}

// ===== SETTINGS MANAGEMENT =====
function openSettings() {
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        settingsModal.style.display = 'flex';
        loadSettingsToModal();
    }
}

function closeSettings() {
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        settingsModal.style.display = 'none';
    }
}

function loadSettingsToModal() {
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    const animationSpeedSelect = document.getElementById('animationSpeed');
    const showAnimationsCheck = document.getElementById('showAnimations');
    const showNotesCheck = document.getElementById('showNotes');
    
    if (autoplaySpeedSlider) {
        autoplaySpeedSlider.value = lectureState.settings.autoplaySpeed;
        updateSpeedDisplay();
    }
    
    if (animationSpeedSelect) {
        animationSpeedSelect.value = lectureState.settings.animationSpeed;
    }
    
    if (showAnimationsCheck) {
        showAnimationsCheck.checked = lectureState.settings.showAnimations;
    }
    
    if (showNotesCheck) {
        showNotesCheck.checked = lectureState.settings.showNotes;
    }
}

function saveSettings() {
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    const animationSpeedSelect = document.getElementById('animationSpeed');
    const showAnimationsCheck = document.getElementById('showAnimations');
    const showNotesCheck = document.getElementById('showNotes');
    
    if (autoplaySpeedSlider) {
        lectureState.settings.autoplaySpeed = parseInt(autoplaySpeedSlider.value);
        lectureState.autoplaySpeed = lectureState.settings.autoplaySpeed * 1000;
    }
    
    if (animationSpeedSelect) {
        lectureState.settings.animationSpeed = animationSpeedSelect.value;
    }
    
    if (showAnimationsCheck) {
        lectureState.settings.showAnimations = showAnimationsCheck.checked;
        lectureState.animationsEnabled = showAnimationsCheck.checked;
    }
    
    if (showNotesCheck) {
        lectureState.settings.showNotes = showNotesCheck.checked;
    }
    
    // Save to localStorage
    localStorage.setItem('lecture_settings', JSON.stringify(lectureState.settings));
    
    closeSettings();
    showNotification('Settings saved successfully!', 'success');
}

function resetSettings() {
    lectureState.settings = {
        autoplaySpeed: 8,
        animationSpeed: 'normal',
        showAnimations: true,
        showNotes: true
    };
    
    loadSettingsToModal();
    showNotification('Settings reset to default!', 'info');
}

function updateSpeedDisplay() {
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    const speedValue = document.getElementById('speedValue');
    
    if (autoplaySpeedSlider && speedValue) {
        speedValue.textContent = `${autoplaySpeedSlider.value}s`;
    }
}

function loadLectureSettings() {
    const savedSettings = localStorage.getItem('lecture_settings');
    if (savedSettings) {
        lectureState.settings = { ...lectureState.settings, ...JSON.parse(savedSettings) };
        lectureState.autoplaySpeed = lectureState.settings.autoplaySpeed * 1000;
        lectureState.animationsEnabled = lectureState.settings.showAnimations;
    }
}

// ===== KEYBOARD CONTROLS =====
function setupKeyboardControls() {
    document.addEventListener('keydown', handleKeyboardNavigation);
}

function handleKeyboardNavigation(event) {
    if (!lectureState.currentLecture) return;
    
    switch (event.key) {
        case 'ArrowRight':
        case ' ':
            event.preventDefault();
            nextSlide();
            break;
        case 'ArrowLeft':
            event.preventDefault();
            previousSlide();
            break;
        case 'Escape':
            event.preventDefault();
            closeLecture();
            break;
        case 'f':
        case 'F':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                toggleFullscreen();
            }
            break;
        case 'n':
        case 'N':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                toggleNotes();
            }
            break;
        case 'p':
        case 'P':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                toggleAutoplay();
            }
            break;
    }
}

// ===== FULLSCREEN FUNCTIONALITY =====
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
            lectureState.fullscreen = true;
            updateFullscreenButton();
        });
    } else {
        document.exitFullscreen().then(() => {
            lectureState.fullscreen = false;
            updateFullscreenButton();
        });
    }
}

function updateFullscreenButton() {
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    if (fullscreenBtn) {
        const icon = fullscreenBtn.querySelector('i');
        if (icon) {
            icon.className = lectureState.fullscreen ? 'fas fa-compress' : 'fas fa-expand';
        }
    }
}

// ===== PROGRESS TRACKING =====
function saveLectureProgress() {
    if (lectureState.currentLecture) {
        const progress = {
            lectureId: lectureState.currentLecture.id,
            currentSlide: lectureState.currentSlide,
            totalSlides: lectureState.totalSlides,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('current_lecture_progress', JSON.stringify(progress));
    }
}

function trackLectureEvent(eventType, lectureId, data = {}) {
    const event = {
        type: eventType,
        lectureId: lectureId,
        timestamp: new Date().toISOString(),
        data: data
    };
    
    console.log('Lecture Event:', event);
    
    // Store in analytics (could be sent to server in real implementation)
    const analytics = JSON.parse(localStorage.getItem('lecture_analytics') || '[]');
    analytics.push(event);
    
    // Keep only last 1000 events
    if (analytics.length > 1000) {
        analytics.splice(0, analytics.length - 1000);
    }
    
    localStorage.setItem('lecture_analytics', JSON.stringify(analytics));
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function applySlideAnimations(slideElement, slide) {
    if (!lectureState.animationsEnabled) return;
    
    slideElement.classList.add('slide-content-animate');
    
    // Apply specific animations based on slide content
    if (slide.animations) {
        slide.animations.forEach(animation => {
            const element = slideElement.querySelector(animation.selector);
            if (element) {
                element.classList.add(animation.class);
            }
        });
    }
}

// ===== EXPORT FUNCTIONS =====
window.startLecture = startLecture;
window.closeLecture = closeLecture;
window.nextSlide = nextSlide;
window.previousSlide = previousSlide;
window.goToSlide = goToSlide;
window.toggleAutoplay = toggleAutoplay;
window.toggleNotes = toggleNotes;
window.saveNotes = saveNotes;
window.exportNotes = exportNotes;
window.openSettings = openSettings;
window.closeSettings = closeSettings;
window.saveSettings = saveSettings;
window.resetSettings = resetSettings;
window.toggleFullscreen = toggleFullscreen;
