// ===== LECTURE SYSTEM JAVASCRIPT =====

// Lecture State Management
const lectureState = {
    currentLecture: null,
    currentSlide: 0,
    totalSlides: 0,
    isPlaying: false,
    autoplayEnabled: false,
    autoplaySpeed: 8000, // 8 seconds default
    animationsEnabled: true,
    notesOpen: false,
    fullscreen: false,
    settings: {
        autoplaySpeed: 8,
        animationSpeed: 'normal',
        showAnimations: true,
        showNotes: true
    }
};

// Autoplay Timer
let autoplayTimer = null;

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeLectureSystem();
    loadLectureSettings();
    setupKeyboardControls();
});

function initializeLectureSystem() {
    console.log('Lecture System - Initializing...');
    
    // Load saved settings
    loadLectureSettings();
    
    // Setup event listeners
    setupLectureEventListeners();
    
    console.log('Lecture System initialized');
}

function setupLectureEventListeners() {
    // Settings modal events
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    if (autoplaySpeedSlider) {
        autoplaySpeedSlider.addEventListener('input', updateSpeedDisplay);
    }
    
    // Keyboard events
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Window events
    window.addEventListener('beforeunload', saveLectureProgress);
}

// ===== LECTURE MANAGEMENT =====
function startLecture(lectureId) {
    console.log(`Starting lecture: ${lectureId}`);
    
    // Load lecture data
    const lectureData = getLectureData(lectureId);
    if (!lectureData) {
        console.error('Lecture data not found:', lectureId);
        return;
    }
    
    // Set current lecture
    lectureState.currentLecture = lectureData;
    lectureState.currentSlide = 0;
    lectureState.totalSlides = lectureData.slides.length;
    
    // Show lecture viewer
    showLectureViewer();
    
    // Initialize lecture display
    initializeLectureDisplay();
    
    // Load first slide
    loadSlide(0);
    
    // Track lecture start
    trackLectureEvent('lecture_started', lectureId);
}

function showLectureViewer() {
    const lectureSelection = document.getElementById('lecture-selection');
    const lectureViewer = document.getElementById('lecture-viewer');
    
    if (lectureSelection) lectureSelection.style.display = 'none';
    if (lectureViewer) lectureViewer.style.display = 'block';
    
    // Update document title
    document.title = `${lectureState.currentLecture.title} - Virtual Medical Simulation LMS`;
}

function closeLecture() {
    // Save progress
    saveLectureProgress();
    
    // Stop autoplay
    stopAutoplay();
    
    // Hide lecture viewer
    const lectureSelection = document.getElementById('lecture-selection');
    const lectureViewer = document.getElementById('lecture-viewer');
    
    if (lectureSelection) lectureSelection.style.display = 'block';
    if (lectureViewer) lectureViewer.style.display = 'none';
    
    // Reset state
    lectureState.currentLecture = null;
    lectureState.currentSlide = 0;
    lectureState.totalSlides = 0;
    
    // Restore document title
    document.title = 'Interactive Lectures - Virtual Medical Simulation LMS';
    
    // Track lecture end
    trackLectureEvent('lecture_ended', lectureState.currentLecture?.id);
}

function initializeLectureDisplay() {
    const lecture = lectureState.currentLecture;
    
    // Update lecture title
    const titleElement = document.getElementById('currentLectureTitle');
    if (titleElement) {
        titleElement.textContent = lecture.title;
    }
    
    // Generate slide thumbnails
    generateSlideThumbnails();
    
    // Update slide counter
    updateSlideCounter();
    
    // Update progress bar
    updateProgressBar();
}

// ===== SLIDE NAVIGATION =====
function loadSlide(slideIndex) {
    if (!lectureState.currentLecture || slideIndex < 0 || slideIndex >= lectureState.totalSlides) {
        return;
    }
    
    const slide = lectureState.currentLecture.slides[slideIndex];
    const slideContent = document.getElementById('slideContent');
    
    if (!slideContent) return;
    
    // Clear previous content
    slideContent.innerHTML = '';
    
    // Create slide element
    const slideElement = createSlideElement(slide, slideIndex);
    slideContent.appendChild(slideElement);
    
    // Update state
    lectureState.currentSlide = slideIndex;
    
    // Update UI
    updateSlideCounter();
    updateProgressBar();
    updateNavigationButtons();
    updateThumbnailSelection();
    
    // Apply animations if enabled
    if (lectureState.animationsEnabled) {
        applySlideAnimations(slideElement, slide);
    }
    
    // Track slide view
    trackLectureEvent('slide_viewed', lectureState.currentLecture.id, { slideIndex });
}

function createSlideElement(slide, slideIndex) {
    const slideElement = document.createElement('div');
    slideElement.className = 'slide active';
    slideElement.innerHTML = generateSlideHTML(slide, slideIndex);
    
    return slideElement;
}

function generateSlideHTML(slide, slideIndex) {
    // Use modern slide generation for enhanced visuals
    return generateModernSlideHTML(slide, slideIndex);
}

function generateTitleSlide(slide) {
    return `
        <div class="slide-title-content">
            <h1 class="fade-in">${slide.title}</h1>
            ${slide.subtitle ? `<h2 class="fade-in-delay">${slide.subtitle}</h2>` : ''}
            ${slide.description ? `<p class="fade-in-delay-2">${slide.description}</p>` : ''}
            ${slide.visual ? generateVisualElement(slide.visual) : ''}
        </div>
    `;
}

function generateContentSlide(slide) {
    let html = `<h2 class="fade-in">${slide.title}</h2>`;
    
    if (slide.content) {
        slide.content.forEach((item, index) => {
            const delay = index + 1;
            if (typeof item === 'string') {
                html += `<p class="fade-in-delay-${Math.min(delay, 3)}">${item}</p>`;
            } else if (item.type === 'list') {
                html += `<ul class="fade-in-delay-${Math.min(delay, 3)}">`;
                item.items.forEach(listItem => {
                    html += `<li>${listItem}</li>`;
                });
                html += '</ul>';
            } else if (item.type === 'highlight') {
                html += `<p class="highlight fade-in-delay-${Math.min(delay, 3)}">${item.text}</p>`;
            }
        });
    }
    
    if (slide.visual) {
        html += generateVisualElement(slide.visual);
    }
    
    return html;
}

function generateVisualSlide(slide) {
    let html = `<h2 class="fade-in">${slide.title}</h2>`;
    
    if (slide.description) {
        html += `<p class="fade-in-delay">${slide.description}</p>`;
    }
    
    html += generateVisualElement(slide.visual);
    
    return html;
}

function generateVisualElement(visual) {
    if (!visual) return '';
    
    switch (visual.type) {
        case 'heartbeat':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="heartbeat-demo">
                        <i class="fas fa-heartbeat heartbeat" style="font-size: 4rem; color: #e74c3c;"></i>
                        <p>Normal Heart Rate: 60-100 bpm</p>
                    </div>
                </div>
            `;
        
        case 'ecg':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="ecg-wave">
                        <div class="ecg-line"></div>
                        <div class="ecg-pulse"></div>
                    </div>
                    <p>Electrocardiogram (ECG) Waveform</p>
                </div>
            `;
        
        case 'blood-pressure':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="pressure-gauge pulse">
                        <div class="pressure-needle"></div>
                    </div>
                    <p>Blood Pressure Measurement</p>
                </div>
            `;
        
        case 'breathing':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="lung-visual breathing" style="width: 200px; height: 150px; background: #3498db; border-radius: 50%; margin: 0 auto; opacity: 0.7;"></div>
                    <p>Respiratory Cycle</p>
                </div>
            `;
        
        case 'gas-flow':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="gas-flow">
                        <div class="gas-bubble"></div>
                        <div class="gas-bubble"></div>
                        <div class="gas-bubble"></div>
                        <div class="gas-bubble"></div>
                    </div>
                    <p>Gas Flow Visualization</p>
                </div>
            `;
        
        case 'flowmeter':
            return `
                <div class="visual-container fade-in-delay-2">
                    <div class="flowmeter">
                        <div class="flowmeter-ball"></div>
                    </div>
                    <p>Flowmeter Operation</p>
                </div>
            `;
        
        default:
            return `<div class="visual-placeholder fade-in-delay-2">${visual.description || 'Visual Element'}</div>`;
    }
}

function generateInteractiveSlide(slide) {
    let html = `<h2 class="fade-in">${slide.title}</h2>`;
    
    if (slide.description) {
        html += `<p class="fade-in-delay">${slide.description}</p>`;
    }
    
    // Add interactive elements based on slide content
    if (slide.interactive) {
        switch (slide.interactive.type) {
            case 'quiz':
                html += generateQuizElement(slide.interactive);
                break;
            case 'simulation':
                html += generateSimulationElement(slide.interactive);
                break;
            default:
                html += `<div class="interactive-placeholder">${slide.interactive.description}</div>`;
        }
    }
    
    return html;
}

function generateSummarySlide(slide) {
    let html = `<h1 class="fade-in">${slide.title}</h1>`;
    
    if (slide.keyPoints) {
        html += '<div class="summary-points fade-in-delay">';
        html += '<h3>Key Points:</h3>';
        html += '<ul>';
        slide.keyPoints.forEach(point => {
            html += `<li>${point}</li>`;
        });
        html += '</ul>';
        html += '</div>';
    }
    
    if (slide.nextSteps) {
        html += '<div class="next-steps fade-in-delay-2">';
        html += '<h3>Next Steps:</h3>';
        html += `<p>${slide.nextSteps}</p>`;
        html += '</div>';
    }
    
    return html;
}

function nextSlide() {
    if (lectureState.currentSlide < lectureState.totalSlides - 1) {
        loadSlide(lectureState.currentSlide + 1);
        
        // Restart autoplay if enabled
        if (lectureState.autoplayEnabled) {
            startAutoplay();
        }
    }
}

function previousSlide() {
    if (lectureState.currentSlide > 0) {
        loadSlide(lectureState.currentSlide - 1);
        
        // Restart autoplay if enabled
        if (lectureState.autoplayEnabled) {
            startAutoplay();
        }
    }
}

function goToSlide(slideIndex) {
    if (slideIndex >= 0 && slideIndex < lectureState.totalSlides) {
        loadSlide(slideIndex);
        
        // Restart autoplay if enabled
        if (lectureState.autoplayEnabled) {
            startAutoplay();
        }
    }
}

// ===== UI UPDATES =====
function updateSlideCounter() {
    const counter = document.getElementById('slideCounter');
    if (counter) {
        counter.textContent = `${lectureState.currentSlide + 1} / ${lectureState.totalSlides}`;
    }
}

function updateProgressBar() {
    const progressBar = document.getElementById('lectureProgress');
    if (progressBar) {
        const progress = ((lectureState.currentSlide + 1) / lectureState.totalSlides) * 100;
        progressBar.style.width = `${progress}%`;
    }
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    if (prevBtn) {
        prevBtn.disabled = lectureState.currentSlide === 0;
    }
    
    if (nextBtn) {
        nextBtn.disabled = lectureState.currentSlide === lectureState.totalSlides - 1;
    }
}

function generateSlideThumbnails() {
    const thumbnailsContainer = document.getElementById('slideThumbnails');
    if (!thumbnailsContainer || !lectureState.currentLecture) return;
    
    thumbnailsContainer.innerHTML = '';
    
    lectureState.currentLecture.slides.forEach((slide, index) => {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'thumbnail';
        thumbnail.textContent = index + 1;
        thumbnail.onclick = () => goToSlide(index);
        
        if (index === lectureState.currentSlide) {
            thumbnail.classList.add('active');
        }
        
        thumbnailsContainer.appendChild(thumbnail);
    });
}

function updateThumbnailSelection() {
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach((thumb, index) => {
        thumb.classList.toggle('active', index === lectureState.currentSlide);
    });
}

// ===== AUTOPLAY FUNCTIONALITY =====
function toggleAutoplay() {
    lectureState.autoplayEnabled = !lectureState.autoplayEnabled;
    
    const autoplayIcon = document.getElementById('autoplayIcon');
    if (autoplayIcon) {
        autoplayIcon.className = lectureState.autoplayEnabled ? 'fas fa-pause' : 'fas fa-play';
    }
    
    if (lectureState.autoplayEnabled) {
        startAutoplay();
    } else {
        stopAutoplay();
    }
}

function startAutoplay() {
    stopAutoplay(); // Clear any existing timer
    
    if (lectureState.autoplayEnabled && lectureState.currentSlide < lectureState.totalSlides - 1) {
        autoplayTimer = setTimeout(() => {
            nextSlide();
        }, lectureState.autoplaySpeed);
    }
}

function stopAutoplay() {
    if (autoplayTimer) {
        clearTimeout(autoplayTimer);
        autoplayTimer = null;
    }
}

// ===== NOTES FUNCTIONALITY =====
function toggleNotes() {
    const notesPanel = document.getElementById('notesPanel');
    if (notesPanel) {
        lectureState.notesOpen = !lectureState.notesOpen;
        notesPanel.classList.toggle('open', lectureState.notesOpen);
        
        if (lectureState.notesOpen) {
            loadNotes();
        }
    }
}

function saveNotes() {
    const notesTextarea = document.getElementById('notesTextarea');
    if (notesTextarea && lectureState.currentLecture) {
        const notes = notesTextarea.value;
        const lectureId = lectureState.currentLecture.id;
        
        localStorage.setItem(`lecture_notes_${lectureId}`, notes);
        
        // Show save confirmation
        showNotification('Notes saved successfully!', 'success');
    }
}

function loadNotes() {
    const notesTextarea = document.getElementById('notesTextarea');
    if (notesTextarea && lectureState.currentLecture) {
        const lectureId = lectureState.currentLecture.id;
        const savedNotes = localStorage.getItem(`lecture_notes_${lectureId}`) || '';
        notesTextarea.value = savedNotes;
    }
}

function exportNotes() {
    const notesTextarea = document.getElementById('notesTextarea');
    if (notesTextarea && lectureState.currentLecture) {
        const notes = notesTextarea.value;
        const lectureTitle = lectureState.currentLecture.title;
        
        const blob = new Blob([notes], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${lectureTitle}_notes.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('Notes exported successfully!', 'success');
    }
}

// ===== SETTINGS MANAGEMENT =====
function openSettings() {
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        settingsModal.style.display = 'flex';
        loadSettingsToModal();
    }
}

function closeSettings() {
    const settingsModal = document.getElementById('settingsModal');
    if (settingsModal) {
        settingsModal.style.display = 'none';
    }
}

function loadSettingsToModal() {
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    const animationSpeedSelect = document.getElementById('animationSpeed');
    const showAnimationsCheck = document.getElementById('showAnimations');
    const showNotesCheck = document.getElementById('showNotes');
    
    if (autoplaySpeedSlider) {
        autoplaySpeedSlider.value = lectureState.settings.autoplaySpeed;
        updateSpeedDisplay();
    }
    
    if (animationSpeedSelect) {
        animationSpeedSelect.value = lectureState.settings.animationSpeed;
    }
    
    if (showAnimationsCheck) {
        showAnimationsCheck.checked = lectureState.settings.showAnimations;
    }
    
    if (showNotesCheck) {
        showNotesCheck.checked = lectureState.settings.showNotes;
    }
}

function saveSettings() {
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    const animationSpeedSelect = document.getElementById('animationSpeed');
    const showAnimationsCheck = document.getElementById('showAnimations');
    const showNotesCheck = document.getElementById('showNotes');
    
    if (autoplaySpeedSlider) {
        lectureState.settings.autoplaySpeed = parseInt(autoplaySpeedSlider.value);
        lectureState.autoplaySpeed = lectureState.settings.autoplaySpeed * 1000;
    }
    
    if (animationSpeedSelect) {
        lectureState.settings.animationSpeed = animationSpeedSelect.value;
    }
    
    if (showAnimationsCheck) {
        lectureState.settings.showAnimations = showAnimationsCheck.checked;
        lectureState.animationsEnabled = showAnimationsCheck.checked;
    }
    
    if (showNotesCheck) {
        lectureState.settings.showNotes = showNotesCheck.checked;
    }
    
    // Save to localStorage
    localStorage.setItem('lecture_settings', JSON.stringify(lectureState.settings));
    
    closeSettings();
    showNotification('Settings saved successfully!', 'success');
}

function resetSettings() {
    lectureState.settings = {
        autoplaySpeed: 8,
        animationSpeed: 'normal',
        showAnimations: true,
        showNotes: true
    };
    
    loadSettingsToModal();
    showNotification('Settings reset to default!', 'info');
}

function updateSpeedDisplay() {
    const autoplaySpeedSlider = document.getElementById('autoplaySpeed');
    const speedValue = document.getElementById('speedValue');
    
    if (autoplaySpeedSlider && speedValue) {
        speedValue.textContent = `${autoplaySpeedSlider.value}s`;
    }
}

function loadLectureSettings() {
    const savedSettings = localStorage.getItem('lecture_settings');
    if (savedSettings) {
        lectureState.settings = { ...lectureState.settings, ...JSON.parse(savedSettings) };
        lectureState.autoplaySpeed = lectureState.settings.autoplaySpeed * 1000;
        lectureState.animationsEnabled = lectureState.settings.showAnimations;
    }
}

// ===== KEYBOARD CONTROLS =====
function setupKeyboardControls() {
    document.addEventListener('keydown', handleKeyboardNavigation);
}

function handleKeyboardNavigation(event) {
    if (!lectureState.currentLecture) return;
    
    switch (event.key) {
        case 'ArrowRight':
        case ' ':
            event.preventDefault();
            nextSlide();
            break;
        case 'ArrowLeft':
            event.preventDefault();
            previousSlide();
            break;
        case 'Escape':
            event.preventDefault();
            closeLecture();
            break;
        case 'f':
        case 'F':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                toggleFullscreen();
            }
            break;
        case 'n':
        case 'N':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                toggleNotes();
            }
            break;
        case 'p':
        case 'P':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                toggleAutoplay();
            }
            break;
    }
}

// ===== FULLSCREEN FUNCTIONALITY =====
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
            lectureState.fullscreen = true;
            updateFullscreenButton();
        });
    } else {
        document.exitFullscreen().then(() => {
            lectureState.fullscreen = false;
            updateFullscreenButton();
        });
    }
}

function updateFullscreenButton() {
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    if (fullscreenBtn) {
        const icon = fullscreenBtn.querySelector('i');
        if (icon) {
            icon.className = lectureState.fullscreen ? 'fas fa-compress' : 'fas fa-expand';
        }
    }
}

// ===== PROGRESS TRACKING =====
function saveLectureProgress() {
    if (lectureState.currentLecture) {
        const progress = {
            lectureId: lectureState.currentLecture.id,
            currentSlide: lectureState.currentSlide,
            totalSlides: lectureState.totalSlides,
            timestamp: new Date().toISOString()
        };
        
        localStorage.setItem('current_lecture_progress', JSON.stringify(progress));
    }
}

function trackLectureEvent(eventType, lectureId, data = {}) {
    const event = {
        type: eventType,
        lectureId: lectureId,
        timestamp: new Date().toISOString(),
        data: data
    };
    
    console.log('Lecture Event:', event);
    
    // Store in analytics (could be sent to server in real implementation)
    const analytics = JSON.parse(localStorage.getItem('lecture_analytics') || '[]');
    analytics.push(event);
    
    // Keep only last 1000 events
    if (analytics.length > 1000) {
        analytics.splice(0, analytics.length - 1000);
    }
    
    localStorage.setItem('lecture_analytics', JSON.stringify(analytics));
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function applySlideAnimations(slideElement, slide) {
    if (!lectureState.animationsEnabled) return;
    
    slideElement.classList.add('slide-content-animate');
    
    // Apply specific animations based on slide content
    if (slide.animations) {
        slide.animations.forEach(animation => {
            const element = slideElement.querySelector(animation.selector);
            if (element) {
                element.classList.add(animation.class);
            }
        });
    }
}

// ===== THEME MANAGEMENT =====
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    document.documentElement.setAttribute('data-theme', newTheme);

    // Update theme toggle button
    updateThemeToggleButton(newTheme);

    // Save theme preference
    localStorage.setItem('lecture_theme', newTheme);

    // Show theme change notification
    showNotification(`Switched to ${newTheme} mode`, 'info');
}

function updateThemeToggleButton(theme) {
    const themeIcon = document.getElementById('themeIcon');
    const themeText = document.getElementById('themeText');

    if (themeIcon && themeText) {
        if (theme === 'dark') {
            themeIcon.className = 'fas fa-sun';
            themeText.textContent = 'Light';
        } else {
            themeIcon.className = 'fas fa-moon';
            themeText.textContent = 'Dark';
        }
    }
}

function initializeTheme() {
    const savedTheme = localStorage.getItem('lecture_theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeToggleButton(savedTheme);
}

// ===== ENHANCED SLIDE CONTENT GENERATION =====
function generateModernSlideHTML(slide, slideIndex) {
    let html = '';

    // Add slide content based on type with modern styling
    switch (slide.type) {
        case 'title':
            html = generateModernTitleSlide(slide);
            break;
        case 'content':
            html = generateModernContentSlide(slide);
            break;
        case 'visual':
            html = generateModernVisualSlide(slide);
            break;
        case 'interactive':
            html = generateModernInteractiveSlide(slide);
            break;
        case 'summary':
            html = generateModernSummarySlide(slide);
            break;
        default:
            html = generateModernContentSlide(slide);
    }

    return html;
}

function generateModernTitleSlide(slide) {
    return `
        <div class="modern-slide-title-content">
            <div class="title-background-pattern"></div>
            <h1 class="modern-title fade-in">${slide.title}</h1>
            ${slide.subtitle ? `<h2 class="modern-subtitle fade-in-delay">${slide.subtitle}</h2>` : ''}
            ${slide.description ? `<p class="modern-description fade-in-delay-2">${slide.description}</p>` : ''}
            ${slide.visual ? generateModernVisualElement(slide.visual) : ''}
            <div class="title-decorative-elements">
                <div class="floating-element element-1"></div>
                <div class="floating-element element-2"></div>
                <div class="floating-element element-3"></div>
            </div>
        </div>
    `;
}

function generateModernContentSlide(slide) {
    let html = `
        <div class="modern-content-layout">
            <h2 class="modern-heading fade-in">${slide.title}</h2>
            <div class="content-body">
    `;

    if (slide.content) {
        slide.content.forEach((item, index) => {
            const delay = Math.min(index + 1, 3);
            if (typeof item === 'string') {
                html += `<p class="modern-paragraph fade-in-delay-${delay}">${item}</p>`;
            } else if (item.type === 'list') {
                html += `<ul class="modern-list fade-in-delay-${delay}">`;
                item.items.forEach((listItem, itemIndex) => {
                    html += `<li class="modern-list-item" style="animation-delay: ${(delay * 0.3) + (itemIndex * 0.1)}s">${listItem}</li>`;
                });
                html += '</ul>';
            } else if (item.type === 'highlight') {
                html += `<div class="modern-highlight fade-in-delay-${delay}">
                    <div class="highlight-icon">💡</div>
                    <p>${item.text}</p>
                </div>`;
            }
        });
    }

    if (slide.visual) {
        html += `<div class="visual-section">${generateModernVisualElement(slide.visual)}</div>`;
    }

    html += `
            </div>
        </div>
    `;

    return html;
}

function generateModernVisualElement(visual) {
    if (!visual) return '';

    switch (visual.type) {
        case 'heartbeat':
            return `
                <div class="modern-visual-container fade-in-delay-2">
                    <div class="heartbeat-visualization">
                        <div class="heart-monitor">
                            <div class="monitor-screen">
                                <div class="ecg-grid"></div>
                                <div class="heartbeat-wave">
                                    <i class="fas fa-heartbeat heartbeat modern-heartbeat-icon"></i>
                                </div>
                            </div>
                        </div>
                        <div class="vital-display">
                            <div class="vital-value">75</div>
                            <div class="vital-unit">bpm</div>
                            <div class="vital-status normal">Normal</div>
                        </div>
                    </div>
                    <p class="visual-caption">Real-time Heart Rate Monitoring</p>
                </div>
            `;

        case 'ecg':
            return `
                <div class="modern-visual-container fade-in-delay-2">
                    <div class="ecg-visualization">
                        <div class="ecg-monitor">
                            <div class="ecg-wave modern-ecg">
                                <div class="ecg-line"></div>
                                <div class="ecg-pulse"></div>
                            </div>
                            <div class="ecg-controls">
                                <div class="control-group">
                                    <label>Lead II</label>
                                    <div class="led-indicator active"></div>
                                </div>
                                <div class="control-group">
                                    <label>Rate: 75 bpm</label>
                                    <div class="led-indicator active"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="visual-caption">Electrocardiogram (ECG) Waveform Analysis</p>
                </div>
            `;

        case 'blood-pressure':
            return `
                <div class="modern-visual-container fade-in-delay-2">
                    <div class="bp-visualization">
                        <div class="bp-monitor">
                            <div class="pressure-gauge modern-gauge">
                                <div class="gauge-face">
                                    <div class="gauge-markings"></div>
                                    <div class="gauge-needle pulse"></div>
                                    <div class="gauge-center"></div>
                                </div>
                            </div>
                            <div class="bp-readings">
                                <div class="reading-display">
                                    <span class="systolic">120</span>
                                    <span class="separator">/</span>
                                    <span class="diastolic">80</span>
                                    <span class="unit">mmHg</span>
                                </div>
                                <div class="bp-status normal">Normal Range</div>
                            </div>
                        </div>
                    </div>
                    <p class="visual-caption">Blood Pressure Measurement System</p>
                </div>
            `;

        case 'breathing':
            return `
                <div class="modern-visual-container fade-in-delay-2">
                    <div class="breathing-visualization">
                        <div class="lung-system">
                            <div class="lung-pair">
                                <div class="lung left-lung breathing modern-lung">
                                    <div class="lung-segments"></div>
                                </div>
                                <div class="lung right-lung breathing modern-lung">
                                    <div class="lung-segments"></div>
                                </div>
                            </div>
                            <div class="respiratory-data">
                                <div class="data-point">
                                    <span class="label">Rate:</span>
                                    <span class="value">16</span>
                                    <span class="unit">/min</span>
                                </div>
                                <div class="data-point">
                                    <span class="label">SpO2:</span>
                                    <span class="value">98</span>
                                    <span class="unit">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="visual-caption">Respiratory Cycle Monitoring</p>
                </div>
            `;

        case 'gas-flow':
            return `
                <div class="modern-visual-container fade-in-delay-2">
                    <div class="gas-flow-visualization">
                        <div class="flow-system">
                            <div class="gas-sources">
                                <div class="gas-tank oxygen">
                                    <div class="tank-label">O₂</div>
                                    <div class="flow-indicator active"></div>
                                </div>
                                <div class="gas-tank nitrous">
                                    <div class="tank-label">N₂O</div>
                                    <div class="flow-indicator"></div>
                                </div>
                            </div>
                            <div class="flow-paths">
                                <div class="flow-line oxygen-flow">
                                    <div class="gas-flow modern-flow">
                                        <div class="gas-bubble"></div>
                                        <div class="gas-bubble"></div>
                                        <div class="gas-bubble"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="mixing-chamber">
                                <div class="chamber-display">Mixed Gas</div>
                            </div>
                        </div>
                    </div>
                    <p class="visual-caption">Anesthesia Gas Flow Dynamics</p>
                </div>
            `;

        case 'flowmeter':
            return `
                <div class="modern-visual-container fade-in-delay-2">
                    <div class="flowmeter-visualization">
                        <div class="flowmeter-assembly modern-flowmeter">
                            <div class="flowmeter-tube">
                                <div class="tube-scale">
                                    <div class="scale-mark" data-value="5">5</div>
                                    <div class="scale-mark" data-value="4">4</div>
                                    <div class="scale-mark" data-value="3">3</div>
                                    <div class="scale-mark" data-value="2">2</div>
                                    <div class="scale-mark" data-value="1">1</div>
                                    <div class="scale-mark" data-value="0">0</div>
                                </div>
                                <div class="flowmeter-ball animated-ball"></div>
                                <div class="gas-flow-stream">
                                    <div class="flow-bubble"></div>
                                    <div class="flow-bubble"></div>
                                    <div class="flow-bubble"></div>
                                </div>
                            </div>
                            <div class="flow-reading">
                                <span class="reading-value">2.5</span>
                                <span class="reading-unit">L/min</span>
                            </div>
                        </div>
                    </div>
                    <p class="visual-caption">Precision Flowmeter Operation</p>
                </div>
            `;

        default:
            return `
                <div class="modern-visual-placeholder fade-in-delay-2">
                    <div class="placeholder-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <p>${visual.description || 'Visual Element'}</p>
                </div>
            `;
    }
}

function generateModernVisualSlide(slide) {
    let html = `
        <div class="modern-content-layout">
            <h2 class="modern-heading fade-in">${slide.title}</h2>
            <div class="content-body">
    `;

    if (slide.description) {
        html += `<p class="modern-paragraph fade-in-delay">${slide.description}</p>`;
    }

    html += `<div class="visual-section">${generateModernVisualElement(slide.visual)}</div>`;

    html += `
            </div>
        </div>
    `;

    return html;
}

function generateModernInteractiveSlide(slide) {
    let html = `
        <div class="modern-content-layout">
            <h2 class="modern-heading fade-in">${slide.title}</h2>
            <div class="content-body">
    `;

    if (slide.description) {
        html += `<p class="modern-paragraph fade-in-delay">${slide.description}</p>`;
    }

    // Add interactive elements based on slide content
    if (slide.interactive) {
        switch (slide.interactive.type) {
            case 'quiz':
                html += generateModernQuizElement(slide.interactive);
                break;
            case 'simulation':
                html += generateModernSimulationElement(slide.interactive);
                break;
            default:
                html += `<div class="modern-interactive-placeholder">${slide.interactive.description}</div>`;
        }
    }

    html += `
            </div>
        </div>
    `;

    return html;
}

function generateModernSummarySlide(slide) {
    let html = `
        <div class="modern-slide-title-content">
            <div class="title-background-pattern"></div>
            <h1 class="modern-title fade-in">${slide.title}</h1>
    `;

    if (slide.keyPoints) {
        html += `
            <div class="modern-summary-points fade-in-delay">
                <h3 class="summary-heading">Key Points:</h3>
                <ul class="modern-list">
        `;
        slide.keyPoints.forEach((point, index) => {
            html += `<li class="modern-list-item" style="animation-delay: ${0.5 + (index * 0.1)}s">${point}</li>`;
        });
        html += `
                </ul>
            </div>
        `;
    }

    if (slide.nextSteps) {
        html += `
            <div class="modern-next-steps fade-in-delay-2">
                <h3 class="summary-heading">Next Steps:</h3>
                <p class="modern-description">${slide.nextSteps}</p>
            </div>
        `;
    }

    html += `
            <div class="title-decorative-elements">
                <div class="floating-element element-1"></div>
                <div class="floating-element element-2"></div>
                <div class="floating-element element-3"></div>
            </div>
        </div>
    `;

    return html;
}

function generateModernQuizElement(interactive) {
    let html = `
        <div class="modern-quiz-container fade-in-delay-2">
            <div class="quiz-header">
                <div class="quiz-icon">🧠</div>
                <h4 class="quiz-question">${interactive.question}</h4>
            </div>
            <div class="modern-quiz-options">
    `;

    interactive.options.forEach((option, index) => {
        html += `
            <button class="modern-quiz-option" onclick="selectQuizAnswer(${index}, ${interactive.correct})">
                <span class="option-letter">${String.fromCharCode(65 + index)}</span>
                <span class="option-text">${option}</span>
            </button>
        `;
    });

    html += `
            </div>
            <div class="modern-quiz-feedback" id="quizFeedback" style="display: none;"></div>
        </div>
    `;

    return html;
}

function generateModernSimulationElement(interactive) {
    let html = `
        <div class="modern-simulation-container fade-in-delay-2">
            <div class="simulation-header">
                <div class="simulation-icon">🔬</div>
                <h4>${interactive.title || 'Interactive Simulation'}</h4>
            </div>
            <p class="simulation-description">${interactive.description}</p>
            <div class="simulation-controls">
                <button class="modern-simulation-btn" onclick="launchSimulation()">
                    <i class="fas fa-play"></i>
                    Launch Simulation
                </button>
            </div>
        </div>
    `;

    return html;
}

// ===== INITIALIZATION UPDATE =====
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    initializeLectureSystem();
    loadLectureSettings();
    setupKeyboardControls();
});

// ===== EXPORT FUNCTIONS =====
window.startLecture = startLecture;
window.closeLecture = closeLecture;
window.nextSlide = nextSlide;
window.previousSlide = previousSlide;
window.goToSlide = goToSlide;
window.toggleAutoplay = toggleAutoplay;
window.toggleNotes = toggleNotes;
window.saveNotes = saveNotes;
window.exportNotes = exportNotes;
window.openSettings = openSettings;
window.closeSettings = closeSettings;
window.saveSettings = saveSettings;
window.resetSettings = resetSettings;
window.toggleFullscreen = toggleFullscreen;
window.toggleTheme = toggleTheme;
