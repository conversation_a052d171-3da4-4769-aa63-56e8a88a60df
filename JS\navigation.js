// ===== NAVIGATION MANAGEMENT JAVASCRIPT =====

// Navigation State
const navigationState = {
    currentSection: 'home',
    isMenuOpen: false,
    scrollPosition: 0,
    lastScrollPosition: 0
};

// ===== ADVANCED NAVIGATION FEATURES =====
document.addEventListener('DOMContentLoaded', function() {
    initializeAdvancedNavigation();
    setupIntersectionObserver();
    setupMobileNavigation();
    setupKeyboardNavigation();
});

function initializeAdvancedNavigation() {
    // Add scroll spy functionality
    setupScrollSpy();
    
    // Add smooth scroll behavior
    enhanceSmoothScrolling();
    
    // Add navigation breadcrumbs
    setupBreadcrumbs();
    
    // Add progress indicator
    setupProgressIndicator();
}

// ===== SCROLL SPY IMPLEMENTATION =====
function setupScrollSpy() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    const observerOptions = {
        root: null,
        rootMargin: '-20% 0px -80% 0px',
        threshold: 0
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.id;
                updateActiveNavLink(sectionId);
                navigationState.currentSection = sectionId;
                updateBreadcrumbs(sectionId);
            }
        });
    }, observerOptions);
    
    sections.forEach(section => {
        observer.observe(section);
    });
}

function updateActiveNavLink(sectionId) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${sectionId}`) {
            link.classList.add('active');
        }
    });
}

// ===== INTERSECTION OBSERVER FOR ANIMATIONS =====
function setupIntersectionObserver() {
    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.module-card, .simulation-card, .stat-item');
    animatedElements.forEach(el => {
        animationObserver.observe(el);
    });
}

// ===== MOBILE NAVIGATION =====
function setupMobileNavigation() {
    // Create mobile menu toggle if screen is small
    if (window.innerWidth <= 768) {
        createMobileMenuToggle();
    }
    
    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 768 && !document.querySelector('.mobile-menu-toggle')) {
            createMobileMenuToggle();
        } else if (window.innerWidth > 768) {
            removeMobileMenuToggle();
        }
    });
}

function createMobileMenuToggle() {
    const header = document.querySelector('.main-header');
    const navigation = document.querySelector('.main-navigation');
    
    if (!header || !navigation) return;
    
    // Create toggle button
    const toggleButton = document.createElement('button');
    toggleButton.className = 'mobile-menu-toggle';
    toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
    toggleButton.setAttribute('aria-label', 'Toggle navigation menu');
    
    // Add toggle functionality
    toggleButton.addEventListener('click', toggleMobileMenu);
    
    // Insert toggle button
    const headerContent = header.querySelector('.header-content');
    headerContent.appendChild(toggleButton);
    
    // Make navigation collapsible
    navigation.classList.add('mobile-nav');
}

function removeMobileMenuToggle() {
    const toggleButton = document.querySelector('.mobile-menu-toggle');
    const navigation = document.querySelector('.main-navigation');
    
    if (toggleButton) {
        toggleButton.remove();
    }
    
    if (navigation) {
        navigation.classList.remove('mobile-nav', 'mobile-nav-open');
        navigationState.isMenuOpen = false;
    }
}

function toggleMobileMenu() {
    const navigation = document.querySelector('.main-navigation');
    const toggleButton = document.querySelector('.mobile-menu-toggle');
    
    if (!navigation || !toggleButton) return;
    
    navigationState.isMenuOpen = !navigationState.isMenuOpen;
    
    if (navigationState.isMenuOpen) {
        navigation.classList.add('mobile-nav-open');
        toggleButton.innerHTML = '<i class="fas fa-times"></i>';
        document.body.style.overflow = 'hidden';
    } else {
        navigation.classList.remove('mobile-nav-open');
        toggleButton.innerHTML = '<i class="fas fa-bars"></i>';
        document.body.style.overflow = 'auto';
    }
}

// ===== ENHANCED SMOOTH SCROLLING =====
function enhanceSmoothScrolling() {
    // Add easing function for smoother scrolling
    const easeInOutCubic = (t) => {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };
    
    // Override default smooth scroll with custom implementation
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                smoothScrollToElement(targetElement, 800, easeInOutCubic);
                
                // Close mobile menu if open
                if (navigationState.isMenuOpen) {
                    toggleMobileMenu();
                }
            }
        });
    });
}

function smoothScrollToElement(element, duration = 800, easing = null) {
    const headerHeight = document.querySelector('.main-header').offsetHeight;
    const targetPosition = element.offsetTop - headerHeight - 20;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;
    
    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        
        const easedProgress = easing ? easing(progress) : progress;
        const currentPosition = startPosition + (distance * easedProgress);
        
        window.scrollTo(0, currentPosition);
        
        if (timeElapsed < duration) {
            requestAnimationFrame(animation);
        }
    }
    
    requestAnimationFrame(animation);
}

// ===== BREADCRUMBS SYSTEM =====
function setupBreadcrumbs() {
    const breadcrumbContainer = document.createElement('div');
    breadcrumbContainer.className = 'breadcrumb-container';
    breadcrumbContainer.innerHTML = '<nav class="breadcrumbs" aria-label="Breadcrumb navigation"></nav>';
    
    const header = document.querySelector('.main-header');
    if (header) {
        header.appendChild(breadcrumbContainer);
    }
}

function updateBreadcrumbs(currentSection) {
    const breadcrumbsNav = document.querySelector('.breadcrumbs');
    if (!breadcrumbsNav) return;
    
    const sectionTitles = {
        'home': 'Home',
        'modules': 'Learning Modules',
        'simulations': 'Interactive Simulations',
        'assessments': 'Assessments',
        'progress': 'Progress Tracking'
    };
    
    const breadcrumbItems = ['Home'];
    
    if (currentSection !== 'home') {
        breadcrumbItems.push(sectionTitles[currentSection] || currentSection);
    }
    
    breadcrumbsNav.innerHTML = breadcrumbItems.map((item, index) => {
        const isLast = index === breadcrumbItems.length - 1;
        const href = index === 0 ? '#home' : `#${Object.keys(sectionTitles).find(key => sectionTitles[key] === item)}`;
        
        return isLast 
            ? `<span class="breadcrumb-current">${item}</span>`
            : `<a href="${href}" class="breadcrumb-link">${item}</a>`;
    }).join('<span class="breadcrumb-separator">›</span>');
}

// ===== PROGRESS INDICATOR =====
function setupProgressIndicator() {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress-bar';
    document.body.appendChild(progressBar);
    
    window.addEventListener('scroll', updateScrollProgress);
}

function updateScrollProgress() {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;
    
    const progressBar = document.querySelector('.scroll-progress-bar');
    if (progressBar) {
        progressBar.style.width = `${Math.min(scrollPercent, 100)}%`;
    }
    
    // Update navigation state
    navigationState.lastScrollPosition = navigationState.scrollPosition;
    navigationState.scrollPosition = scrollTop;
    
    // Hide/show header on scroll
    handleHeaderVisibility();
}

function handleHeaderVisibility() {
    const header = document.querySelector('.main-header');
    if (!header) return;
    
    const scrollDifference = navigationState.scrollPosition - navigationState.lastScrollPosition;
    
    if (scrollDifference > 5 && navigationState.scrollPosition > 100) {
        // Scrolling down - hide header
        header.style.transform = 'translateY(-100%)';
    } else if (scrollDifference < -5 || navigationState.scrollPosition <= 100) {
        // Scrolling up or at top - show header
        header.style.transform = 'translateY(0)';
    }
}

// ===== KEYBOARD NAVIGATION =====
function setupKeyboardNavigation() {
    let focusableElements = [];
    
    function updateFocusableElements() {
        focusableElements = Array.from(document.querySelectorAll(
            'a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
        ));
    }
    
    updateFocusableElements();
    
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'Tab':
                handleTabNavigation(e);
                break;
            case 'Escape':
                handleEscapeKey(e);
                break;
            case 'Enter':
            case ' ':
                handleActivation(e);
                break;
            case 'ArrowUp':
            case 'ArrowDown':
                handleArrowNavigation(e);
                break;
        }
    });
    
    function handleTabNavigation(e) {
        updateFocusableElements();
        
        if (focusableElements.length === 0) return;
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
    
    function handleEscapeKey(e) {
        if (navigationState.isMenuOpen) {
            toggleMobileMenu();
        }
        
        if (appState.modalOpen) {
            closeModal();
        }
    }
    
    function handleActivation(e) {
        const activeElement = document.activeElement;
        
        if (activeElement.tagName === 'A' || activeElement.tagName === 'BUTTON') {
            // Let default behavior handle it
            return;
        }
        
        // Handle custom activations
        if (activeElement.classList.contains('module-card')) {
            e.preventDefault();
            const moduleId = activeElement.dataset.module;
            if (moduleId) {
                viewModuleDetails(moduleId);
            }
        }
    }
    
    function handleArrowNavigation(e) {
        const activeElement = document.activeElement;
        
        // Navigate through module cards
        if (activeElement.classList.contains('module-card')) {
            e.preventDefault();
            const moduleCards = Array.from(document.querySelectorAll('.module-card'));
            const currentIndex = moduleCards.indexOf(activeElement);
            
            let nextIndex;
            if (e.key === 'ArrowDown') {
                nextIndex = (currentIndex + 1) % moduleCards.length;
            } else {
                nextIndex = (currentIndex - 1 + moduleCards.length) % moduleCards.length;
            }
            
            moduleCards[nextIndex].focus();
        }
    }
}

// ===== NAVIGATION ANALYTICS =====
function trackNavigation(section, method = 'click') {
    // Track navigation events for analytics
    const navigationEvent = {
        timestamp: new Date().toISOString(),
        section: section,
        method: method,
        previousSection: navigationState.currentSection,
        scrollPosition: navigationState.scrollPosition
    };
    
    console.log('Navigation tracked:', navigationEvent);
    
    // Store in localStorage for analytics
    const navigationHistory = JSON.parse(localStorage.getItem('vms_navigation_history') || '[]');
    navigationHistory.push(navigationEvent);
    
    // Keep only last 100 navigation events
    if (navigationHistory.length > 100) {
        navigationHistory.splice(0, navigationHistory.length - 100);
    }
    
    localStorage.setItem('vms_navigation_history', JSON.stringify(navigationHistory));
}

// ===== EXPORT FUNCTIONS =====
window.smoothScrollToElement = smoothScrollToElement;
window.toggleMobileMenu = toggleMobileMenu;
window.trackNavigation = trackNavigation;
