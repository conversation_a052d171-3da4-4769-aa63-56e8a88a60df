/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .hero-content h2 {
        font-size: 3.5rem;
    }
    
    .modules-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .simulations-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .header-content {
        padding: 1rem 1.5rem;
    }
    
    .hero-content h2 {
        font-size: 2.8rem;
    }
    
    .modules-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .main-navigation ul {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .hero-content h2 {
        font-size: 2.5rem;
    }
    
    .hero-content p {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .stat-item {
        min-width: 120px;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .simulations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .module-actions {
        flex-direction: column;
    }
    
    .section-header h2 {
        font-size: 2.2rem;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .header-content {
        padding: 1rem;
    }
    
    .logo-section {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .logo-section h1 {
        font-size: 1.5rem;
    }
    
    .subtitle {
        font-size: 0.8rem;
    }
    
    .main-navigation ul {
        gap: 0.5rem;
    }
    
    .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .hero-section {
        padding: 3rem 1rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
        line-height: 1.2;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }
    
    .stat-item {
        min-width: 200px;
        max-width: 250px;
    }
    
    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .modules-section,
    .simulations-section {
        padding: 3rem 1rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .section-header p {
        font-size: 1rem;
    }
    
    .module-card {
        margin-bottom: 1rem;
    }
    
    .module-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .module-icon {
        font-size: 2.5rem;
    }
    
    .module-header h3 {
        font-size: 1.2rem;
    }
    
    .module-content {
        padding: 1rem 1.5rem;
    }
    
    .module-actions {
        padding: 1rem 1.5rem 1.5rem;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .simulations-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .simulation-card {
        padding: 1.25rem;
    }
    
    .simulation-preview {
        width: 60px;
        height: 60px;
    }
    
    .simulation-icon {
        font-size: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .modal-overlay {
        padding: 1rem;
    }
    
    .modal-content {
        max-height: 90vh;
    }
    
    .modal-header,
    .modal-body {
        padding: 1.5rem;
    }
    
    .module-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .header-content {
        padding: 0.75rem;
    }
    
    .logo-icon {
        font-size: 2rem;
    }
    
    .logo-section h1 {
        font-size: 1.3rem;
    }
    
    .main-navigation ul {
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
    }
    
    .nav-link {
        display: block;
        text-align: center;
        padding: 0.5rem;
        width: 100%;
    }
    
    .hero-section {
        padding: 2rem 0.75rem;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .hero-content p {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }
    
    .stat-item {
        min-width: 100%;
        max-width: none;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .cta-button {
        padding: 0.75rem 1.25rem;
        font-size: 0.95rem;
        width: 100%;
        max-width: 250px;
    }
    
    .modules-section,
    .simulations-section {
        padding: 2rem 0.75rem;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
    }
    
    .module-header {
        padding: 1.25rem 1rem 0.75rem;
    }
    
    .module-icon {
        font-size: 2rem;
    }
    
    .module-header h3 {
        font-size: 1.1rem;
    }
    
    .module-level {
        font-size: 0.7rem;
        padding: 0.25rem 0.75rem;
    }
    
    .module-content {
        padding: 0.75rem 1rem;
    }
    
    .feature-item {
        padding: 0.4rem;
        margin-bottom: 0.5rem;
    }
    
    .feature-item span {
        font-size: 0.85rem;
    }
    
    .module-actions {
        padding: 0.75rem 1rem 1.25rem;
        gap: 0.5rem;
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-simulation {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }
    
    .simulation-card {
        padding: 1rem;
    }
    
    .simulation-preview {
        width: 50px;
        height: 50px;
        margin-bottom: 0.75rem;
    }
    
    .simulation-icon {
        font-size: 1.25rem;
    }
    
    .simulation-card h4 {
        font-size: 1rem;
    }
    
    .simulation-card p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }
    
    .main-footer {
        padding: 2rem 0.75rem 1rem;
    }
    
    .footer-section h4 {
        font-size: 1.1rem;
    }
    
    .modal-header,
    .modal-body {
        padding: 1rem;
    }
    
    .modal-header h3 {
        font-size: 1.2rem;
    }
    
    .learning-objectives {
        padding: 1rem;
    }
    
    .module-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .stat-box {
        padding: 0.75rem;
    }
    
    .stat-box .stat-value {
        font-size: 1.25rem;
    }
}

/* Extra Small Mobile (up to 320px) */
@media (max-width: 320px) {
    .hero-content h2 {
        font-size: 1.6rem;
    }
    
    .section-header h2 {
        font-size: 1.6rem;
    }
    
    .module-header h3 {
        font-size: 1rem;
    }
    
    .simulation-card h4 {
        font-size: 0.95rem;
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-simulation {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .main-header,
    .main-footer,
    .cta-button,
    .module-actions,
    .btn-primary,
    .btn-secondary,
    .btn-simulation {
        display: none !important;
    }
    
    .hero-section {
        background: none !important;
        color: #333 !important;
    }
    
    .module-card,
    .simulation-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid;
    }
    
    .modules-grid,
    .simulations-grid {
        grid-template-columns: 1fr !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .hero-section {
        background: #000 !important;
        color: #fff !important;
    }
    
    .module-card,
    .simulation-card {
        border: 2px solid #333 !important;
    }
    
    .btn-primary,
    .btn-secondary,
    .btn-simulation {
        border: 2px solid #333 !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .logo-icon {
        animation: none !important;
    }
    
    .module-icon {
        animation: none !important;
    }
    
    .progress-fill::after {
        animation: none !important;
    }
}
