/* ===== INTERACTIVE DIAGRAMS STYLING ===== */

/* Diagram Container */
.diagram-container {
    background: var(--module-bg-primary);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid var(--module-border);
    box-shadow: 0 8px 32px var(--module-shadow-light);
}

.diagram-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid var(--module-border);
    padding-bottom: 1rem;
}

.tab-btn {
    background: transparent;
    border: 2px solid var(--module-border);
    color: var(--module-text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9rem;
}

.tab-btn:hover {
    border-color: var(--module-primary);
    color: var(--module-primary);
    transform: translateY(-2px);
}

.tab-btn.active {
    background: var(--module-gradient-primary);
    border-color: transparent;
    color: white;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.diagram-panel {
    display: none;
    animation: fadeInDiagram 0.5s ease-out;
}

.diagram-panel.active {
    display: block;
}

@keyframes fadeInDiagram {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Interactive SVG Diagram */
.interactive-diagram {
    width: 100%;
    height: auto;
    max-height: 500px;
    background: var(--module-bg-secondary);
    border-radius: 16px;
    border: 2px solid var(--module-border);
    padding: 1rem;
    margin-bottom: 2rem;
}

/* Diagram Components */
.diagram-component {
    cursor: pointer;
    transition: all 0.3s ease;
}

.diagram-component:hover {
    transform: scale(1.05);
}

.component-box {
    fill: var(--module-bg-primary);
    stroke: var(--module-primary);
    stroke-width: 2;
    transition: all 0.3s ease;
}

.diagram-component:hover .component-box {
    fill: rgba(59, 130, 246, 0.1);
    stroke: var(--module-secondary);
    stroke-width: 3;
}

.component-label {
    fill: var(--module-text-primary);
    font-size: 14px;
    font-weight: 600;
    text-anchor: middle;
    dominant-baseline: middle;
}

.component-sublabel {
    fill: var(--module-text-secondary);
    font-size: 10px;
    font-weight: 400;
    text-anchor: middle;
    dominant-baseline: middle;
}

/* Connection Lines */
.connection-line {
    stroke: var(--module-primary);
    stroke-width: 2;
    fill: none;
    animation: flowAnimation 2s ease-in-out infinite;
}

@keyframes flowAnimation {
    0%, 100% {
        stroke-dasharray: 0, 10;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 5, 5;
        stroke-dashoffset: -10;
    }
}

.arrow-fill {
    fill: var(--module-primary);
}

/* Component Categories */
.diagram-component.patient .component-box {
    fill: rgba(16, 185, 129, 0.1);
    stroke: var(--module-success);
}

.diagram-component.sensors .component-box {
    fill: rgba(6, 182, 212, 0.1);
    stroke: var(--module-accent);
}

.diagram-component.processing .component-box {
    fill: rgba(139, 92, 246, 0.1);
    stroke: var(--module-secondary);
}

.diagram-component.display .component-box {
    fill: rgba(245, 158, 11, 0.1);
    stroke: var(--module-warning);
}

/* Component Information Panel */
.component-info {
    background: var(--module-bg-secondary);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid var(--module-border);
    min-height: 200px;
}

.component-info h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--module-text-primary);
    margin-bottom: 1rem;
    background: var(--module-gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.component-info p {
    color: var(--module-text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Circuit Schematic Styles */
.circuit-element {
    cursor: pointer;
    transition: all 0.3s ease;
}

.circuit-element:hover {
    transform: scale(1.1);
}

.resistor {
    fill: none;
    stroke: var(--module-text-primary);
    stroke-width: 2;
}

.capacitor {
    fill: none;
    stroke: var(--module-primary);
    stroke-width: 2;
}

.inductor {
    fill: none;
    stroke: var(--module-secondary);
    stroke-width: 2;
}

.wire {
    stroke: var(--module-text-primary);
    stroke-width: 1.5;
    fill: none;
}

.ground {
    stroke: var(--module-text-primary);
    stroke-width: 2;
    fill: none;
}

.voltage-source {
    fill: var(--module-bg-primary);
    stroke: var(--module-danger);
    stroke-width: 2;
}

/* Signal Flow Diagram */
.signal-path {
    stroke: var(--module-accent);
    stroke-width: 3;
    fill: none;
    stroke-dasharray: 5, 5;
    animation: signalFlow 1.5s linear infinite;
}

@keyframes signalFlow {
    0% {
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dashoffset: -20;
    }
}

.signal-node {
    fill: var(--module-accent);
    stroke: var(--module-bg-primary);
    stroke-width: 2;
    animation: signalPulse 1s ease-in-out infinite;
}

@keyframes signalPulse {
    0%, 100% {
        r: 4;
        opacity: 1;
    }
    50% {
        r: 6;
        opacity: 0.7;
    }
}

/* Lecture Notes Section */
.lecture-notes {
    padding: 4rem 0;
    background: var(--module-bg-secondary);
}

.notes-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.note-card {
    background: var(--module-bg-primary);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid var(--module-border);
    box-shadow: 0 8px 32px var(--module-shadow-light);
    transition: all 0.3s ease;
}

.note-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px var(--module-shadow-medium);
}

.note-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--module-border);
}

.note-icon {
    width: 60px;
    height: 60px;
    background: var(--module-gradient-primary);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.note-icon i {
    font-size: 1.5rem;
    color: white;
}

.note-header h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--module-text-primary);
    margin: 0;
}

.note-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--module-text-primary);
    margin-bottom: 1rem;
    margin-top: 1.5rem;
}

.note-content h4:first-child {
    margin-top: 0;
}

.note-content ul,
.note-content ol {
    color: var(--module-text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
}

.note-content li {
    margin-bottom: 0.5rem;
}

.note-content strong {
    color: var(--module-text-primary);
    font-weight: 600;
}

/* Reference Table */
.reference-table {
    background: var(--module-bg-secondary);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid var(--module-border);
}

.table-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--module-border);
}

.table-row:last-child {
    border-bottom: none;
}

.table-row .parameter {
    font-weight: 600;
    color: var(--module-text-primary);
}

.table-row .range {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--module-primary);
    background: rgba(59, 130, 246, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .diagram-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .tab-btn {
        width: 100%;
        text-align: center;
    }
    
    .monitor-display {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .notes-grid {
        grid-template-columns: 1fr;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
}
