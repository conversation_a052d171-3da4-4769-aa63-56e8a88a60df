/* ===== LECTURE SYSTEM STYLES ===== */

/* Lecture Header */
.lecture-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.lecture-header .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.lecture-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lecture-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lecture-title .lecture-icon {
    font-size: 2rem;
    color: #3498db;
}

.lecture-title h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.lecture-controls {
    display: flex;
    gap: 1rem;
}

.btn-control {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.btn-control:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Lecture Selection */
.lecture-selection {
    padding: 3rem 0;
    background: #f8f9fa;
}

.lectures-grid {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.lecture-category {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.lecture-category h3 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 2px solid #e8ecef;
    padding-bottom: 1rem;
}

.lecture-category h3 i {
    color: #3498db;
    font-size: 1.3rem;
}

.lecture-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.lecture-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #e8ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.lecture-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
}

.lecture-thumbnail {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    position: relative;
}

.lecture-thumbnail i {
    font-size: 2rem;
    color: white;
}

.duration-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.lecture-info h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.75rem;
}

.lecture-info p {
    color: #7f8c8d;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.lecture-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.slides-count {
    color: #7f8c8d;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.difficulty {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.difficulty.beginner {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.difficulty.intermediate {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.difficulty.advanced {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.btn-lecture {
    width: 100%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-lecture:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

/* Lecture Viewer */
.lecture-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #1a1a1a;
    z-index: 2000;
    display: flex;
    flex-direction: column;
}

.lecture-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.lecture-viewer-header {
    background: #2c3e50;
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #34495e;
}

.lecture-info-bar {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.lecture-info-bar h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.lecture-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#slideCounter {
    font-size: 0.9rem;
    color: #bdc3c7;
    min-width: 60px;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.lecture-controls-bar {
    display: flex;
    gap: 1rem;
}

/* Slide Container */
.slide-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a1a1a;
    overflow: hidden;
}

.slide-content {
    width: 90%;
    max-width: 1000px;
    height: 80%;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.slide {
    width: 100%;
    height: 100%;
    padding: 3rem;
    display: none;
    flex-direction: column;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.slide.active {
    display: flex;
}

.slide h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 700;
}

.slide h2 {
    font-size: 2rem;
    color: #34495e;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.slide h3 {
    font-size: 1.5rem;
    color: #3498db;
    margin-bottom: 1rem;
    font-weight: 600;
}

.slide p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.slide ul {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2c3e50;
    margin-left: 2rem;
}

.slide li {
    margin-bottom: 0.75rem;
    position: relative;
}

.slide li::marker {
    color: #3498db;
}

/* Slide Navigation */
.slide-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    pointer-events: none;
}

.nav-btn {
    width: 50px;
    height: 50px;
    background: rgba(52, 152, 219, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    pointer-events: all;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-btn:hover {
    background: #3498db;
    transform: scale(1.1);
}

.nav-btn:disabled {
    background: rgba(127, 140, 141, 0.5);
    cursor: not-allowed;
    transform: none;
}

/* Slide Thumbnails */
.slide-thumbnails {
    background: #2c3e50;
    padding: 1rem 2rem;
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #3498db #34495e;
}

.slide-thumbnails::-webkit-scrollbar {
    height: 6px;
}

.slide-thumbnails::-webkit-scrollbar-track {
    background: #34495e;
}

.slide-thumbnails::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 3px;
}

.thumbnail {
    min-width: 80px;
    height: 60px;
    background: #34495e;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #bdc3c7;
    font-size: 0.8rem;
    font-weight: 600;
}

.thumbnail:hover {
    background: #3498db;
    color: white;
}

.thumbnail.active {
    border-color: #3498db;
    background: #3498db;
    color: white;
}

/* Notes Panel */
.notes-panel {
    position: fixed;
    right: 0;
    top: 0;
    width: 350px;
    height: 100%;
    background: white;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.2);
    z-index: 2001;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notes-panel.open {
    transform: translateX(0);
}

.notes-header {
    background: #34495e;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notes-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.notes-content {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

#notesTextarea {
    flex: 1;
    border: 1px solid #e8ecef;
    border-radius: 8px;
    padding: 1rem;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: none;
    margin-bottom: 1rem;
}

.notes-actions {
    display: flex;
    gap: 0.5rem;
}

.notes-actions .btn-secondary {
    flex: 1;
    padding: 0.75rem;
    font-size: 0.8rem;
}

/* Settings Modal */
.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 0.5rem;
}

.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e8ecef;
    border-radius: 6px;
    font-size: 0.9rem;
}

.setting-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

#speedValue {
    color: #3498db;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lecture-viewer-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .lecture-info-bar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .slide-content {
        width: 95%;
        height: 85%;
    }
    
    .slide {
        padding: 2rem 1.5rem;
    }
    
    .slide h1 {
        font-size: 2rem;
    }
    
    .slide h2 {
        font-size: 1.5rem;
    }
    
    .slide p, .slide ul {
        font-size: 1rem;
    }
    
    .notes-panel {
        width: 100%;
    }
    
    .lecture-cards {
        grid-template-columns: 1fr;
    }
}
