/* ===== MODERN LECTURE SYSTEM STYLES ===== */

/* CSS Custom Properties for Theme System */
:root {
    /* Modern Blue-Purple Color Palette */
    --primary-blue: #3b82f6;
    --primary-purple: #8b5cf6;
    --secondary-blue: #1e40af;
    --secondary-purple: #7c3aed;
    --accent-blue: #60a5fa;
    --accent-purple: #a78bfa;

    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --shadow-heavy: rgba(0, 0, 0, 0.25);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-blue), var(--secondary-purple));
    --gradient-accent: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
    --gradient-bg: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --shadow-heavy: rgba(0, 0, 0, 0.6);
    --gradient-bg: linear-gradient(135deg, #1e293b, #334155);
}

/* Lecture Header */
.lecture-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px var(--shadow-medium);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.lecture-header .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.lecture-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lecture-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lecture-title .lecture-icon {
    font-size: 2rem;
    color: #3498db;
}

.lecture-title h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.lecture-controls {
    display: flex;
    gap: 1rem;
}

.btn-control {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.btn-control:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Lecture Selection */
.lecture-selection {
    padding: 4rem 0;
    background: var(--gradient-bg);
    min-height: 100vh;
}

.lectures-grid {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.lecture-category {
    background: var(--bg-primary);
    border-radius: 24px;
    padding: 3rem;
    box-shadow: 0 8px 32px var(--shadow-light);
    border: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.lecture-category:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px var(--shadow-medium);
}

.lecture-category h3 {
    font-size: 1.8rem;
    color: var(--text-primary);
    margin-bottom: 2.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 2px solid transparent;
    background: var(--gradient-accent);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-bottom: 1rem;
    position: relative;
}

.lecture-category h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.lecture-category h3 i {
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 1.5rem;
}

.lecture-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2rem;
}

.lecture-card {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.lecture-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.lecture-card:hover::before {
    transform: scaleX(1);
}

.lecture-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px var(--shadow-medium);
    border-color: var(--primary-blue);
}

.lecture-thumbnail {
    width: 90px;
    height: 90px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    position: relative;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.lecture-thumbnail::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 20px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
    pointer-events: none;
}

.lecture-thumbnail i {
    font-size: 2.2rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.duration-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--gradient-secondary);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
    border: 2px solid var(--bg-primary);
}

.lecture-info h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.lecture-info p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.lecture-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.slides-count {
    color: var(--text-secondary);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.slides-count i {
    color: var(--primary-blue);
}

.difficulty {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.difficulty::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 20px;
    opacity: 0.1;
    z-index: -1;
}

.difficulty.beginner {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
    color: #059669;
    border-color: rgba(34, 197, 94, 0.2);
}

.difficulty.intermediate {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.1));
    color: #d97706;
    border-color: rgba(245, 158, 11, 0.2);
}

.difficulty.advanced {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    color: #dc2626;
    border-color: rgba(239, 68, 68, 0.2);
}

.btn-lecture {
    width: 100%;
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.2rem 1.5rem;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.btn-lecture::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.btn-lecture:hover::before {
    transform: translateX(100%);
}

.btn-lecture:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

.btn-lecture:active {
    transform: translateY(-1px) scale(0.98);
}

/* Lecture Viewer */
.lecture-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f172a, #1e293b);
    z-index: 2000;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.lecture-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.lecture-viewer-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.lecture-info-bar {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.lecture-info-bar h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.lecture-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#slideCounter {
    font-size: 0.9rem;
    color: #bdc3c7;
    min-width: 60px;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.lecture-controls-bar {
    display: flex;
    gap: 1rem;
}

/* Slide Container */
.slide-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0f172a, #1e293b);
    overflow: hidden;
    padding: 2rem;
}

.slide-content {
    width: 95%;
    max-width: 1200px;
    height: 85%;
    background: var(--bg-primary);
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.slide-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
    z-index: 1;
}

.slide {
    width: 100%;
    height: 100%;
    padding: 4rem;
    display: none;
    flex-direction: column;
    justify-content: center;
    position: relative;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.slide.active {
    display: flex;
}

.slide h1 {
    font-size: 3.5rem;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 2.5rem;
    text-align: center;
    font-weight: 900;
    line-height: 1.1;
    text-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
    letter-spacing: -0.02em;
}

.slide h2 {
    font-size: 2.8rem;
    color: var(--text-primary);
    margin-bottom: 2.5rem;
    font-weight: 800;
    position: relative;
    line-height: 1.2;
    letter-spacing: -0.01em;
}

.slide h2::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 0;
    width: 80px;
    height: 5px;
    background: var(--gradient-accent);
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.slide h3 {
    font-size: 2.2rem;
    background: var(--gradient-accent);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 2rem;
    font-weight: 700;
    line-height: 1.3;
    letter-spacing: -0.01em;
}

.slide p {
    font-size: 1.4rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.slide ul {
    font-size: 1.3rem;
    line-height: 1.9;
    color: var(--text-secondary);
    margin-left: 0;
    list-style: none;
    margin-bottom: 2rem;
}

.slide li {
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 2.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
    line-height: 1.8;
}

.slide li:hover {
    color: var(--text-primary);
    transform: translateX(4px);
}

.slide li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--primary-blue);
    font-size: 1rem;
    top: 0.3rem;
    font-weight: 700;
    filter: drop-shadow(0 1px 3px rgba(59, 130, 246, 0.3));
}

/* Modern Slide Layouts */
.modern-slide-title-content {
    position: relative;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.title-background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    z-index: -1;
}

.modern-title {
    font-size: 4.5rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 3rem;
    line-height: 1.05;
    text-shadow: 0 6px 30px rgba(59, 130, 246, 0.4);
    letter-spacing: -0.03em;
}

.modern-subtitle {
    font-size: 2.4rem;
    font-weight: 700;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    opacity: 0.95;
    line-height: 1.3;
    letter-spacing: -0.01em;
}

.modern-description {
    font-size: 1.5rem;
    color: var(--text-muted);
    max-width: 700px;
    line-height: 1.7;
    margin-bottom: 4rem;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.title-decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
}

.floating-element {
    position: absolute;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: var(--gradient-accent);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-element.element-1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element.element-2 {
    top: 20%;
    right: 15%;
    animation-delay: 2s;
}

.floating-element.element-3 {
    bottom: 15%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-20px) scale(1.1);
    }
}

.modern-content-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    height: 100%;
    padding: 2rem 0;
}

.modern-heading {
    font-size: 3.2rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 3rem;
    position: relative;
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.modern-heading::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 100px;
    height: 6px;
    background: var(--gradient-accent);
    border-radius: 3px;
    box-shadow: 0 3px 12px rgba(139, 92, 246, 0.4);
}

.content-body {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.modern-paragraph {
    font-size: 1.6rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.modern-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-list-item {
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 16px;
    border-left: 5px solid var(--primary-blue);
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.4rem;
    line-height: 1.7;
    color: var(--text-primary);
    font-weight: 500;
    letter-spacing: 0.01em;
}

.modern-list-item:hover {
    transform: translateX(12px) scale(1.02);
    box-shadow: 0 12px 35px var(--shadow-light);
    border-left-color: var(--primary-purple);
    background: rgba(59, 130, 246, 0.05);
}

.modern-list-item::before {
    content: '✓';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 800;
    font-size: 1rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    border: 3px solid var(--bg-primary);
}

.modern-highlight {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15));
    border: 3px solid rgba(59, 130, 246, 0.3);
    border-radius: 20px;
    padding: 3rem;
    margin: 3rem 0;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
    position: relative;
    overflow: hidden;
}

.modern-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    z-index: 1;
}

.highlight-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
    filter: drop-shadow(0 3px 12px rgba(59, 130, 246, 0.4));
}

.modern-highlight p {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.6;
    letter-spacing: 0.01em;
}

/* Modern Visual Containers */
.modern-visual-container {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 3rem;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px var(--shadow-light);
    text-align: center;
}

.visual-caption {
    font-size: 1rem;
    color: var(--text-muted);
    margin-top: 2rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Heart Monitor Visualization */
.heartbeat-visualization {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.heart-monitor {
    background: linear-gradient(135deg, #0f172a, #1e293b);
    border-radius: 16px;
    padding: 2rem;
    border: 2px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
}

.monitor-screen {
    background: #001122;
    border-radius: 8px;
    padding: 1.5rem;
    position: relative;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ecg-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(59, 130, 246, 0.2) 1px, transparent 1px),
        linear-gradient(90deg, rgba(59, 130, 246, 0.2) 1px, transparent 1px);
    background-size: 20px 20px;
    border-radius: 8px;
}

.modern-heartbeat-icon {
    font-size: 3rem;
    color: var(--primary-blue);
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.8));
}

.vital-display {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--bg-tertiary);
    padding: 1rem 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.vital-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-blue);
}

.vital-unit {
    font-size: 1rem;
    color: var(--text-muted);
    font-weight: 500;
}

.vital-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vital-status.normal {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* ECG Visualization */
.ecg-visualization {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.ecg-monitor {
    background: linear-gradient(135deg, #0f172a, #1e293b);
    border-radius: 16px;
    padding: 2rem;
    border: 2px solid rgba(59, 130, 246, 0.3);
}

.modern-ecg {
    background: #001122;
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.ecg-controls {
    display: flex;
    justify-content: space-around;
    gap: 2rem;
}

.control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    font-weight: 500;
}

.led-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.led-indicator.active {
    background: var(--primary-blue);
    box-shadow: 0 0 12px rgba(59, 130, 246, 0.8);
}

/* Blood Pressure Visualization */
.bp-visualization {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.bp-monitor {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.modern-gauge {
    width: 200px;
    height: 200px;
    position: relative;
}

.gauge-face {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border: 8px solid var(--border-color);
    position: relative;
    box-shadow: inset 0 0 30px var(--shadow-light);
}

.gauge-markings {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        var(--primary-blue) 0deg 120deg,
        #f59e0b 120deg 240deg,
        #ef4444 240deg 360deg
    );
    opacity: 0.3;
}

.gauge-needle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 4px;
    height: 80px;
    background: var(--gradient-primary);
    transform-origin: bottom center;
    transform: translate(-50%, -100%) rotate(45deg);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.gauge-center {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
}

.bp-readings {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.reading-display {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    font-family: 'Courier New', monospace;
}

.systolic, .diastolic {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-blue);
}

.separator {
    font-size: 2rem;
    color: var(--text-muted);
}

.unit {
    font-size: 1rem;
    color: var(--text-muted);
    font-weight: 500;
}

.bp-status {
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bp-status.normal {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

/* Modern Quiz Elements */
.modern-quiz-container {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 3rem;
    margin: 2rem 0;
    border: 2px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 8px 32px var(--shadow-light);
}

.quiz-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.quiz-icon {
    font-size: 3rem;
    filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.3));
}

.quiz-question {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.5;
    letter-spacing: 0.01em;
}

.modern-quiz-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.modern-quiz-option {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--bg-tertiary);
    border: 3px solid var(--border-color);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: left;
    font-size: 1.3rem;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.6;
}

.modern-quiz-option:hover {
    border-color: var(--primary-blue);
    background: rgba(59, 130, 246, 0.05);
    transform: translateX(8px);
}

.option-letter {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.4rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.option-text {
    flex: 1;
    font-weight: 500;
    line-height: 1.6;
    font-size: 1.3rem;
}

.modern-quiz-option.correct {
    border-color: #059669;
    background: rgba(34, 197, 94, 0.1);
}

.modern-quiz-option.correct .option-letter {
    background: linear-gradient(135deg, #059669, #34d399);
}

.modern-quiz-option.incorrect {
    border-color: #dc2626;
    background: rgba(239, 68, 68, 0.1);
}

.modern-quiz-option.incorrect .option-letter {
    background: linear-gradient(135deg, #dc2626, #f87171);
}

.modern-quiz-feedback {
    padding: 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.modern-quiz-feedback.correct {
    background: rgba(34, 197, 94, 0.1);
    color: #059669;
    border: 2px solid rgba(34, 197, 94, 0.2);
}

.modern-quiz-feedback.incorrect {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 2px solid rgba(239, 68, 68, 0.2);
}

/* Modern Simulation Elements */
.modern-simulation-container {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 3rem;
    margin: 2rem 0;
    border: 2px solid rgba(139, 92, 246, 0.2);
    box-shadow: 0 8px 32px var(--shadow-light);
    text-align: center;
}

.simulation-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.simulation-icon {
    font-size: 3rem;
    filter: drop-shadow(0 2px 8px rgba(139, 92, 246, 0.3));
}

.simulation-header h4 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.simulation-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.modern-simulation-btn {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    padding: 1.2rem 2rem;
    border-radius: 16px;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
}

.modern-simulation-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 32px rgba(139, 92, 246, 0.4);
}

/* Summary Slide Styling */
.summary-heading {
    font-size: 1.6rem;
    font-weight: 700;
    background: var(--gradient-accent);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1.5rem;
    text-align: center;
}

.modern-summary-points {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 2.5rem;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px var(--shadow-light);
}

.modern-next-steps {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05));
    border-radius: 20px;
    padding: 2.5rem;
    margin: 2rem 0;
    border: 2px solid rgba(59, 130, 246, 0.1);
    text-align: center;
}

/* Interactive Placeholder */
.modern-interactive-placeholder {
    background: var(--bg-tertiary);
    border: 2px dashed var(--border-color);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 1.1rem;
    margin: 2rem 0;
}

.modern-visual-placeholder {
    background: var(--bg-tertiary);
    border: 2px dashed var(--border-color);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    color: var(--text-muted);
    margin: 2rem 0;
}

.placeholder-icon {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Dark Mode Specific Adjustments */
[data-theme="dark"] .modern-quiz-option:hover {
    background: rgba(59, 130, 246, 0.1);
}

[data-theme="dark"] .modern-simulation-container {
    border-color: rgba(139, 92, 246, 0.3);
}

[data-theme="dark"] .modern-quiz-container {
    border-color: rgba(59, 130, 246, 0.3);
}

/* Slide Navigation */
.slide-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    pointer-events: none;
}

.nav-btn {
    width: 50px;
    height: 50px;
    background: rgba(52, 152, 219, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    pointer-events: all;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-btn:hover {
    background: #3498db;
    transform: scale(1.1);
}

.nav-btn:disabled {
    background: rgba(127, 140, 141, 0.5);
    cursor: not-allowed;
    transform: none;
}

/* Slide Thumbnails */
.slide-thumbnails {
    background: #2c3e50;
    padding: 1rem 2rem;
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #3498db #34495e;
}

.slide-thumbnails::-webkit-scrollbar {
    height: 6px;
}

.slide-thumbnails::-webkit-scrollbar-track {
    background: #34495e;
}

.slide-thumbnails::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 3px;
}

.thumbnail {
    min-width: 80px;
    height: 60px;
    background: #34495e;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #bdc3c7;
    font-size: 0.8rem;
    font-weight: 600;
}

.thumbnail:hover {
    background: #3498db;
    color: white;
}

.thumbnail.active {
    border-color: #3498db;
    background: #3498db;
    color: white;
}

/* Notes Panel */
.notes-panel {
    position: fixed;
    right: 0;
    top: 0;
    width: 350px;
    height: 100%;
    background: white;
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.2);
    z-index: 2001;
    display: flex;
    flex-direction: column;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notes-panel.open {
    transform: translateX(0);
}

.notes-header {
    background: #34495e;
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notes-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.notes-content {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

#notesTextarea {
    flex: 1;
    border: 1px solid #e8ecef;
    border-radius: 8px;
    padding: 1rem;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: none;
    margin-bottom: 1rem;
}

.notes-actions {
    display: flex;
    gap: 0.5rem;
}

.notes-actions .btn-secondary {
    flex: 1;
    padding: 0.75rem;
    font-size: 0.8rem;
}

/* Settings Modal */
.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 0.5rem;
}

.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e8ecef;
    border-radius: 6px;
    font-size: 0.9rem;
}

.setting-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

#speedValue {
    color: #3498db;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lecture-viewer-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .lecture-info-bar {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .slide-content {
        width: 95%;
        height: 85%;
    }
    
    .slide {
        padding: 2rem 1.5rem;
    }
    
    .slide h1 {
        font-size: 2rem;
    }
    
    .slide h2 {
        font-size: 1.5rem;
    }
    
    .slide p, .slide ul {
        font-size: 1rem;
    }
    
    .notes-panel {
        width: 100%;
    }
    
    .lecture-cards {
        grid-template-columns: 1fr;
    }
}
