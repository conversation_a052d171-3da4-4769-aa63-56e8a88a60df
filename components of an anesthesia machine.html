<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine SPDD Modeler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            line-height: 1.6;
            overscroll-behavior: none; /* Prevent pull-to-refresh on mobile when dragging */
        }
        .app-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .component-library {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f0f8ff; /* Light AliceBlue */
        }
        .component-item {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #007bff;
            background-color: #e7f3ff;
            color: #004085;
            cursor: grab;
            user-select: none;
            text-align: center;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .panels-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative; /* For SVG positioning */
        }
        .panel {
            border: 2px dashed #ccc;
            padding: 10px;
            margin: 0 5px;
            flex: 1;
            min-height: 250px;
            display: flex;
            flex-direction: column;
            background-color: #fff;
            border-radius: 5px;
        }
        .panel h2 {
            margin-top: 0;
            margin-bottom: 10px;
            text-align: center;
            font-size: 1.1em;
            color: #333;
        }
        .drop-zone {
            width: 100%;
            flex-grow: 1;
            background-color: #fdfdfd;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 5px;
            display: flex; 
            flex-direction: column; 
            align-items: center; 
            overflow-y: auto; /* In case of many items */
        }
        .dropped-item {
            padding: 8px 10px;
            margin: 5px;
            border: 1px solid #28a745;
            background-color: #d4edda;
            color: #155724;
            cursor: default; 
            position: relative; 
            z-index: 1; /* Above SVG lines */
            border-radius: 4px;
            text-align: center;
            font-size: 0.9em;
            width: calc(100% - 10px); /* Make items take most of drop-zone width, accounting for padding */
            box-sizing: border-box;
            user-select: none; /* Prevent text selection when trying to drag for connection */
        }
        #checkButton {
            display: block;
            margin: 20px auto;
            padding: 12px 25px;
            font-size: 1em;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        #checkButton:hover {
            background-color: #0056b3;
        }
        #messageArea {
            margin-top: 15px;
            text-align: center;
            font-weight: bold;
            font-size: 1.1em;
            min-height: 1.5em; /* Prevent layout shift */
        }
        .correct { color: green; }
        .incorrect { color: red; }

        #connectionsCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none; 
            z-index: 0; /* Behind items */
        }

        /* Responsive adjustments */
        @media (max-width: 860px) { /* Increased breakpoint for better panel layout */
            .panels-container {
                flex-direction: column;
            }
            .panel {
                margin: 10px 0;
                min-height: 200px;
            }
        }
        @media (max-width: 480px) {
            .component-library .component-item {
                font-size: 0.8em;
                padding: 6px 10px;
            }
            .dropped-item {
                font-size: 0.8em;
                padding: 6px 8px;
            }
            .panel h2 {
                font-size: 1em;
            }
             .component-library {
                justify-content: space-around;
            }
        }
    </style>
</head>
<body>

<div class="app-container">
    <div class="component-library" id="componentLibrary">
        <!-- Components will be added here by JS -->
    </div>
    <div class="panels-container" id="panelsContainer">
        <div id="supplyPanel" class="panel" data-panel-name="Supply">
            <h2>Supply</h2>
            <div class="drop-zone"></div>
        </div>
        <div id="processingPanel" class="panel" data-panel-name="Processing">
            <h2>Processing</h2>
            <div class="drop-zone"></div>
        </div>
        <div id="deliveryPanel" class="panel" data-panel-name="Delivery">
            <h2>Delivery</h2>
            <div class="drop-zone"></div>
        </div>
        <div id="disposalPanel" class="panel" data-panel-name="Disposal">
            <h2>Disposal</h2>
            <div class="drop-zone"></div>
        </div>
        <svg id="connectionsCanvas"></svg>
    </div>
    <button id="checkButton">Check</button>
    <div id="messageArea"></div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const COMPONENT_NAMES = [
            "Gas Cylinder", "Pipeline", "Flow Meter", "Vaporizer",
            "Breathing System", "Scavenging System", "Patient"
        ];

        const CORRECT_PANEL_CONTENTS = {
            Supply: ["Gas Cylinder", "Pipeline"].sort(),
            Processing: ["Flow Meter", "Vaporizer"].sort(),
            Delivery: ["Breathing System", "Patient"].sort(),
            Disposal: ["Scavenging System"].sort()
        };

        const componentLibrary = document.getElementById('componentLibrary');
        const panels = document.querySelectorAll('.panel');
        const panelsContainer = document.getElementById('panelsContainer');
        const connectionsCanvas = document.getElementById('connectionsCanvas');
        const checkButton = document.getElementById('checkButton');
        const messageArea = document.getElementById('messageArea');

        let draggedElementData = null; 
        let connections = []; 
        let itemIdCounter = 0;

        let isConnecting = false;
        let connectionStartItem = null;
        let tempLine = null;
        let touchStartCoords = { x: 0, y: 0 }; // For touch handling

        function generateItemId(componentName) {
            return `${componentName.toLowerCase().replace(/\s+/g, '-')}-${itemIdCounter++}`;
        }

        COMPONENT_NAMES.forEach(name => {
            const item = document.createElement('div');
            item.classList.add('component-item');
            item.textContent = name;
            item.draggable = true;
            item.dataset.componentName = name;
            item.addEventListener('dragstart', handleDragStartLibrary);
            item.addEventListener('touchstart', (e) => { // Basic touch support for library items
                item.style.opacity = '0.5'; // Visual feedback for touch
                 draggedElementData = {
                    name: e.target.dataset.componentName,
                    isLibraryItem: true,
                    element: e.target
                };
            }, {passive: true});
            item.addEventListener('touchend', (e) => {
                 if (draggedElementData && draggedElementData.element === e.target) {
                    // This is a bit tricky, touchend on the item itself means no drop occurred on a panel
                    // We'd need to check if touchend is over a panel.
                    // For simplicity, this example focuses on mouse drag/drop primarily.
                    // Proper touch drag-and-drop requires more involved logic.
                    e.target.style.opacity = '1';
                 }
            });
            componentLibrary.appendChild(item);
        });

        function handleDragStartLibrary(event) {
            draggedElementData = {
                name: event.target.dataset.componentName,
                isLibraryItem: true
            };
            event.dataTransfer.setData('text/plain', event.target.dataset.componentName);
            event.dataTransfer.effectAllowed = 'copy';
            event.target.style.opacity = '0.5'; 
        }
        
        document.addEventListener('dragend', (event) => { // Reset opacity if drag source was a library item
            if (event.target.classList.contains('component-item')) {
                event.target.style.opacity = '1';
            }
        });


        panels.forEach(panel => {
            const dropZone = panel.querySelector('.drop-zone');
            
            [dropZone, panel].forEach(el => {
                el.addEventListener('dragover', handleDragOver);
                el.addEventListener('drop', (event) => handleDrop(event, dropZone));

                // Basic touch drop handling
                el.addEventListener('touchmove', handleDragOver, {passive: false}); // preventDefault needed
                el.addEventListener('touchend', (event) => {
                    if (draggedElementData && draggedElementData.isLibraryItem) {
                        // Find the dropZone under the touch point
                        const touch = event.changedTouches[0];
                        const targetElement = document.elementFromPoint(touch.clientX, touch.clientY);
                        const currentDropZone = targetElement ? targetElement.closest('.drop-zone') : null;
                        
                        if (currentDropZone && currentDropZone === dropZone) { // Ensure it's the correct drop zone
                           handleDrop(event, currentDropZone, true); // Pass touch=true
                        }
                        if (draggedElementData && draggedElementData.element) {
                             draggedElementData.element.style.opacity = '1';
                        }
                        draggedElementData = null; // Reset
                    }
                });
            });
        });

        function handleDragOver(event) {
            event.preventDefault(); // Necessary to allow dropping
            if (event.dataTransfer) { // Mouse events
                 event.dataTransfer.dropEffect = 'copy';
            }
        }

        function handleDrop(event, dropZone, isTouchEvent = false) {
            event.preventDefault();
            if (draggedElementData && draggedElementData.isLibraryItem) {
                const componentName = draggedElementData.name;
                const newItem = document.createElement('div');
                newItem.classList.add('dropped-item');
                newItem.id = generateItemId(componentName);
                newItem.dataset.componentName = componentName;
                newItem.textContent = componentName;
                
                newItem.addEventListener('mousedown', handleInteractionStartDroppedItem);
                newItem.addEventListener('touchstart', handleInteractionStartDroppedItem, {passive: false});
                
                dropZone.appendChild(newItem);
                if (draggedElementData.element) draggedElementData.element.style.opacity = '1'; // Reset opacity of library item if it was a touch drag
            }
            draggedElementData = null;
        }


        function handleInteractionStartDroppedItem(event) {
            event.preventDefault(); // Important for touch to prevent scrolling, etc.
            isConnecting = true;
            connectionStartItem = event.target.closest('.dropped-item');
            if (!connectionStartItem) return;
            connectionStartItem.style.cursor = 'crosshair';

            const svgRect = connectionsCanvas.getBoundingClientRect();
            const startCoords = getCenterCoordinates(connectionStartItem, svgRect);

            let clientX, clientY;
            if (event.type === 'touchstart') {
                clientX = event.touches[0].clientX;
                clientY = event.touches[0].clientY;
            } else {
                clientX = event.clientX;
                clientY = event.clientY;
            }
            touchStartCoords = { x: clientX, y: clientY }; // Store initial touch/mouse point

            tempLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            tempLine.setAttribute('x1', startCoords.x);
            tempLine.setAttribute('y1', startCoords.y);
            tempLine.setAttribute('x2', startCoords.x); 
            tempLine.setAttribute('y2', startCoords.y);
            tempLine.setAttribute('stroke', '#555');
            tempLine.setAttribute('stroke-width', '2');
            tempLine.setAttribute('stroke-dasharray', '5,5');
            connectionsCanvas.appendChild(tempLine);

            document.addEventListener('mousemove', handleConnectingInteractionMove);
            document.addEventListener('mouseup', handleConnectingInteractionEnd);
            document.addEventListener('touchmove', handleConnectingInteractionMove, {passive: false});
            document.addEventListener('touchend', handleConnectingInteractionEnd);
        }
        
        function handleConnectingInteractionMove(event) {
            if (!isConnecting || !tempLine) return;
            event.preventDefault(); 

            const svgRect = connectionsCanvas.getBoundingClientRect();
            let clientX, clientY;
            if (event.type === 'touchmove') {
                clientX = event.touches[0].clientX;
                clientY = event.touches[0].clientY;
            } else {
                clientX = event.clientX;
                clientY = event.clientY;
            }
            
            const mouseX = clientX - svgRect.left;
            const mouseY = clientY - svgRect.top;

            tempLine.setAttribute('x2', mouseX);
            tempLine.setAttribute('y2', mouseY);
        }

        function handleConnectingInteractionEnd(event) {
            if (!isConnecting) return;
            
            // Determine the element under the mouse/touch end point
            let clientX, clientY;
            if (event.type === 'touchend') {
                clientX = event.changedTouches[0].clientX;
                clientY = event.changedTouches[0].clientY;
            } else {
                clientX = event.clientX;
                clientY = event.clientY;
            }

            // Temporarily hide tempLine to correctly identify element underneath
            if (tempLine) tempLine.style.display = 'none';
            const endElement = document.elementFromPoint(clientX, clientY);
            if (tempLine) tempLine.style.display = ''; // Show it back
            
            const endItem = endElement ? endElement.closest('.dropped-item') : null;

            if (connectionStartItem) connectionStartItem.style.cursor = 'default';

            if (tempLine) {
                connectionsCanvas.removeChild(tempLine);
                tempLine = null;
            }

            if (endItem && connectionStartItem && endItem.id !== connectionStartItem.id) {
                const exists = connections.some(conn =>
                    (conn.fromId === connectionStartItem.id && conn.toId === endItem.id) ||
                    (conn.fromId === endItem.id && conn.toId === connectionStartItem.id)
                );
                if (!exists) {
                    addConnection(connectionStartItem, endItem);
                }
            }

            isConnecting = false;
            connectionStartItem = null;
            document.removeEventListener('mousemove', handleConnectingInteractionMove);
            document.removeEventListener('mouseup', handleConnectingInteractionEnd);
            document.removeEventListener('touchmove', handleConnectingInteractionMove);
            document.removeEventListener('touchend', handleConnectingInteractionEnd);
        }
        
        function getCenterCoordinates(element, svgRect) {
            const rect = element.getBoundingClientRect();
            return {
                x: rect.left + rect.width / 2 - svgRect.left,
                y: rect.top + rect.height / 2 - svgRect.top
            };
        }

        function addConnection(itemA, itemB) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            redrawAllConnections(); // Redraw all to ensure this new one is also positioned correctly
                                    // More efficient would be to just draw the new one, but this handles resizes too.
                                    // For simplicity now, just redraw.
            // The actual addition of the new connection and drawing:
            const svgRect = connectionsCanvas.getBoundingClientRect();
            const startCoords = getCenterCoordinates(itemA, svgRect);
            const endCoords = getCenterCoordinates(itemB, svgRect);

            line.setAttribute('x1', startCoords.x);
            line.setAttribute('y1', startCoords.y);
            line.setAttribute('x2', endCoords.x);
            line.setAttribute('y2', endCoords.y);
            line.setAttribute('stroke', 'black');
            line.setAttribute('stroke-width', '2');
            line.dataset.from = itemA.id; 
            line.dataset.to = itemB.id;
            // connectionsCanvas.appendChild(line); // This will be done by redrawAllConnections

            const newConnection = { fromId: itemA.id, toId: itemB.id, lineElement: line };
            // Avoid duplicates if redrawAllConnections doesn't clear connections array
            const existingIndex = connections.findIndex(c => c.fromId === newConnection.fromId && c.toId === newConnection.toId);
            if (existingIndex === -1) {
                 connections.push(newConnection);
            }
            redrawAllConnections(); // This will draw the new line along with others
        }
        
        function redrawAllConnections() {
            while (connectionsCanvas.firstChild) {
                connectionsCanvas.removeChild(connectionsCanvas.firstChild);
            }
            
            const svgRect = connectionsCanvas.getBoundingClientRect();

            connections.forEach(conn => {
                const itemA = document.getElementById(conn.fromId);
                const itemB = document.getElementById(conn.toId);

                if (itemA && itemB) {
                    const startCoords = getCenterCoordinates(itemA, svgRect);
                    const endCoords = getCenterCoordinates(itemB, svgRect);
                    
                    // Re-create or update lineElement. For simplicity, re-create.
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', startCoords.x);
                    line.setAttribute('y1', startCoords.y);
                    line.setAttribute('x2', endCoords.x);
                    line.setAttribute('y2', endCoords.y);
                    line.setAttribute('stroke', 'black');
                    line.setAttribute('stroke-width', '2');
                    connectionsCanvas.appendChild(line);
                    conn.lineElement = line; // Update reference
                } else {
                    // If an item was removed, remove the connection
                    // This part is not implemented (item removal)
                }
            });
        }
        
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(redrawAllConnections, 100); // Shorter delay
        });

        checkButton.addEventListener('click', () => {
            messageArea.textContent = ""; // Clear previous message
            redrawAllConnections(); // Ensure lines are current before checking

            // 1. Check Placement
            let placementCorrect = true;
            const panelContents = {};
            panels.forEach(panel => {
                const panelName = panel.dataset.panelName;
                const itemsInPanel = Array.from(panel.querySelectorAll('.dropped-item'))
                                        .map(item => item.dataset.componentName)
                                        .sort();
                panelContents[panelName] = itemsInPanel;
            });

            for (const panelName in CORRECT_PANEL_CONTENTS) {
                const expected = CORRECT_PANEL_CONTENTS[panelName];
                const actual = panelContents[panelName] || [];
                if (expected.length !== actual.length || !expected.every((val, idx) => val === actual[idx])) {
                    placementCorrect = false;
                    break;
                }
            }
            // Check if all defined panels in CORRECT_PANEL_CONTENTS are covered
            // And if any panel has items that is not in CORRECT_PANEL_CONTENTS (e.g. an extra panel)
            // This is implicitly handled by iterating over CORRECT_PANEL_CONTENTS keys.

            // 2. Check Connections
            let connectionsLogicCorrect = false; 
            if (placementCorrect) { 
                let hasSupplyToProcessing = false;
                let hasProcessingToDelivery = false;
                let hasDeliveryToDisposal = false; 
                let hasInvalidPanelOrderConnection = false;
                let hasPatientToDisposalConnection = false;

                connections.forEach(conn => {
                    const fromItem = document.getElementById(conn.fromId);
                    const toItem = document.getElementById(conn.toId);

                    if (!fromItem || !toItem) return; 

                    const fromPanel = fromItem.closest('.panel').dataset.panelName;
                    const toPanel = toItem.closest('.panel').dataset.panelName;
                    const fromCompName = fromItem.dataset.componentName;
                    
                    if (fromPanel === "Supply" && toPanel === "Processing") {
                        hasSupplyToProcessing = true;
                    } else if (fromPanel === "Processing" && toPanel === "Delivery") {
                        hasProcessingToDelivery = true;
                    } else if (fromPanel === "Delivery" && toPanel === "Disposal") {
                        if (fromCompName === "Patient") {
                            hasPatientToDisposalConnection = true;
                        } else { // Any other component from Delivery to Disposal is fine for this flag
                            hasDeliveryToDisposal = true;
                        }
                    } else if (fromPanel !== toPanel) { 
                        hasInvalidPanelOrderConnection = true;
                    }
                });
                
                connectionsLogicCorrect = hasSupplyToProcessing &&
                                     hasProcessingToDelivery &&
                                     hasDeliveryToDisposal && // Must have this specific D->Di link
                                     !hasInvalidPanelOrderConnection &&
                                     !hasPatientToDisposalConnection;
            }

            if (placementCorrect && connectionsLogicCorrect) {
                messageArea.textContent = "Correct!";
                messageArea.className = 'correct';
            } else {
                let errorMsg = "Not quite! ";
                if (!placementCorrect) {
                    errorMsg += "Check component placement in panels.";
                } else if (!connectionsLogicCorrect) { // Placement was OK, so error is in connections
                    errorMsg += "Check connections based on SPDD model (Supply -> Processing -> Delivery -> Disposal). Patient should not connect to Disposal.";
                }
                errorMsg += " Try again.";
                messageArea.textContent = errorMsg;
                messageArea.className = 'incorrect';
            }
        });
    });
</script>

</body>
</html>
