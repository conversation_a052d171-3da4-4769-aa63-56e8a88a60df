<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Monitor Simulator</title>
    <style>
        :root {
            --primary-bg: #f0f4f8;
            --monitor-bg: #2c3e50;
            --vital-text-color: #ecf0f1;
            --vital-label-color: #bdc3c7;
            --button-bg: #3498db;
            --button-hover-bg: #2980b9;
            --button-text-color: #ffffff;
            --status-stable-color: #2ecc71;
            --status-alert-color: #e74c3c;
            --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            background-color: var(--primary-bg);
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        .app-container {
            background-color: #ffffff;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            padding: 20px;
            width: 100%;
            max-width: 700px;
        }

        header h1 {
            text-align: center;
            color: var(--monitor-bg);
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .monitor-panel {
            background-color: var(--monitor-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
        }

        .monitor-panel h2 {
            color: var(--vital-text-color);
            text-align: center;
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.2em;
            border-bottom: 1px solid var(--vital-label-color);
            padding-bottom: 10px;
        }

        .vitals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .vital-card {
            background-color: #34495e; /* Slightly lighter than monitor-bg */
            padding: 15px;
            border-radius: var(--border-radius);
            text-align: center;
            border: 2px solid transparent; /* For alarm border */
            transition: border-color 0.3s ease;
        }

        .vital-label {
            font-size: 0.9em;
            color: var(--vital-label-color);
            margin-bottom: 5px;
            display: block;
        }

        .vital-value {
            font-size: 1.8em;
            font-weight: bold;
            color: var(--vital-text-color);
        }
        
        .vital-value .unit {
            font-size: 0.6em;
            font-weight: normal;
            margin-left: 4px;
            color: var(--vital-label-color);
        }

        @keyframes flash-alarm {
            0%, 100% { border-color: var(--status-alert-color); box-shadow: 0 0 8px var(--status-alert-color); }
            50% { border-color: transparent; box-shadow: none; }
        }

        .out-of-range {
            animation: flash-alarm 1s infinite;
        }

        .status-panel {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: var(--border-radius);
        }
        
        .status-panel h2 {
            margin-top: 0;
            font-size: 1.2em;
            color: var(--monitor-bg);
        }

        #status-message {
            font-size: 1.3em;
            font-weight: bold;
            color: var(--status-stable-color);
        }

        .controls-panel, .correction-panel {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .controls-panel h2 {
             margin-top: 0;
            font-size: 1.2em;
            color: var(--monitor-bg);
            margin-bottom: 15px;
        }

        .intervention-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .intervention-btn, #btn-corrective-action {
            background-color: var(--button-bg);
            color: var(--button-text-color);
            border: none;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.3s ease;
            min-width: 150px; /* Ensure buttons have a decent width */
        }

        .intervention-btn:hover, #btn-corrective-action:hover {
            background-color: var(--button-hover-bg);
        }
        
        .intervention-btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        #btn-corrective-action {
            background-color: var(--status-alert-color); /* Make corrective action button distinct */
            display: none; /* Initially hidden */
        }
        #btn-corrective-action:hover {
            background-color: #c0392b;
        }


        footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.8em;
            color: #7f8c8d;
        }
        
        /* Responsive adjustments */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .app-container {
                padding: 15px;
            }
            header h1 {
                font-size: 1.5em;
            }
            .vital-value {
                font-size: 1.5em;
            }
            .intervention-buttons {
                flex-direction: column;
                align-items: center;
            }
            .intervention-btn, #btn-corrective-action {
                width: 100%;
                max-width: 300px; /* Limit width on small screens for stacked buttons */
            }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1>Anesthesia Monitor Simulator</h1>
        </header>
        <main>
            <div class="monitor-panel">
                <h2>Patient Vitals</h2>
                <div class="vitals-grid">
                    <div class="vital-card" id="hr-card">
                        <div class="vital-label">Heart Rate</div>
                        <div class="vital-value"><span id="hr-value">0</span><span class="unit">bpm</span></div>
                    </div>
                    <div class="vital-card" id="spo2-card">
                        <div class="vital-label">SpO2</div>
                        <div class="vital-value"><span id="spo2-value">0</span><span class="unit">%</span></div>
                    </div>
                    <div class="vital-card" id="bp-card">
                        <div class="vital-label">Blood Pressure</div>
                        <div class="vital-value"><span id="bp-value">0/0</span><span class="unit">mmHg</span></div>
                    </div>
                    <div class="vital-card" id="temp-card">
                        <div class="vital-label">Temperature</div>
                        <div class="vital-value"><span id="temp-value">0.0</span><span class="unit">°C</span></div>
                    </div>
                </div>
            </div>

            <div class="status-panel">
                <h2>Patient Status</h2>
                <p id="status-message">Initializing...</p>
            </div>

            <div class="controls-panel">
                <h2>Interventions</h2>
                <div class="intervention-buttons">
                    <button class="intervention-btn" id="btn-hypotensive" title="Lowers blood pressure significantly, may cause reflex tachycardia.">Administer Hypotensive Drug</button>
                    <button class="intervention-btn" id="btn-reduce-o2" title="Reduces inhaled oxygen, can lead to hypoxemia.">Reduce Oxygen Supply</button>
                    <button class="intervention-btn" id="btn-induce-mh" title="Simulates a rapid rise in temperature and heart rate (Malignant Hyperthermia).">Simulate Malignant Hyperthermia</button>
                </div>
            </div>

            <div class="correction-panel">
                <button id="btn-corrective-action" title="Reverts the last intervention and stabilizes patient.">Apply Corrective Action</button>
            </div>
        </main>
        <footer>
            <p>Educational Simulator. Not for clinical use.</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const initialVitals = {
                hr: 75,
                spo2: 98,
                bpSystolic: 110,
                bpDiastolic: 70,
                temp: 37.0
            };

            let currentVitals = { ...initialVitals };
            let vitalsBeforeIntervention = { ...initialVitals };

            let patientStatus = 'Stable';
            let activeAlarmVitalElementId = null;

            // DOM Elements
            const hrValueEl = document.getElementById('hr-value');
            const spo2ValueEl = document.getElementById('spo2-value');
            const bpValueEl = document.getElementById('bp-value');
            const tempValueEl = document.getElementById('temp-value');
            
            const hrCardEl = document.getElementById('hr-card');
            const spo2CardEl = document.getElementById('spo2-card');
            const bpCardEl = document.getElementById('bp-card');
            const tempCardEl = document.getElementById('temp-card');

            const statusMessageEl = document.getElementById('status-message');
            const interventionBtnElements = document.querySelectorAll('.intervention-btn');
            const correctiveActionBtnEl = document.getElementById('btn-corrective-action');

            const btnHypotensive = document.getElementById('btn-hypotensive');
            const btnReduceO2 = document.getElementById('btn-reduce-o2');
            const btnInduceMH = document.getElementById('btn-induce-mh');

            const thresholds = {
                spo2Low: 90,
                bpSysLow: 90,
                tempLow: 36.0,
                tempHigh: 38.5,
                // Optional: HR thresholds for future expansion
                // hrLow: 50,
                // hrHigh: 120
            };
            
            const statusColors = {
                stable: 'var(--status-stable-color)',
                alert: 'var(--status-alert-color)'
            };

            function updateDisplay() {
                hrValueEl.textContent = Math.round(currentVitals.hr);
                spo2ValueEl.textContent = Math.round(currentVitals.spo2);
                bpValueEl.textContent = `${Math.round(currentVitals.bpSystolic)}/${Math.round(currentVitals.bpDiastolic)}`;
                tempValueEl.textContent = currentVitals.temp.toFixed(1);

                statusMessageEl.textContent = patientStatus;
                statusMessageEl.style.color = (patientStatus === 'Stable') ? statusColors.stable : statusColors.alert;

                // Clear previous alarms visuals
                [hrCardEl, spo2CardEl, bpCardEl, tempCardEl].forEach(card => card.classList.remove('out-of-range'));
                
                if (activeAlarmVitalElementId) {
                    const alarmedElement = document.getElementById(activeAlarmVitalElementId);
                    if (alarmedElement) {
                        alarmedElement.classList.add('out-of-range');
                    }
                }

                if (patientStatus === 'Stable') {
                    correctiveActionBtnEl.style.display = 'none';
                    interventionBtnElements.forEach(btn => btn.disabled = false);
                } else {
                    correctiveActionBtnEl.style.display = 'block';
                    interventionBtnElements.forEach(btn => btn.disabled = true);
                }
            }

            function checkPatientStatus() {
                let newStatus = 'Stable';
                let newAlarmVitalId = null;

                // Order of checks can matter if multiple things are abnormal.
                // The first one to be true will set the status.
                if (currentVitals.spo2 < thresholds.spo2Low) {
                    newStatus = 'Low Oxygen';
                    newAlarmVitalId = 'spo2-card';
                } else if (currentVitals.bpSystolic < thresholds.bpSysLow) {
                    newStatus = 'Hypotension Detected';
                    newAlarmVitalId = 'bp-card';
                } else if (currentVitals.temp < thresholds.tempLow) {
                    newStatus = 'Temperature Alert: Hypothermia';
                    newAlarmVitalId = 'temp-card';
                } else if (currentVitals.temp > thresholds.tempHigh) {
                    newStatus = 'Temperature Alert: Hyperthermia';
                    newAlarmVitalId = 'temp-card';
                }
                // Example for future HR alert:
                // else if (currentVitals.hr > thresholds.hrHigh) {
                //     newStatus = 'Tachycardia Detected';
                //     newAlarmVitalId = 'hr-card';
                // } else if (currentVitals.hr < thresholds.hrLow) {
                //     newStatus = 'Bradycardia Detected';
                //     newAlarmVitalId = 'hr-card';
                // }


                patientStatus = newStatus;
                activeAlarmVitalElementId = newAlarmVitalId;
                updateDisplay();
            }

            function applyIntervention(interventionType) {
                if (patientStatus !== 'Stable') return; 

                vitalsBeforeIntervention = { ...currentVitals }; 

                switch (interventionType) {
                    case 'hypotensive':
                        currentVitals.bpSystolic -= 30;
                        currentVitals.bpDiastolic -= 15;
                        currentVitals.hr += 15;
                        break;
                    case 'reduce_o2':
                        currentVitals.spo2 -= 12;
                        break;
                    case 'induce_mh':
                        currentVitals.temp += 2.0;
                        currentVitals.hr += 30;
                        break;
                }

                // Clamp vital signs to reasonable minimums/maximums
                currentVitals.hr = Math.max(20, Math.min(currentVitals.hr, 250));
                currentVitals.spo2 = Math.max(0, Math.min(currentVitals.spo2, 100));
                currentVitals.bpSystolic = Math.max(30, Math.min(currentVitals.bpSystolic, 250));
                currentVitals.bpDiastolic = Math.max(20, Math.min(currentVitals.bpDiastolic, 150));
                currentVitals.temp = Math.max(30.0, Math.min(currentVitals.temp, 45.0));


                checkPatientStatus();
            }

            btnHypotensive.addEventListener('click', () => applyIntervention('hypotensive'));
            btnReduceO2.addEventListener('click', () => applyIntervention('reduce_o2'));
            btnInduceMH.addEventListener('click', () => applyIntervention('induce_mh'));

            correctiveActionBtnEl.addEventListener('click', () => {
                // Restore vitals to the state before the destabilizing intervention
                currentVitals = { ...vitalsBeforeIntervention };
                
                // Or, to always reset to initial stable state:
                // currentVitals = { ...initialVitals };
                // This ensures that even if vitalsBeforeIntervention was somehow not perfectly stable,
                // the corrective action brings it to a known good state.
                // For this exercise, using vitalsBeforeIntervention is closer to "reversing the impact".

                checkPatientStatus(); // This should set status to 'Stable'
            });

            // Initial load
            function initializeApp() {
                currentVitals = { ...initialVitals };
                vitalsBeforeIntervention = { ...initialVitals };
                patientStatus = 'Stable';
                activeAlarmVitalElementId = null;
                checkPatientStatus(); // This will call updateDisplay
            }

            initializeApp();
        });
    </script>
</body>
</html>
