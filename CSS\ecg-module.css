/* ===== ECG MODULE SPECIFIC STYLING ===== */

/* ECG Machine Styles */
.ecg-machine {
    background: linear-gradient(135deg, #1e293b, #334155);
    border-radius: 24px;
    padding: 2rem;
    border: 2px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.2);
}

.ecg-display {
    background: #000;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 3px solid rgba(59, 130, 246, 0.5);
    position: relative;
}

.ecg-screen {
    min-height: 600px;
}

.screen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.3);
    margin-bottom: 2rem;
}

.screen-header h4 {
    color: #00ff88;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.ecg-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.heart-rate-display {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    background: rgba(0, 255, 136, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.hr-value {
    font-size: 2rem;
    font-weight: 800;
    color: #00ff88;
    font-family: 'Courier New', monospace;
}

.hr-unit {
    font-size: 1rem;
    color: #64748b;
    font-weight: 500;
}

/* Leads Grid */
.leads-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.lead-group {
    background: rgba(59, 130, 246, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.lead-group h5 {
    color: #00ff88;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.lead-display {
    margin-bottom: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.lead-label {
    color: #3b82f6;
    font-weight: 700;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-align: center;
}

.ecg-canvas {
    width: 100%;
    background: rgba(0, 255, 136, 0.05);
    border-radius: 4px;
    display: block;
}

.lead-info {
    color: #64748b;
    font-size: 0.7rem;
    text-align: center;
    margin-top: 0.25rem;
}

/* Precordial Leads Special Layout */
.lead-group.precordial {
    grid-column: 1 / -1;
}

.precordial-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.precordial-grid .lead-display {
    margin-bottom: 0;
}

/* Analysis Panel */
.analysis-panel {
    background: rgba(139, 92, 246, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.analysis-panel h5 {
    color: #8b5cf6;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.analysis-results {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.analysis-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    background: rgba(0, 0, 0, 0.3);
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.analysis-label {
    color: #cbd5e1;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.analysis-value {
    color: #8b5cf6;
    font-size: 1rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

/* ECG Controls */
.ecg-controls {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2rem;
    background: rgba(59, 130, 246, 0.05);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.control-section h5 {
    color: var(--module-text-primary);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.rhythm-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.rhythm-btn {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.3);
    color: #3b82f6;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.rhythm-btn:hover {
    background: rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
}

.rhythm-btn.active {
    background: var(--module-gradient-primary);
    color: white;
    border-color: transparent;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.analysis-tools {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.tool-btn {
    background: var(--module-gradient-secondary);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.tool-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.4);
}

.display-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.display-controls label {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    color: var(--module-text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.display-controls select {
    background: var(--module-bg-secondary);
    border: 1px solid var(--module-border);
    color: var(--module-text-primary);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
}

/* Cardiac Conduction System */
.conduction-component {
    cursor: pointer;
    transition: all 0.3s ease;
}

.conduction-component:hover {
    transform: scale(1.1);
}

.component-circle {
    transition: all 0.3s ease;
}

.component-circle.sa {
    fill: rgba(239, 68, 68, 0.2);
    stroke: #ef4444;
    stroke-width: 3;
}

.component-circle.av {
    fill: rgba(59, 130, 246, 0.2);
    stroke: #3b82f6;
    stroke-width: 3;
}

.component-rect.his {
    fill: rgba(139, 92, 246, 0.2);
    stroke: #8b5cf6;
    stroke-width: 3;
}

.bundle-path {
    transition: all 0.3s ease;
}

.purkinje-path {
    transition: all 0.3s ease;
}

.conduction-component:hover .component-circle,
.conduction-component:hover .component-rect {
    filter: drop-shadow(0 0 10px currentColor);
}

.component-label {
    fill: white;
    font-size: 10px;
    font-weight: 700;
    text-anchor: middle;
    dominant-baseline: middle;
}

.component-name {
    fill: var(--module-text-primary);
    font-size: 12px;
    font-weight: 600;
    text-anchor: middle;
}

.component-rate {
    fill: var(--module-text-secondary);
    font-size: 10px;
    font-weight: 500;
    text-anchor: middle;
}

.heart-outline {
    transition: all 0.3s ease;
}

/* Electrical Pathway Animation */
.electrical-pulse {
    animation: electricalPulse 2s ease-in-out infinite;
}

.sa-pulse {
    fill: #ef4444;
    animation-delay: 0s;
}

.av-pulse {
    fill: #3b82f6;
    animation-delay: 0.2s;
}

.his-pulse {
    fill: #8b5cf6;
    animation-delay: 0.4s;
}

.left-pulse,
.right-pulse {
    fill: #06b6d4;
    animation-delay: 0.6s;
}

@keyframes electricalPulse {
    0%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

/* ECG Waveform Animations */
.ecg-trace {
    stroke: #00ff88;
    stroke-width: 2;
    fill: none;
    animation: ecgTrace 2s linear infinite;
}

@keyframes ecgTrace {
    0% {
        stroke-dasharray: 0, 1000;
        stroke-dashoffset: 0;
    }
    100% {
        stroke-dasharray: 1000, 0;
        stroke-dashoffset: -1000;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .leads-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .precordial-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .analysis-results {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .ecg-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .rhythm-controls {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .precordial-grid {
        grid-template-columns: 1fr;
    }
    
    .analysis-results {
        grid-template-columns: 1fr;
    }
    
    .rhythm-controls,
    .analysis-tools {
        grid-template-columns: 1fr;
    }
}
