<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Interpretation Module - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/ecg-module.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="../HTML/lectures.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Lectures
                </a>
                <div class="module-title">
                    <h1>ECG Interpretation Module</h1>
                    <p>Advanced Electrocardiogram Analysis & Cardiac Rhythms</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Module Overview -->
    <section class="module-overview">
        <div class="overview-content">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-heartbeat animated-heartbeat"></i>
                    </div>
                    <h3>ECG Fundamentals</h3>
                    <p>Master the electrical conduction system and basic ECG interpretation principles</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%"></div>
                    </div>
                    <span class="progress-text">90% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-chart-line animated-pulse"></i>
                    </div>
                    <h3>Rhythm Analysis</h3>
                    <p>Systematic approach to cardiac rhythm identification and arrhythmia recognition</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <span class="progress-text">75% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-stethoscope animated-gauge"></i>
                    </div>
                    <h3>Clinical Correlation</h3>
                    <p>Pathophysiology and clinical significance of ECG findings</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <span class="progress-text">85% Complete</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive ECG Analyzer -->
    <section class="learning-tools">
        <div class="tools-container">
            <h2 class="section-title">Interactive ECG Analyzer</h2>
            
            <!-- ECG Simulator -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Virtual ECG Machine</h3>
                    <p>Real-time ECG simulation with multiple rhythm patterns and analysis tools</p>
                </div>
                
                <div class="ecg-machine">
                    <!-- ECG Display -->
                    <div class="ecg-display">
                        <div class="ecg-screen">
                            <div class="screen-header">
                                <h4>12-Lead ECG Monitor</h4>
                                <div class="ecg-status">
                                    <span class="status-light active" id="ecgPowerStatus"></span>
                                    <span class="status-text">Recording</span>
                                    <div class="heart-rate-display">
                                        <span class="hr-value">75</span>
                                        <span class="hr-unit">bpm</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Lead Display Grid -->
                            <div class="leads-grid">
                                <!-- Limb Leads -->
                                <div class="lead-group">
                                    <h5>Limb Leads</h5>
                                    <div class="lead-display" data-lead="I">
                                        <div class="lead-label">Lead I</div>
                                        <canvas class="ecg-canvas" width="300" height="80"></canvas>
                                        <div class="lead-info">0° axis</div>
                                    </div>
                                    <div class="lead-display" data-lead="II">
                                        <div class="lead-label">Lead II</div>
                                        <canvas class="ecg-canvas" width="300" height="80"></canvas>
                                        <div class="lead-info">60° axis</div>
                                    </div>
                                    <div class="lead-display" data-lead="III">
                                        <div class="lead-label">Lead III</div>
                                        <canvas class="ecg-canvas" width="300" height="80"></canvas>
                                        <div class="lead-info">120° axis</div>
                                    </div>
                                </div>
                                
                                <!-- Augmented Leads -->
                                <div class="lead-group">
                                    <h5>Augmented Leads</h5>
                                    <div class="lead-display" data-lead="aVR">
                                        <div class="lead-label">aVR</div>
                                        <canvas class="ecg-canvas" width="300" height="80"></canvas>
                                        <div class="lead-info">-150° axis</div>
                                    </div>
                                    <div class="lead-display" data-lead="aVL">
                                        <div class="lead-label">aVL</div>
                                        <canvas class="ecg-canvas" width="300" height="80"></canvas>
                                        <div class="lead-info">-30° axis</div>
                                    </div>
                                    <div class="lead-display" data-lead="aVF">
                                        <div class="lead-label">aVF</div>
                                        <canvas class="ecg-canvas" width="300" height="80"></canvas>
                                        <div class="lead-info">90° axis</div>
                                    </div>
                                </div>
                                
                                <!-- Precordial Leads -->
                                <div class="lead-group precordial">
                                    <h5>Precordial Leads</h5>
                                    <div class="precordial-grid">
                                        <div class="lead-display" data-lead="V1">
                                            <div class="lead-label">V1</div>
                                            <canvas class="ecg-canvas" width="200" height="60"></canvas>
                                        </div>
                                        <div class="lead-display" data-lead="V2">
                                            <div class="lead-label">V2</div>
                                            <canvas class="ecg-canvas" width="200" height="60"></canvas>
                                        </div>
                                        <div class="lead-display" data-lead="V3">
                                            <div class="lead-label">V3</div>
                                            <canvas class="ecg-canvas" width="200" height="60"></canvas>
                                        </div>
                                        <div class="lead-display" data-lead="V4">
                                            <div class="lead-label">V4</div>
                                            <canvas class="ecg-canvas" width="200" height="60"></canvas>
                                        </div>
                                        <div class="lead-display" data-lead="V5">
                                            <div class="lead-label">V5</div>
                                            <canvas class="ecg-canvas" width="200" height="60"></canvas>
                                        </div>
                                        <div class="lead-display" data-lead="V6">
                                            <div class="lead-label">V6</div>
                                            <canvas class="ecg-canvas" width="200" height="60"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- ECG Analysis Panel -->
                            <div class="analysis-panel">
                                <h5>Automated Analysis</h5>
                                <div class="analysis-results">
                                    <div class="analysis-item">
                                        <span class="analysis-label">Rhythm:</span>
                                        <span class="analysis-value" id="rhythmAnalysis">Normal Sinus Rhythm</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">Rate:</span>
                                        <span class="analysis-value" id="rateAnalysis">75 bpm</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">PR Interval:</span>
                                        <span class="analysis-value" id="prAnalysis">0.16 sec</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">QRS Duration:</span>
                                        <span class="analysis-value" id="qrsAnalysis">0.08 sec</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">QT Interval:</span>
                                        <span class="analysis-value" id="qtAnalysis">0.40 sec</span>
                                    </div>
                                    <div class="analysis-item">
                                        <span class="analysis-label">Axis:</span>
                                        <span class="analysis-value" id="axisAnalysis">Normal (60°)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- ECG Controls -->
                        <div class="ecg-controls">
                            <div class="control-section">
                                <h5>Rhythm Selection</h5>
                                <div class="rhythm-controls">
                                    <button class="rhythm-btn active" onclick="selectRhythm('normal')">
                                        <i class="fas fa-heartbeat"></i>
                                        Normal Sinus
                                    </button>
                                    <button class="rhythm-btn" onclick="selectRhythm('bradycardia')">
                                        <i class="fas fa-heart"></i>
                                        Bradycardia
                                    </button>
                                    <button class="rhythm-btn" onclick="selectRhythm('tachycardia')">
                                        <i class="fas fa-heartbeat"></i>
                                        Tachycardia
                                    </button>
                                    <button class="rhythm-btn" onclick="selectRhythm('afib')">
                                        <i class="fas fa-wave-square"></i>
                                        Atrial Fib
                                    </button>
                                    <button class="rhythm-btn" onclick="selectRhythm('vfib')">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        V-Fib
                                    </button>
                                    <button class="rhythm-btn" onclick="selectRhythm('asystole')">
                                        <i class="fas fa-minus"></i>
                                        Asystole
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Analysis Tools</h5>
                                <div class="analysis-tools">
                                    <button class="tool-btn" onclick="measureInterval()">
                                        <i class="fas fa-ruler"></i>
                                        Measure
                                    </button>
                                    <button class="tool-btn" onclick="calculateRate()">
                                        <i class="fas fa-calculator"></i>
                                        Rate Calc
                                    </button>
                                    <button class="tool-btn" onclick="showCaliper()">
                                        <i class="fas fa-compass"></i>
                                        Caliper
                                    </button>
                                    <button class="tool-btn" onclick="printECG()">
                                        <i class="fas fa-print"></i>
                                        Print
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Display Settings</h5>
                                <div class="display-controls">
                                    <label>
                                        Speed:
                                        <select onchange="changeSpeed(this.value)">
                                            <option value="25">25 mm/s</option>
                                            <option value="50" selected>50 mm/s</option>
                                        </select>
                                    </label>
                                    <label>
                                        Gain:
                                        <select onchange="changeGain(this.value)">
                                            <option value="5">5 mm/mV</option>
                                            <option value="10" selected>10 mm/mV</option>
                                            <option value="20">20 mm/mV</option>
                                        </select>
                                    </label>
                                    <label>
                                        Filter:
                                        <select onchange="changeFilter(this.value)">
                                            <option value="0.05">0.05-150 Hz</option>
                                            <option value="0.5" selected>0.5-40 Hz</option>
                                            <option value="5">5-15 Hz</option>
                                        </select>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cardiac Conduction System -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Cardiac Conduction System</h3>
                    <p>Interactive anatomy and electrical pathway visualization</p>
                </div>
                
                <div class="diagram-container">
                    <div class="diagram-tabs">
                        <button class="tab-btn active" onclick="showConductionDiagram('anatomy')">Anatomy</button>
                        <button class="tab-btn" onclick="showConductionDiagram('electrical')">Electrical</button>
                        <button class="tab-btn" onclick="showConductionDiagram('timing')">Timing</button>
                    </div>
                    
                    <!-- Anatomy Diagram -->
                    <div id="anatomyDiagram" class="diagram-panel active">
                        <svg class="interactive-diagram" viewBox="0 0 800 600">
                            <!-- Heart Outline -->
                            <path d="M 200 150 Q 250 100 300 150 Q 350 100 400 150 Q 450 200 400 300 Q 350 400 300 350 Q 250 400 200 300 Q 150 200 200 150 Z" 
                                  fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" stroke-width="3" class="heart-outline"/>
                            
                            <!-- SA Node -->
                            <g class="conduction-component sa-node" onclick="showConductionInfo('sa-node')">
                                <circle cx="350" cy="180" r="15" class="component-circle sa"/>
                                <text x="350" y="185" class="component-label">SA</text>
                                <text x="350" y="160" class="component-name">Sinoatrial Node</text>
                                <text x="350" y="210" class="component-rate">60-100 bpm</text>
                            </g>
                            
                            <!-- AV Node -->
                            <g class="conduction-component av-node" onclick="showConductionInfo('av-node')">
                                <circle cx="300" cy="280" r="12" class="component-circle av"/>
                                <text x="300" y="285" class="component-label">AV</text>
                                <text x="300" y="260" class="component-name">Atrioventricular Node</text>
                                <text x="300" y="310" class="component-rate">40-60 bpm</text>
                            </g>
                            
                            <!-- Bundle of His -->
                            <g class="conduction-component bundle-his" onclick="showConductionInfo('bundle-his')">
                                <rect x="295" y="320" width="10" height="30" class="component-rect his"/>
                                <text x="300" y="340" class="component-label">His</text>
                                <text x="300" y="365" class="component-name">Bundle of His</text>
                            </g>
                            
                            <!-- Left Bundle Branch -->
                            <g class="conduction-component left-bundle" onclick="showConductionInfo('left-bundle')">
                                <path d="M 300 350 L 250 380 L 220 420" stroke="#8b5cf6" stroke-width="4" fill="none" class="bundle-path"/>
                                <text x="220" y="440" class="component-name">Left Bundle</text>
                                <text x="220" y="455" class="component-rate">20-40 bpm</text>
                            </g>
                            
                            <!-- Right Bundle Branch -->
                            <g class="conduction-component right-bundle" onclick="showConductionInfo('right-bundle')">
                                <path d="M 300 350 L 350 380 L 380 420" stroke="#8b5cf6" stroke-width="4" fill="none" class="bundle-path"/>
                                <text x="380" y="440" class="component-name">Right Bundle</text>
                                <text x="380" y="455" class="component-rate">20-40 bpm</text>
                            </g>
                            
                            <!-- Purkinje Fibers -->
                            <g class="conduction-component purkinje" onclick="showConductionInfo('purkinje')">
                                <path d="M 220 420 Q 200 450 180 480 Q 160 500 140 480" stroke="#06b6d4" stroke-width="2" fill="none" class="purkinje-path"/>
                                <path d="M 380 420 Q 400 450 420 480 Q 440 500 460 480" stroke="#06b6d4" stroke-width="2" fill="none" class="purkinje-path"/>
                                <text x="300" y="520" class="component-name">Purkinje Fibers</text>
                                <text x="300" y="535" class="component-rate">20-40 bpm</text>
                            </g>
                            
                            <!-- Electrical Pathway Animation -->
                            <g class="electrical-pathway">
                                <circle cx="350" cy="180" r="20" class="electrical-pulse sa-pulse" opacity="0"/>
                                <circle cx="300" cy="280" r="15" class="electrical-pulse av-pulse" opacity="0"/>
                                <circle cx="300" cy="350" r="10" class="electrical-pulse his-pulse" opacity="0"/>
                                <circle cx="250" cy="400" r="8" class="electrical-pulse left-pulse" opacity="0"/>
                                <circle cx="350" cy="400" r="8" class="electrical-pulse right-pulse" opacity="0"/>
                            </g>
                        </svg>
                    </div>
                    
                    <!-- Component Information Panel -->
                    <div class="component-info" id="conductionComponentInfo">
                        <h4 id="conductionComponentTitle">Cardiac Conduction System</h4>
                        <p id="conductionComponentDescription">The heart's electrical system controls the coordinated contraction of cardiac muscle. Click on any component to learn more about its function and characteristics.</p>
                        <div id="conductionComponentSpecs"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comprehensive ECG Notes -->
    <section class="lecture-notes">
        <div class="notes-container">
            <h2 class="section-title">ECG Interpretation Fundamentals</h2>
            
            <div class="notes-grid">
                <!-- ECG Basics -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h3>ECG Fundamentals</h3>
                    </div>
                    <div class="note-content">
                        <h4>Electrical Conduction</h4>
                        <ul>
                            <li><strong>SA Node:</strong> Primary pacemaker (60-100 bpm)</li>
                            <li><strong>AV Node:</strong> Delays impulse (40-60 bpm backup)</li>
                            <li><strong>Bundle of His:</strong> Conducts to ventricles</li>
                            <li><strong>Bundle Branches:</strong> Left and right pathways</li>
                            <li><strong>Purkinje Fibers:</strong> Ventricular depolarization</li>
                        </ul>
                        
                        <h4>ECG Waveforms</h4>
                        <div class="reference-table">
                            <div class="table-row">
                                <span class="parameter">P Wave</span>
                                <span class="range">Atrial depolarization</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">PR Interval</span>
                                <span class="range">0.12-0.20 seconds</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">QRS Complex</span>
                                <span class="range">0.06-0.10 seconds</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">T Wave</span>
                                <span class="range">Ventricular repolarization</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">QT Interval</span>
                                <span class="range">0.36-0.44 seconds</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Rhythm Analysis -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>Systematic Analysis</h3>
                    </div>
                    <div class="note-content">
                        <h4>5-Step Approach</h4>
                        <ol>
                            <li><strong>Rate:</strong> 300/large squares or 1500/small squares</li>
                            <li><strong>Rhythm:</strong> Regular vs irregular patterns</li>
                            <li><strong>Axis:</strong> Normal (-30° to +90°)</li>
                            <li><strong>Intervals:</strong> PR, QRS, QT measurements</li>
                            <li><strong>Morphology:</strong> P, QRS, T wave analysis</li>
                        </ol>
                        
                        <h4>Common Arrhythmias</h4>
                        <ul>
                            <li><strong>Sinus Bradycardia:</strong> Rate &lt;60 bpm</li>
                            <li><strong>Sinus Tachycardia:</strong> Rate &gt;100 bpm</li>
                            <li><strong>Atrial Fibrillation:</strong> Irregular rhythm, no P waves</li>
                            <li><strong>Ventricular Tachycardia:</strong> Wide QRS, rate &gt;150</li>
                            <li><strong>Heart Blocks:</strong> Delayed or blocked conduction</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/ecg-analyzer.js"></script>
    <script src="../JS/conduction-system.js"></script>
    <script>
        // Initialize module
        document.addEventListener('DOMContentLoaded', function() {
            initializeECGAnalyzer();
            initializeConductionSystem();
            startECGAnimations();
        });
    </script>
</body>
</html>
