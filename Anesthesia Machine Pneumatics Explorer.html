<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine Pneumatics Explorer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            width: 90%;
            max-width: 900px;
            margin: 20px auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        h1, h2, h3 {
            color: #0056b3; /* A professional blue */
            text-align: center;
        }

        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        /* Diagram Styles */
        .diagram-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            position: relative; /* For tooltip positioning */
        }

        .pressure-system {
            width: 80%;
            max-width: 300px; /* Max width for individual system boxes */
            padding: 20px;
            border: 2px solid #333;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            position: relative; /* For pseudo-elements like arrows */
        }

        .pressure-system:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .pressure-system h4 {
            margin-top: 0;
            color: #fff;
        }

        #high-pressure { background-color: #d9534f; /* Reddish */ color: white; }
        #intermediate-pressure { background-color: #f0ad4e; /* Orangey */ color: white; }
        #low-pressure { background-color: #5cb85c; /* Greenish */ color: white; }

        .arrow {
            font-size: 24px;
            color: #333;
            margin: 5px 0;
        }

        /* Tooltip Styles */
        .tooltip {
            position: fixed; /* Fixed or absolute depending on desired behavior */
            background-color: #333;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 0.9em;
            z-index: 1000;
            display: none; /* Hidden by default */
            max-width: 300px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            pointer-events: none; /* So it doesn't interfere with mouse events on elements below */
        }
        .tooltip h5 {
            margin-top: 0;
            margin-bottom: 5px;
            color: #f0ad4e; /* Highlight title */
        }
        .tooltip ul {
            margin: 0;
            padding-left: 20px;
        }

        /* Quiz Styles */
        .quiz-question {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .quiz-question p {
            margin-bottom: 8px;
            font-weight: bold;
        }
        .quiz-question label {
            display: block;
            margin-bottom: 5px;
            cursor: pointer;
        }
        .quiz-question input[type="radio"] {
            margin-right: 8px;
        }
        #quiz-submit {
            display: block;
            margin: 20px auto 10px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }
        #quiz-submit:hover {
            background-color: #0056b3;
        }
        #quiz-results {
            margin-top: 15px;
            font-weight: bold;
            text-align: center;
        }

        /* Challenge Styles */
        .challenge-controls label {
            display: block;
            margin-bottom: 5px;
        }
        .challenge-controls input[type="range"] {
            width: 100%;
            margin-bottom: 10px;
        }
        #pressure-value {
            font-weight: bold;
        }
        .status-display div {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .status-normal { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;}
        .status-compromised { background-color: #fff3cd; color: #856404; border: 1px solid #ffeeba;}
        .status-critical { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;}
        #challenge-alert {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }

        /* Responsive Adjustments */
        @media (min-width: 768px) {
            .diagram-container {
                flex-direction: row;
                justify-content: space-around;
                align-items: flex-start; /* Align boxes at the top */
            }
            .pressure-system {
                width: 30%; /* Adjust width for side-by-side layout */
            }
            .arrow { /* For horizontal arrows if we add them */
                transform: rotate(0deg); 
                margin: auto 10px;
            }
            .diagram-flow-arrows { /* Container for arrows between system boxes on desktop */
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                align-items: center;
                height: 300px; /* Approximate height of a box */
            }
        }
        
        /* Specific styling for the conceptual diagram flow */
        .diagram-flow-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
        
        .diagram-element {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-align: center;
            background-color: #f9f9f9;
            min-width: 150px;
        }
        
        .diagram-interactive-area {
            display: flex;
            flex-direction: column; /* Mobile first */
            align-items: center;
            width: 100%;
            gap: 15px; /* Space between system boxes */
        }

        @media (min-width: 768px) {
            .diagram-interactive-area {
                flex-direction: row; /* Desktop layout */
                justify-content: space-between;
                align-items: stretch; /* Make boxes same height if content differs */
            }
            .pressure-system {
                flex: 1; /* Distribute space equally */
                margin: 0 5px; /* Small margin between boxes */
            }
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.9em;
            color: #666;
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>Anesthesia Machine Pneumatics Explorer</h1>

        <div class="section" id="intro-section">
            <h2>Introduction</h2>
            <p>This app will help you understand the basics of an anesthesia machine's pneumatic system. The system is typically divided into three parts based on pressure:</p>
            <ul>
                <li><strong>High-Pressure System:</strong> Deals with gas from cylinders.</li>
                <li><strong>Intermediate-Pressure System:</strong> Receives gas from pipelines or regulated cylinder gas.</li>
                <li><strong>Low-Pressure System:</strong> Fine-tunes gas flow to the patient.</li>
            </ul>
            <p>Interact with the diagram below, take the quiz, and try the challenge!</p>
        </div>

        <div class="section" id="diagram-section">
            <h2>Pneumatic System Diagram</h2>
            <p style="text-align:center;">Mouse over (or tap on mobile) each system block for more details.</p>
            
            <div class="diagram-flow-container">
                <div class="diagram-element">Gas Cylinders (e.g., O<sub>2</sub>, N<sub>2</sub>O)</div>
                <div class="arrow">⬇️</div>
                <div class="diagram-element">Pipeline Inlets (e.g., O<sub>2</sub>, N<sub>2</sub>O, Air)</div>
                <div class="arrow">⬇️</div>
            </div>

            <div class="diagram-interactive-area">
                <div class="pressure-system" id="high-pressure" data-system="high">
                    <h4>High-Pressure System</h4>
                </div>
                <!-- On desktop, arrows would be placed between these by a separate div or pseudo-elements -->
                <div class="pressure-system" id="intermediate-pressure" data-system="intermediate">
                    <h4>Intermediate-Pressure System</h4>
                </div>
                <div class="pressure-system" id="low-pressure" data-system="low">
                    <h4>Low-Pressure System</h4>
                </div>
            </div>
            <div class="arrow" style="text-align:center; width:100%;">⬇️ To Common Gas Outlet & Patient Circuit ⬇️</div>
            <div id="tooltip" class="tooltip"></div>
        </div>

        <div class="section" id="quiz-section">
            <h2>Knowledge Check</h2>
            <form id="quiz-form">
                <div class="quiz-question" id="q1">
                    <p>1. The <strong>Cylinder Pressure Gauge</strong> is primarily associated with which system?</p>
                    <label><input type="radio" name="q1" value="A"> A) High-Pressure System</label>
                    <label><input type="radio" name="q1" value="B"> B) Intermediate-Pressure System</label>
                    <label><input type="radio" name="q1" value="C"> C) Low-Pressure System</label>
                </div>
                <div class="quiz-question" id="q2">
                    <p>2. The <strong>Oxygen Flush Valve</strong> bypasses the low-pressure system and is part of the:</p>
                    <label><input type="radio" name="q2" value="A"> A) High-Pressure System</label>
                    <label><input type="radio" name="q2" value="B"> B) Intermediate-Pressure System</label>
                    <label><input type="radio" name="q2" value="C"> C) Low-Pressure System</label>
                </div>
                <div class="quiz-question" id="q3">
                    <p>3. <strong>Flowmeter tubes (Thorpe tubes)</strong>, where gas flow is precisely controlled and measured, are found in the:</p>
                    <label><input type="radio" name="q3" value="A"> A) High-Pressure System</label>
                    <label><input type="radio" name="q3" value="B"> B) Intermediate-Pressure System</label>
                    <label><input type="radio" name="q3" value="C"> C) Low-Pressure System</label>
                </div>
                <div class="quiz-question" id="q4">
                    <p>4. The <strong>Pipeline Inlet</strong> (e.g., for Oxygen from the wall) connects to the:</p>
                    <label><input type="radio" name="q4" value="A"> A) High-Pressure System</label>
                    <label><input type="radio" name="q4" value="B"> B) Intermediate-Pressure System</label>
                    <label><input type="radio" name="q4" value="C"> C) Low-Pressure System</label>
                </div>
                 <div class="quiz-question" id="q5">
                    <p>5. What is the primary function of the <strong>High-Pressure System</strong>?</p>
                    <label><input type="radio" name="q5" value="A"> A) To precisely control gas flow to the patient.</label>
                    <label><input type="radio" name="q5" value="B"> B) To receive gases from cylinders and reduce their pressure to a usable level.</label>
                    <label><input type="radio" name="q5" value="C"> C) To mix anesthetic vapors with fresh gas.</label>
                </div>
                <button type="button" id="quiz-submit">Submit Answers</button>
                <div id="quiz-results"></div>
            </form>
        </div>

        <div class="section" id="challenge-section">
            <h2>Pressure Challenge</h2>
            <p>Adjust the Oxygen cylinder pressure and observe the effects on the downstream systems. (This is a simplified model focusing on cylinder supply).</p>
            <div class="challenge-controls">
                <label for="o2-cylinder-pressure">Oxygen Cylinder Pressure: <span id="pressure-value">1100</span> psi</label>
                <input type="range" id="o2-cylinder-pressure" min="0" max="2200" value="1100">
            </div>
            <div class="status-display">
                <h3>System Status:</h3>
                <div id="intermediate-pressure-status">Intermediate System Pressure: -- psi</div>
                <div id="low-pressure-status">Low-Pressure System Function: --</div>
            </div>
            <div id="challenge-alert"></div>
        </div>

        <div class="footer">
            <p>Disclaimer: This is a simplified educational tool and not a substitute for formal training or manufacturer's documentation.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Tooltip Data ---
            const systemInfo = {
                high: {
                    title: "High-Pressure System",
                    description: "Receives gases from high-pressure cylinders (e.g., E-cylinders) and reduces their very high pressure (up to ~2200 psi for O<sub>2</sub>) to a lower, more constant pressure (around 45-55 psi).",
                    components: [
                        "Hanger Yoke Assembly (with PISS - Pin Index Safety System)",
                        "Cylinder Pressure Gauge",
                        "Pressure Regulator (First-stage)"
                    ]
                },
                intermediate: {
                    title: "Intermediate-Pressure System",
                    description: "Receives gases from the hospital pipeline system OR from the high-pressure system's regulators, at a nominal pressure of 45-55 psi. It distributes these gases to various components.",
                    components: [
                        "Pipeline Inlets (with DISS - Diameter Index Safety System)",
                        "Pipeline Pressure Gauges",
                        "Oxygen Pressure Failure Device (Fail-Safe Valve)",
                        "Oxygen Flush Valve (delivers O<sub>2</sub> directly to common gas outlet)",
                        "Second-stage regulators (if present, further refines pressure)",
                        "Flowmeter supply inlet"
                    ]
                },
                low: {
                    title: "Low-Pressure System",
                    description: "Receives gases from the intermediate-pressure system (via flow control valves) and precisely controls and measures their flow. Pressure here is slightly above atmospheric. Gases are mixed with anesthetic vapor before delivery to the patient.",
                    components: [
                        "Flow Control Valves (Needle valves)",
                        "Flowmeter Tubes (e.g., Thorpe tubes)",
                        "Vaporizer Manifold & Vaporizers",
                        "Check Valve (often before common gas outlet or vaporizers)",
                        "Common (Fresh) Gas Outlet (CGO)"
                    ]
                }
            };

            // --- Tooltip Functionality ---
            const tooltip = document.getElementById('tooltip');
            const pressureSystems = document.querySelectorAll('.pressure-system');

            pressureSystems.forEach(system => {
                const systemKey = system.dataset.system;
                const info = systemInfo[systemKey];

                const showTooltip = (event) => {
                    let content = `<h5>${info.title}</h5><p>${info.description}</p><strong>Key Components:</strong><ul>`;
                    info.components.forEach(comp => content += `<li>${comp}</li>`);
                    content += `</ul>`;
                    tooltip.innerHTML = content;
                    tooltip.style.display = 'block';
                    
                    // Position tooltip near mouse/tap
                    // Adjustments to keep tooltip within viewport
                    const xOffset = 15;
                    const yOffset = 15;
                    let x = event.clientX + xOffset;
                    let y = event.clientY + yOffset;

                    if (x + tooltip.offsetWidth > window.innerWidth) {
                        x = event.clientX - tooltip.offsetWidth - xOffset;
                    }
                    if (y + tooltip.offsetHeight > window.innerHeight) {
                        y = event.clientY - tooltip.offsetHeight - yOffset;
                    }
                    if (x < 0) x = xOffset;
                    if (y < 0) y = yOffset;


                    tooltip.style.left = `${x}px`;
                    tooltip.style.top = `${y}px`;
                };

                const hideTooltip = () => {
                    tooltip.style.display = 'none';
                };

                system.addEventListener('mouseenter', showTooltip);
                system.addEventListener('mouseleave', hideTooltip);
                system.addEventListener('click', (event) => { // For tap on mobile
                    showTooltip(event);
                    // Could add a small delay then auto-hide for tap, or require another tap to hide
                    // For simplicity, it will stay until mouseleave or another interaction.
                });
            });
             // Hide tooltip on general click outside interactive elements (simplification)
            document.addEventListener('click', (event) => {
                if (!event.target.closest('.pressure-system') && !event.target.closest('.tooltip')) {
                    tooltip.style.display = 'none';
                }
            });


            // --- Quiz Functionality ---
            const quizForm = document.getElementById('quiz-form');
            const quizSubmitBtn = document.getElementById('quiz-submit');
            const quizResultsDiv = document.getElementById('quiz-results');

            const quizAnswers = {
                q1: "A",
                q2: "B",
                q3: "C",
                q4: "B",
                q5: "B"
            };

            quizSubmitBtn.addEventListener('click', () => {
                let score = 0;
                const totalQuestions = Object.keys(quizAnswers).length;
                const userAnswers = {};

                for (let i = 1; i <= totalQuestions; i++) {
                    const questionName = `q${i}`;
                    const selectedOption = quizForm.querySelector(`input[name="${questionName}"]:checked`);
                    const questionDiv = document.getElementById(questionName);
                    
                    // Reset previous feedback styling
                    questionDiv.style.border = "none";

                    if (selectedOption) {
                        userAnswers[questionName] = selectedOption.value;
                        if (userAnswers[questionName] === quizAnswers[questionName]) {
                            score++;
                            questionDiv.style.border = "2px solid green";
                        } else {
                            questionDiv.style.border = "2px solid red";
                        }
                    } else {
                        // Mark unanswered questions
                         questionDiv.style.border = "2px solid orange"; 
                    }
                }
                quizResultsDiv.innerHTML = `You scored ${score} out of ${totalQuestions}.`;
                if (score === totalQuestions) {
                    quizResultsDiv.innerHTML += " Excellent work!";
                    quizResultsDiv.style.color = "green";
                } else if (score >= totalQuestions / 2) {
                    quizResultsDiv.innerHTML += " Good job! Review the highlighted questions for areas to improve.";
                    quizResultsDiv.style.color = "orange";
                }
                 else {
                    quizResultsDiv.innerHTML += " Keep learning! Review the diagram and try again.";
                    quizResultsDiv.style.color = "red";
                }
            });

            // --- Challenge Functionality ---
            const o2PressureSlider = document.getElementById('o2-cylinder-pressure');
            const pressureValueSpan = document.getElementById('pressure-value');
            const intermediateStatusDiv = document.getElementById('intermediate-pressure-status');
            const lowPressureStatusDiv = document.getElementById('low-pressure-status');
            const challengeAlertDiv = document.getElementById('challenge-alert');

            const REGULATOR_SET_POINT = 50; // psi
            const MIN_INTERMEDIATE_FOR_LOW_P_NORMAL = 40; // psi
            const MIN_INTERMEDIATE_FOR_LOW_P_COMPROMISED = 20; // psi

            function updateChallengeStatus() {
                const cylinderPressure = parseInt(o2PressureSlider.value);
                pressureValueSpan.textContent = cylinderPressure;

                let intermediatePressure;
                if (cylinderPressure >= REGULATOR_SET_POINT) {
                    intermediatePressure = REGULATOR_SET_POINT;
                } else {
                    intermediatePressure = cylinderPressure;
                }

                intermediateStatusDiv.textContent = `Intermediate System Pressure: ${intermediatePressure} psi`;
                intermediateStatusDiv.className = ''; // Reset class

                let lowPressureStatusText;
                let lowPressureStatusClass;
                let alertMessage = "";
                challengeAlertDiv.className = ''; // Reset class
                challengeAlertDiv.textContent = '';


                if (intermediatePressure >= MIN_INTERMEDIATE_FOR_LOW_P_NORMAL) {
                    lowPressureStatusText = "Normal Operation";
                    lowPressureStatusClass = "status-normal";
                    intermediateStatusDiv.classList.add('status-normal');
                } else if (intermediatePressure >= MIN_INTERMEDIATE_FOR_LOW_P_COMPROMISED) {
                    lowPressureStatusText = "Compromised - Flow may be reduced";
                    lowPressureStatusClass = "status-compromised";
                    intermediateStatusDiv.classList.add('status-compromised');
                    alertMessage = `Warning: Intermediate pressure is low (${intermediatePressure} psi). Risk to low-pressure system.`;
                    challengeAlertDiv.className = 'status-compromised';
                } else {
                    lowPressureStatusText = "Critical - Low pressure system likely failing";
                    lowPressureStatusClass = "status-critical";
                    intermediateStatusDiv.classList.add('status-critical');
                    alertMessage = `CRITICAL ALERT: Intermediate pressure is very low (${intermediatePressure} psi)! Low-pressure system will not function correctly. Patient safety risk!`;
                    challengeAlertDiv.className = 'status-critical';
                }

                lowPressureStatusDiv.textContent = `Low-Pressure System Function: ${lowPressureStatusText}`;
                lowPressureStatusDiv.className = lowPressureStatusClass;
                
                if (alertMessage) {
                    challengeAlertDiv.textContent = alertMessage;
                } else {
                    challengeAlertDiv.textContent = "All systems nominal based on current cylinder pressure.";
                    challengeAlertDiv.className = 'status-normal';
                }
            }

            o2PressureSlider.addEventListener('input', updateChallengeStatus);
            updateChallengeStatus(); // Initial call to set status
        });
    </script>
</body>
</html>
