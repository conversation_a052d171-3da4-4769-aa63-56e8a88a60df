// ===== SLIDE ANIMATIONS AND VISUAL EFFECTS =====

// Animation State
const animationState = {
    currentAnimations: [],
    animationSpeed: 'normal',
    reducedMotion: false
};

// Animation Speed Settings
const animationSpeeds = {
    slow: 1.5,
    normal: 1,
    fast: 0.5
};

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeAnimations();
    checkReducedMotionPreference();
});

function initializeAnimations() {
    console.log('Slide Animations - Initializing...');
    
    // Check for reduced motion preference
    checkReducedMotionPreference();
    
    // Setup animation observers
    setupAnimationObservers();
    
    console.log('Slide Animations initialized');
}

function checkReducedMotionPreference() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    animationState.reducedMotion = prefersReducedMotion;
    
    if (prefersReducedMotion) {
        console.log('Reduced motion preference detected');
        disableAnimations();
    }
}

// ===== ANIMATION CONTROL =====
function setAnimationSpeed(speed) {
    animationState.animationSpeed = speed;
    const speedMultiplier = animationSpeeds[speed] || 1;
    
    // Update CSS custom property for animation speed
    document.documentElement.style.setProperty('--animation-speed', speedMultiplier);
}

function enableAnimations() {
    document.body.classList.remove('no-animations');
    animationState.reducedMotion = false;
}

function disableAnimations() {
    document.body.classList.add('no-animations');
    animationState.reducedMotion = true;
}

// ===== SLIDE TRANSITION ANIMATIONS =====
function animateSlideTransition(direction = 'next') {
    if (animationState.reducedMotion) return Promise.resolve();
    
    const slideContent = document.getElementById('slideContent');
    if (!slideContent) return Promise.resolve();
    
    return new Promise((resolve) => {
        const animationClass = direction === 'next' ? 'slide-exit-right' : 'slide-exit-left';
        slideContent.classList.add(animationClass);
        
        setTimeout(() => {
            slideContent.classList.remove(animationClass);
            resolve();
        }, 300);
    });
}

function animateSlideEntry(direction = 'next') {
    if (animationState.reducedMotion) return Promise.resolve();
    
    const slideContent = document.getElementById('slideContent');
    if (!slideContent) return Promise.resolve();
    
    return new Promise((resolve) => {
        const animationClass = direction === 'next' ? 'slide-enter-right' : 'slide-enter-left';
        slideContent.classList.add(animationClass);
        
        setTimeout(() => {
            slideContent.classList.remove(animationClass);
            resolve();
        }, 300);
    });
}

// ===== CONTENT ANIMATIONS =====
function animateContentElements(slideElement) {
    if (animationState.reducedMotion) return;
    
    const elements = slideElement.querySelectorAll('h1, h2, h3, p, ul, .visual-container');
    
    elements.forEach((element, index) => {
        const delay = index * 200; // 200ms delay between elements
        
        setTimeout(() => {
            element.classList.add('animate-in');
        }, delay);
    });
}

function animateListItems(listElement) {
    if (animationState.reducedMotion) return;
    
    const items = listElement.querySelectorAll('li');
    
    items.forEach((item, index) => {
        const delay = index * 150; // 150ms delay between list items
        
        setTimeout(() => {
            item.classList.add('fade-in');
        }, delay);
    });
}

// ===== VISUAL ELEMENT ANIMATIONS =====
function createHeartbeatAnimation() {
    return `
        <div class="heartbeat-container">
            <div class="heart-icon heartbeat">
                <i class="fas fa-heartbeat"></i>
            </div>
            <div class="heartbeat-line">
                <div class="pulse-wave"></div>
            </div>
            <div class="heart-rate-display">
                <span class="rate-value" id="heartRateValue">75</span>
                <span class="rate-unit">bpm</span>
            </div>
        </div>
    `;
}

function createECGAnimation() {
    return `
        <div class="ecg-container">
            <div class="ecg-monitor">
                <div class="ecg-grid"></div>
                <svg class="ecg-waveform" viewBox="0 0 400 100">
                    <path class="ecg-path" d="M0,50 L50,50 L55,30 L60,70 L65,20 L70,50 L400,50" 
                          stroke="#2ecc71" stroke-width="2" fill="none"/>
                    <circle class="ecg-dot" cx="0" cy="50" r="3" fill="#2ecc71">
                        <animateMotion dur="2s" repeatCount="indefinite">
                            <mpath href="#ecg-path"/>
                        </animateMotion>
                    </circle>
                </svg>
            </div>
            <div class="ecg-readings">
                <div class="reading">
                    <span class="label">HR:</span>
                    <span class="value">75 bpm</span>
                </div>
                <div class="reading">
                    <span class="label">Rhythm:</span>
                    <span class="value">Sinus</span>
                </div>
            </div>
        </div>
    `;
}

function createBloodPressureAnimation() {
    return `
        <div class="bp-container">
            <div class="bp-gauge">
                <div class="gauge-face">
                    <div class="gauge-needle" id="bpNeedle"></div>
                    <div class="gauge-center"></div>
                </div>
                <div class="gauge-labels">
                    <span class="label low">60</span>
                    <span class="label normal">120</span>
                    <span class="label high">180</span>
                </div>
            </div>
            <div class="bp-display">
                <div class="systolic">
                    <span class="value" id="systolicValue">120</span>
                    <span class="unit">mmHg</span>
                </div>
                <div class="separator">/</div>
                <div class="diastolic">
                    <span class="value" id="diastolicValue">80</span>
                    <span class="unit">mmHg</span>
                </div>
            </div>
            <div class="bp-status">
                <span class="status normal">Normal</span>
            </div>
        </div>
    `;
}

function createBreathingAnimation() {
    return `
        <div class="breathing-container">
            <div class="lung-diagram">
                <div class="lung left-lung breathing">
                    <div class="lung-detail"></div>
                </div>
                <div class="lung right-lung breathing">
                    <div class="lung-detail"></div>
                </div>
                <div class="trachea"></div>
            </div>
            <div class="breathing-cycle">
                <div class="cycle-indicator">
                    <div class="inspiration active">Inspiration</div>
                    <div class="expiration">Expiration</div>
                </div>
                <div class="rate-display">
                    <span class="value">16</span>
                    <span class="unit">breaths/min</span>
                </div>
            </div>
        </div>
    `;
}

function createGasFlowAnimation() {
    return `
        <div class="gas-flow-container">
            <div class="flow-diagram">
                <div class="gas-source">
                    <div class="tank oxygen">O₂</div>
                    <div class="tank nitrous">N₂O</div>
                    <div class="tank air">Air</div>
                </div>
                <div class="flow-paths">
                    <div class="flow-line oxygen-line">
                        <div class="flow-particles oxygen-particles">
                            <div class="particle"></div>
                            <div class="particle"></div>
                            <div class="particle"></div>
                        </div>
                    </div>
                    <div class="flow-line nitrous-line">
                        <div class="flow-particles nitrous-particles">
                            <div class="particle"></div>
                            <div class="particle"></div>
                        </div>
                    </div>
                </div>
                <div class="mixing-chamber">
                    <div class="chamber-content">
                        <div class="mixed-gas"></div>
                    </div>
                </div>
            </div>
            <div class="flow-controls">
                <div class="flowmeter oxygen-flowmeter">
                    <div class="flowmeter-tube">
                        <div class="flowmeter-ball" style="bottom: 30%;"></div>
                    </div>
                    <label>O₂: 2 L/min</label>
                </div>
                <div class="flowmeter nitrous-flowmeter">
                    <div class="flowmeter-tube">
                        <div class="flowmeter-ball" style="bottom: 20%;"></div>
                    </div>
                    <label>N₂O: 1 L/min</label>
                </div>
            </div>
        </div>
    `;
}

function createFlowmeterAnimation() {
    return `
        <div class="flowmeter-container">
            <div class="flowmeter-assembly">
                <div class="flowmeter-tube">
                    <div class="tube-markings">
                        <div class="marking" data-value="0">0</div>
                        <div class="marking" data-value="1">1</div>
                        <div class="marking" data-value="2">2</div>
                        <div class="marking" data-value="3">3</div>
                        <div class="marking" data-value="4">4</div>
                        <div class="marking" data-value="5">5</div>
                    </div>
                    <div class="flowmeter-ball animated-ball"></div>
                    <div class="gas-flow-stream">
                        <div class="flow-bubble"></div>
                        <div class="flow-bubble"></div>
                        <div class="flow-bubble"></div>
                    </div>
                </div>
                <div class="flow-control">
                    <div class="control-knob"></div>
                    <label>Flow Control</label>
                </div>
            </div>
            <div class="flow-reading">
                <span class="value" id="flowValue">2.5</span>
                <span class="unit">L/min</span>
            </div>
        </div>
    `;
}

// ===== INTERACTIVE ANIMATIONS =====
function animateVitalSignChange(vitalType, newValue, isAbnormal = false) {
    const element = document.getElementById(`${vitalType}Value`);
    if (!element) return;
    
    // Add change animation
    element.classList.add('value-changing');
    
    setTimeout(() => {
        element.textContent = newValue;
        element.classList.remove('value-changing');
        
        if (isAbnormal) {
            element.classList.add('abnormal-value');
        } else {
            element.classList.remove('abnormal-value');
        }
    }, 300);
}

function animateAlarmState(isActive) {
    const alarmElements = document.querySelectorAll('.alarm-indicator');
    
    alarmElements.forEach(element => {
        if (isActive) {
            element.classList.add('alarm-active');
        } else {
            element.classList.remove('alarm-active');
        }
    });
}

function animateProgressBar(targetPercentage, duration = 1000) {
    const progressBar = document.querySelector('.progress-fill');
    if (!progressBar) return;
    
    const startPercentage = parseFloat(progressBar.style.width) || 0;
    const difference = targetPercentage - startPercentage;
    const startTime = performance.now();
    
    function updateProgress(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentPercentage = startPercentage + (difference * progress);
        progressBar.style.width = `${currentPercentage}%`;
        
        if (progress < 1) {
            requestAnimationFrame(updateProgress);
        }
    }
    
    requestAnimationFrame(updateProgress);
}

// ===== TYPEWRITER EFFECT =====
function createTypewriterEffect(element, text, speed = 50) {
    if (animationState.reducedMotion) {
        element.textContent = text;
        return Promise.resolve();
    }
    
    return new Promise((resolve) => {
        element.textContent = '';
        element.classList.add('typewriter');
        
        let index = 0;
        const timer = setInterval(() => {
            element.textContent += text[index];
            index++;
            
            if (index >= text.length) {
                clearInterval(timer);
                element.classList.remove('typewriter');
                resolve();
            }
        }, speed);
    });
}

// ===== HIGHLIGHT ANIMATION =====
function animateHighlight(element, color = '#f1c40f') {
    if (animationState.reducedMotion) return;
    
    element.style.setProperty('--highlight-color', color);
    element.classList.add('highlight-animate');
    
    setTimeout(() => {
        element.classList.remove('highlight-animate');
    }, 1000);
}

// ===== PARTICLE EFFECTS =====
function createParticleEffect(container, particleCount = 20) {
    if (animationState.reducedMotion) return;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle-effect';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 2 + 's';
        particle.style.animationDuration = (2 + Math.random() * 3) + 's';
        
        container.appendChild(particle);
        
        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 5000);
    }
}

// ===== ANIMATION OBSERVERS =====
function setupAnimationObservers() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                
                if (element.classList.contains('animate-on-scroll')) {
                    element.classList.add('animated');
                }
                
                if (element.classList.contains('count-up')) {
                    animateCountUp(element);
                }
            }
        });
    }, {
        threshold: 0.5
    });
    
    // Observe elements that should animate on scroll
    document.querySelectorAll('.animate-on-scroll, .count-up').forEach(el => {
        observer.observe(el);
    });
}

function animateCountUp(element) {
    const target = parseInt(element.dataset.target) || 0;
    const duration = parseInt(element.dataset.duration) || 2000;
    const startTime = performance.now();
    
    function updateCount(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = Math.floor(target * progress);
        element.textContent = currentValue;
        
        if (progress < 1) {
            requestAnimationFrame(updateCount);
        }
    }
    
    requestAnimationFrame(updateCount);
}

// ===== CLEANUP FUNCTIONS =====
function clearAllAnimations() {
    animationState.currentAnimations.forEach(animation => {
        if (animation.cancel) {
            animation.cancel();
        }
    });
    animationState.currentAnimations = [];
}

function pauseAllAnimations() {
    document.querySelectorAll('.animated, .heartbeat, .breathing, .pulse').forEach(element => {
        element.style.animationPlayState = 'paused';
    });
}

function resumeAllAnimations() {
    document.querySelectorAll('.animated, .heartbeat, .breathing, .pulse').forEach(element => {
        element.style.animationPlayState = 'running';
    });
}

// ===== EXPORT FUNCTIONS =====
window.setAnimationSpeed = setAnimationSpeed;
window.enableAnimations = enableAnimations;
window.disableAnimations = disableAnimations;
window.animateSlideTransition = animateSlideTransition;
window.animateSlideEntry = animateSlideEntry;
window.animateContentElements = animateContentElements;
window.createHeartbeatAnimation = createHeartbeatAnimation;
window.createECGAnimation = createECGAnimation;
window.createBloodPressureAnimation = createBloodPressureAnimation;
window.createBreathingAnimation = createBreathingAnimation;
window.createGasFlowAnimation = createGasFlowAnimation;
window.createFlowmeterAnimation = createFlowmeterAnimation;
window.animateVitalSignChange = animateVitalSignChange;
window.animateAlarmState = animateAlarmState;
window.animateProgressBar = animateProgressBar;
window.createTypewriterEffect = createTypewriterEffect;
window.animateHighlight = animateHighlight;
window.createParticleEffect = createParticleEffect;
window.clearAllAnimations = clearAllAnimations;
window.pauseAllAnimations = pauseAllAnimations;
window.resumeAllAnimations = resumeAllAnimations;
