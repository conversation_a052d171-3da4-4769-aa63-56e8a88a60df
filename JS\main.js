// ===== MAIN APPLICATION JAVASCRIPT =====

// Global Variables
let currentUser = {
    id: 'user_001',
    name: 'Medical Student',
    progress: {
        'patient-monitoring': 0,
        'anesthesia-machine': 0,
        'ventilator-systems': 0
    },
    completedSimulations: [],
    assessmentScores: {}
};

// Application State
const appState = {
    currentModule: null,
    isLoading: false,
    modalOpen: false
};

// DOM Elements
const loadingOverlay = document.getElementById('loading-overlay');
const navLinks = document.querySelectorAll('.nav-link');

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    loadUserProgress();
    updateProgressBars();
});

// Initialize Application
function initializeApp() {
    console.log('Virtual Medical Simulation LMS - Initializing...');
    
    // Check for saved user data
    const savedUser = localStorage.getItem('vms_user_data');
    if (savedUser) {
        currentUser = { ...currentUser, ...JSON.parse(savedUser) };
    }
    
    // Initialize navigation
    updateActiveNavigation();
    
    // Setup smooth scrolling
    setupSmoothScrolling();
    
    console.log('Application initialized successfully');
}

// Setup Event Listeners
function setupEventListeners() {
    // Navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
    
    // Window events
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
    
    // Keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
    
    // Module cards hover effects
    const moduleCards = document.querySelectorAll('.module-card');
    moduleCards.forEach(card => {
        card.addEventListener('mouseenter', handleModuleCardHover);
        card.addEventListener('mouseleave', handleModuleCardLeave);
    });
}

// ===== NAVIGATION FUNCTIONS =====
function handleNavigation(event) {
    event.preventDefault();
    const targetId = event.target.getAttribute('href');
    
    if (targetId && targetId.startsWith('#')) {
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
            smoothScrollTo(targetElement);
            updateActiveNavigation(targetId);
        }
    }
}

function updateActiveNavigation(activeId = null) {
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (activeId && link.getAttribute('href') === activeId) {
            link.classList.add('active');
        }
    });
    
    // If no activeId provided, determine from scroll position
    if (!activeId) {
        const sections = document.querySelectorAll('section[id]');
        const scrollPosition = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = '#' + section.id;
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                const correspondingLink = document.querySelector(`a[href="${sectionId}"]`);
                if (correspondingLink) {
                    navLinks.forEach(link => link.classList.remove('active'));
                    correspondingLink.classList.add('active');
                }
            }
        });
    }
}

function smoothScrollTo(element) {
    const headerHeight = document.querySelector('.main-header').offsetHeight;
    const targetPosition = element.offsetTop - headerHeight - 20;
    
    window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });
}

function setupSmoothScrolling() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                smoothScrollTo(target);
            }
        });
    });
}

function scrollToModules() {
    const modulesSection = document.getElementById('modules');
    if (modulesSection) {
        smoothScrollTo(modulesSection);
    }
}

// ===== MODULE FUNCTIONS =====
function openModule(moduleId) {
    console.log(`Opening module: ${moduleId}`);
    
    showLoading('Loading Module...');
    
    // Simulate loading time
    setTimeout(() => {
        hideLoading();
        
        // Update current module
        appState.currentModule = moduleId;
        
        // Navigate to module content
        switch(moduleId) {
            case 'patient-monitoring':
                window.location.href = 'patient-monitoring.html';
                break;
            case 'anesthesia-machine':
                window.location.href = 'anesthesia-machine.html';
                break;
            case 'ventilator-systems':
                window.location.href = 'ventilator-systems.html';
                break;
            default:
                console.error('Unknown module:', moduleId);
        }
    }, 1500);
}

function viewModuleDetails(moduleId) {
    const moduleData = getModuleData(moduleId);
    if (moduleData) {
        showModuleModal(moduleData);
    }
}

function getModuleData(moduleId) {
    const modules = {
        'patient-monitoring': {
            id: 'patient-monitoring',
            title: 'Patient Monitoring & Vital Signs',
            description: 'Comprehensive training on patient monitoring systems, vital signs interpretation, and emergency response protocols.',
            level: 'Beginner to Advanced',
            duration: '4-6 hours',
            simulations: 5,
            assessments: 25,
            objectives: [
                'Understand fundamental vital signs and their normal ranges',
                'Interpret patient monitor displays and alarms',
                'Recognize emergency situations and appropriate responses',
                'Master different monitoring modalities and their applications',
                'Develop skills in patient assessment and documentation'
            ],
            prerequisites: 'Basic medical knowledge',
            certification: 'Patient Monitoring Specialist Certificate'
        },
        'anesthesia-machine': {
            id: 'anesthesia-machine',
            title: 'Anesthesia Machine Systems',
            description: 'In-depth exploration of anesthesia machine components, gas flow systems, safety protocols, and operational procedures.',
            level: 'Intermediate to Advanced',
            duration: '6-8 hours',
            simulations: 8,
            assessments: 40,
            objectives: [
                'Master the SPDD (Supply, Processing, Delivery, Disposal) model',
                'Understand gas flow dynamics and pressure systems',
                'Identify and operate all machine components safely',
                'Perform pre-use checks and troubleshooting',
                'Implement safety protocols and emergency procedures'
            ],
            prerequisites: 'Basic anesthesia knowledge, Patient Monitoring module recommended',
            certification: 'Anesthesia Equipment Specialist Certificate'
        },
        'ventilator-systems': {
            id: 'ventilator-systems',
            title: 'Ventilator Systems & Respiratory Care',
            description: 'Comprehensive training on mechanical ventilation, breathing mechanics, and respiratory support strategies.',
            level: 'Intermediate',
            duration: '5-7 hours',
            simulations: 6,
            assessments: 30,
            objectives: [
                'Understand mechanical ventilation principles',
                'Master ventilator settings and parameter calculations',
                'Recognize different ventilation modes and their applications',
                'Develop skills in respiratory assessment and monitoring',
                'Implement ventilator weaning and troubleshooting strategies'
            ],
            prerequisites: 'Basic respiratory physiology, Patient Monitoring module recommended',
            certification: 'Respiratory Care Specialist Certificate'
        }
    };
    
    return modules[moduleId] || null;
}

// ===== SIMULATION FUNCTIONS =====
function openSimulation(simulationUrl) {
    console.log(`Opening simulation: ${simulationUrl}`);
    
    showLoading('Loading Simulation...');
    
    // Track simulation access
    trackSimulationAccess(simulationUrl);
    
    setTimeout(() => {
        hideLoading();
        window.open(simulationUrl, '_blank');
    }, 1000);
}

function trackSimulationAccess(simulationUrl) {
    const simulationName = simulationUrl.split('/').pop().replace('.html', '');
    
    if (!currentUser.completedSimulations.includes(simulationName)) {
        currentUser.completedSimulations.push(simulationName);
        saveUserData();
        updateProgressBars();
    }
}

// ===== PROGRESS TRACKING =====
function updateProgressBars() {
    const progressBars = document.querySelectorAll('.progress-fill');
    const progressTexts = document.querySelectorAll('.progress-text');
    
    progressBars.forEach((bar, index) => {
        const moduleId = Object.keys(currentUser.progress)[index];
        if (moduleId) {
            const progress = currentUser.progress[moduleId];
            bar.style.width = `${progress}%`;
            if (progressTexts[index]) {
                progressTexts[index].textContent = `${progress}% Complete`;
            }
        }
    });
}

function updateModuleProgress(moduleId, progress) {
    currentUser.progress[moduleId] = Math.max(currentUser.progress[moduleId], progress);
    saveUserData();
    updateProgressBars();
}

// ===== USER DATA MANAGEMENT =====
function saveUserData() {
    localStorage.setItem('vms_user_data', JSON.stringify(currentUser));
}

function loadUserProgress() {
    const savedData = localStorage.getItem('vms_user_data');
    if (savedData) {
        const userData = JSON.parse(savedData);
        currentUser = { ...currentUser, ...userData };
    }
}

// ===== UI UTILITY FUNCTIONS =====
function showLoading(message = 'Loading...') {
    if (loadingOverlay) {
        const loadingText = loadingOverlay.querySelector('p');
        if (loadingText) {
            loadingText.textContent = message;
        }
        loadingOverlay.style.display = 'flex';
        appState.isLoading = true;
    }
}

function hideLoading() {
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
        appState.isLoading = false;
    }
}

// ===== EVENT HANDLERS =====
function handleScroll() {
    updateActiveNavigation();
    
    // Add header shadow on scroll
    const header = document.querySelector('.main-header');
    if (header) {
        if (window.scrollY > 50) {
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
        } else {
            header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        }
    }
}

function handleResize() {
    // Handle responsive adjustments if needed
    console.log('Window resized');
}

function handleKeyboardNavigation(event) {
    // ESC key to close modals
    if (event.key === 'Escape' && appState.modalOpen) {
        closeModal();
    }
    
    // Arrow keys for navigation (if needed)
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        // Implement keyboard navigation if needed
    }
}

function handleModuleCardHover(event) {
    const card = event.currentTarget;
    card.style.transform = 'translateY(-10px) scale(1.02)';
}

function handleModuleCardLeave(event) {
    const card = event.currentTarget;
    card.style.transform = 'translateY(0) scale(1)';
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// ===== EXPORT FUNCTIONS FOR GLOBAL ACCESS =====
window.scrollToModules = scrollToModules;
window.openModule = openModule;
window.viewModuleDetails = viewModuleDetails;
window.openSimulation = openSimulation;
window.updateModuleProgress = updateModuleProgress;
