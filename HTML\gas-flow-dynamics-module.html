<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gas Flow Dynamics Module - Virtual Medical Simulation LMS</title>
    <link rel="stylesheet" href="../CSS/main.css">
    <link rel="stylesheet" href="../CSS/enhanced-modules.css">
    <link rel="stylesheet" href="../CSS/diagrams.css">
    <link rel="stylesheet" href="../CSS/gas-flow-module.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Navigation Header -->
    <header class="module-header">
        <div class="header-content">
            <div class="module-nav">
                <a href="../HTML/lectures.html" class="nav-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Lectures
                </a>
                <div class="module-title">
                    <h1>Gas Flow Dynamics Module</h1>
                    <p>Fluid Mechanics & Anesthetic Gas Delivery Systems</p>
                </div>
                <div class="header-controls">
                    <button class="btn-control" id="themeToggle" onclick="toggleTheme()">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="btn-control" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Module Overview -->
    <section class="module-overview">
        <div class="overview-content">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-wind animated-pulse"></i>
                    </div>
                    <h3>Fluid Mechanics</h3>
                    <p>Master laminar and turbulent flow, Poiseuille's law, and Reynolds number</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 90%"></div>
                    </div>
                    <span class="progress-text">90% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-tachometer-alt animated-gauge"></i>
                    </div>
                    <h3>Flowmeter Principles</h3>
                    <p>Variable orifice flowmeters, gas-specific calibration, and accuracy factors</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%"></div>
                    </div>
                    <span class="progress-text">88% Complete</span>
                </div>
                
                <div class="overview-card">
                    <div class="card-icon">
                        <i class="fas fa-shield-alt animated-heartbeat"></i>
                    </div>
                    <h3>Safety Systems</h3>
                    <p>Hypoxic guard, proportioning systems, and fail-safe mechanisms</p>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%"></div>
                    </div>
                    <span class="progress-text">85% Complete</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Gas Flow Simulator -->
    <section class="learning-tools">
        <div class="tools-container">
            <h2 class="section-title">Virtual Gas Flow Laboratory</h2>
            
            <!-- Flow Dynamics Simulator -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Interactive Flow Dynamics Simulator</h3>
                    <p>Real-time visualization of gas flow principles and flowmeter operation</p>
                </div>
                
                <div class="flow-dynamics-system">
                    <!-- Flow Simulator Display -->
                    <div class="flow-simulator-display">
                        <div class="simulator-screen">
                            <div class="screen-header">
                                <h4>Gas Flow Dynamics Laboratory</h4>
                                <div class="flow-status">
                                    <span class="status-light active" id="flowPowerStatus"></span>
                                    <span class="status-text">Active</span>
                                    <div class="flow-mode">
                                        <span class="mode-label">Flow Type:</span>
                                        <span class="mode-value" id="currentFlowType">Laminar</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Virtual Flowmeter Bank -->
                            <div class="flowmeter-bank-section">
                                <h5>Virtual Flowmeter Bank</h5>
                                <div class="flowmeter-array">
                                    <!-- Oxygen Flowmeter -->
                                    <div class="virtual-flowmeter oxygen" data-gas="oxygen">
                                        <div class="flowmeter-tube">
                                            <div class="tube-scale">
                                                <span class="scale-mark">15</span>
                                                <span class="scale-mark">10</span>
                                                <span class="scale-mark">5</span>
                                                <span class="scale-mark">2</span>
                                                <span class="scale-mark">1</span>
                                                <span class="scale-mark">0.5</span>
                                                <span class="scale-mark">0</span>
                                            </div>
                                            <div class="flow-indicator" id="oxygenIndicator" style="bottom: 20%"></div>
                                            <div class="tube-taper"></div>
                                        </div>
                                        <div class="flowmeter-controls">
                                            <div class="needle-valve" onclick="adjustFlow('oxygen', 'up')">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                            <div class="needle-valve" onclick="adjustFlow('oxygen', 'down')">
                                                <i class="fas fa-minus"></i>
                                            </div>
                                        </div>
                                        <div class="gas-label">O₂</div>
                                        <div class="flow-reading" id="oxygenFlow">3.0 L/min</div>
                                    </div>
                                    
                                    <!-- Nitrous Oxide Flowmeter -->
                                    <div class="virtual-flowmeter nitrous" data-gas="nitrous">
                                        <div class="flowmeter-tube">
                                            <div class="tube-scale">
                                                <span class="scale-mark">15</span>
                                                <span class="scale-mark">10</span>
                                                <span class="scale-mark">5</span>
                                                <span class="scale-mark">2</span>
                                                <span class="scale-mark">1</span>
                                                <span class="scale-mark">0.5</span>
                                                <span class="scale-mark">0</span>
                                            </div>
                                            <div class="flow-indicator" id="nitrousIndicator" style="bottom: 15%"></div>
                                            <div class="tube-taper"></div>
                                        </div>
                                        <div class="flowmeter-controls">
                                            <div class="needle-valve" onclick="adjustFlow('nitrous', 'up')">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                            <div class="needle-valve" onclick="adjustFlow('nitrous', 'down')">
                                                <i class="fas fa-minus"></i>
                                            </div>
                                        </div>
                                        <div class="gas-label">N₂O</div>
                                        <div class="flow-reading" id="nitrousFlow">2.0 L/min</div>
                                    </div>
                                    
                                    <!-- Air Flowmeter -->
                                    <div class="virtual-flowmeter air" data-gas="air">
                                        <div class="flowmeter-tube">
                                            <div class="tube-scale">
                                                <span class="scale-mark">15</span>
                                                <span class="scale-mark">10</span>
                                                <span class="scale-mark">5</span>
                                                <span class="scale-mark">2</span>
                                                <span class="scale-mark">1</span>
                                                <span class="scale-mark">0.5</span>
                                                <span class="scale-mark">0</span>
                                            </div>
                                            <div class="flow-indicator" id="airIndicator" style="bottom: 10%"></div>
                                            <div class="tube-taper"></div>
                                        </div>
                                        <div class="flowmeter-controls">
                                            <div class="needle-valve" onclick="adjustFlow('air', 'up')">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                            <div class="needle-valve" onclick="adjustFlow('air', 'down')">
                                                <i class="fas fa-minus"></i>
                                            </div>
                                        </div>
                                        <div class="gas-label">Air</div>
                                        <div class="flow-reading" id="airFlow">1.0 L/min</div>
                                    </div>
                                </div>
                                
                                <!-- Gas Mixture Analysis -->
                                <div class="mixture-analysis">
                                    <h6>Gas Mixture Composition</h6>
                                    <div class="composition-display">
                                        <div class="gas-percentage">
                                            <span class="gas-name">O₂:</span>
                                            <span class="percentage" id="oxygenPercentage">50%</span>
                                        </div>
                                        <div class="gas-percentage">
                                            <span class="gas-name">N₂O:</span>
                                            <span class="percentage" id="nitrousPercentage">33%</span>
                                        </div>
                                        <div class="gas-percentage">
                                            <span class="gas-name">Air:</span>
                                            <span class="percentage" id="airPercentage">17%</span>
                                        </div>
                                        <div class="total-flow">
                                            <span class="flow-name">Total:</span>
                                            <span class="flow-value" id="totalFlow">6.0 L/min</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Safety Indicators -->
                                    <div class="safety-indicators">
                                        <div class="safety-item" id="hypoxicGuard">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>Hypoxic Guard: Active</span>
                                        </div>
                                        <div class="safety-item" id="proportioningSystem">
                                            <i class="fas fa-balance-scale"></i>
                                            <span>Link-25: Engaged</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Flow Visualization -->
                            <div class="flow-visualization-section">
                                <h5>Flow Pattern Visualization</h5>
                                <div class="flow-tube-display">
                                    <canvas id="flowVisualization" width="600" height="200"></canvas>
                                    <div class="flow-parameters">
                                        <div class="parameter-item">
                                            <span class="param-label">Reynolds Number:</span>
                                            <span class="param-value" id="reynoldsNumber">1200</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">Flow Type:</span>
                                            <span class="param-value" id="flowType">Laminar</span>
                                        </div>
                                        <div class="parameter-item">
                                            <span class="param-label">Pressure Drop:</span>
                                            <span class="param-value" id="pressureDrop">2.5 cmH₂O</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Poiseuille's Law Calculator -->
                                <div class="poiseuille-calculator">
                                    <h6>Poiseuille's Law Calculator</h6>
                                    <div class="calculator-inputs">
                                        <div class="input-group">
                                            <label>Tube Radius (mm)</label>
                                            <input type="range" min="1" max="10" value="5" id="tubeRadius" onchange="calculatePoiseuille()">
                                            <span id="radiusValue">5 mm</span>
                                        </div>
                                        <div class="input-group">
                                            <label>Tube Length (cm)</label>
                                            <input type="range" min="10" max="100" value="50" id="tubeLength" onchange="calculatePoiseuille()">
                                            <span id="lengthValue">50 cm</span>
                                        </div>
                                        <div class="input-group">
                                            <label>Pressure Gradient (cmH₂O)</label>
                                            <input type="range" min="1" max="20" value="10" id="pressureGradient" onchange="calculatePoiseuille()">
                                            <span id="gradientValue">10 cmH₂O</span>
                                        </div>
                                    </div>
                                    <div class="calculator-result">
                                        <span class="result-label">Calculated Flow:</span>
                                        <span class="result-value" id="calculatedFlow">8.2 L/min</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Flow Controls -->
                        <div class="flow-controls">
                            <div class="control-section">
                                <h5>Flow Scenarios</h5>
                                <div class="scenario-controls">
                                    <button class="scenario-btn" onclick="loadFlowScenario('normal')">
                                        <i class="fas fa-check-circle"></i>
                                        Normal Flow
                                    </button>
                                    <button class="scenario-btn" onclick="loadFlowScenario('turbulent')">
                                        <i class="fas fa-wave-square"></i>
                                        Turbulent Flow
                                    </button>
                                    <button class="scenario-btn" onclick="loadFlowScenario('obstruction')">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Obstruction
                                    </button>
                                    <button class="scenario-btn" onclick="loadFlowScenario('leak')">
                                        <i class="fas fa-tint"></i>
                                        System Leak
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>Safety Systems</h5>
                                <div class="safety-controls">
                                    <button class="safety-btn" onclick="toggleHypoxicGuard()">
                                        <i class="fas fa-shield-alt"></i>
                                        <span id="hypoxicGuardStatus">Hypoxic Guard: ON</span>
                                    </button>
                                    <button class="safety-btn" onclick="toggleProportioning()">
                                        <i class="fas fa-balance-scale"></i>
                                        <span id="proportioningStatus">Link-25: ON</span>
                                    </button>
                                    <button class="safety-btn" onclick="emergencyShutoff()">
                                        <i class="fas fa-stop"></i>
                                        Emergency Shutoff
                                    </button>
                                </div>
                            </div>
                            
                            <div class="control-section">
                                <h5>System Settings</h5>
                                <div class="settings-controls">
                                    <div class="setting-group">
                                        <label>Temperature (°C)</label>
                                        <input type="range" min="15" max="35" value="22" onchange="setTemperature(this.value)">
                                        <span id="temperatureValue">22°C</span>
                                    </div>
                                    
                                    <div class="setting-group">
                                        <label>Pressure (kPa)</label>
                                        <input type="range" min="90" max="110" value="101" onchange="setPressure(this.value)">
                                        <span id="pressureValue">101 kPa</span>
                                    </div>
                                    
                                    <div class="setting-group">
                                        <label>Humidity (%)</label>
                                        <input type="range" min="30" max="80" value="50" onchange="setHumidity(this.value)">
                                        <span id="humidityValue">50%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Flow Principles Diagram -->
            <div class="tool-section">
                <div class="tool-header">
                    <h3>Flow Principles & Physics</h3>
                    <p>Interactive visualization of fluid mechanics and gas flow principles</p>
                </div>
                
                <div class="diagram-container">
                    <div class="diagram-tabs">
                        <button class="tab-btn active" onclick="showFlowDiagram('laminar')">Laminar Flow</button>
                        <button class="tab-btn" onclick="showFlowDiagram('turbulent')">Turbulent Flow</button>
                        <button class="tab-btn" onclick="showFlowDiagram('flowmeter')">Flowmeter Design</button>
                    </div>
                    
                    <!-- Laminar Flow Diagram -->
                    <div id="laminarFlowDiagram" class="diagram-panel active">
                        <div class="flow-physics-visualization">
                            <div class="flow-tube-model">
                                <svg viewBox="0 0 600 300" class="flow-diagram">
                                    <!-- Tube outline -->
                                    <rect x="50" y="100" width="500" height="100" fill="none" stroke="#64748b" stroke-width="3" rx="10"/>
                                    
                                    <!-- Laminar flow lines -->
                                    <g class="laminar-flow-lines">
                                        <path d="M 60 120 Q 300 120 540 120" class="flow-line fast" stroke="#3b82f6" stroke-width="3" fill="none"/>
                                        <path d="M 60 135 Q 300 135 540 135" class="flow-line medium" stroke="#3b82f6" stroke-width="2" fill="none"/>
                                        <path d="M 60 150 Q 300 150 540 150" class="flow-line center" stroke="#3b82f6" stroke-width="4" fill="none"/>
                                        <path d="M 60 165 Q 300 165 540 165" class="flow-line medium" stroke="#3b82f6" stroke-width="2" fill="none"/>
                                        <path d="M 60 180 Q 300 180 540 180" class="flow-line slow" stroke="#3b82f6" stroke-width="3" fill="none"/>
                                    </g>
                                    
                                    <!-- Velocity profile -->
                                    <g class="velocity-profile">
                                        <path d="M 450 100 Q 500 150 450 200" fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" stroke-width="2"/>
                                        <text x="480" y="80" class="profile-label">Parabolic Velocity Profile</text>
                                    </g>
                                    
                                    <!-- Flow arrows -->
                                    <g class="flow-arrows">
                                        <path d="M 20 150 L 50 150" stroke="#10b981" stroke-width="3" marker-end="url(#arrowhead)"/>
                                        <path d="M 550 150 L 580 150" stroke="#10b981" stroke-width="3" marker-end="url(#arrowhead)"/>
                                    </g>
                                    
                                    <!-- Labels -->
                                    <text x="300" y="50" class="diagram-title">Laminar Flow in Circular Tube</text>
                                    <text x="20" y="140" class="flow-label">Inlet</text>
                                    <text x="560" y="140" class="flow-label">Outlet</text>
                                    
                                    <!-- Equations -->
                                    <text x="100" y="250" class="equation">Q = (πr⁴ΔP)/(8μL)</text>
                                    <text x="100" y="270" class="equation-label">Poiseuille's Law</text>
                                </svg>
                            </div>
                            
                            <div class="flow-characteristics">
                                <h6>Laminar Flow Characteristics</h6>
                                <ul class="characteristics-list">
                                    <li><strong>Reynolds Number:</strong> Re &lt; 2000</li>
                                    <li><strong>Flow Pattern:</strong> Smooth, parallel layers</li>
                                    <li><strong>Velocity Profile:</strong> Parabolic</li>
                                    <li><strong>Pressure Drop:</strong> Linear with flow rate</li>
                                    <li><strong>Resistance:</strong> Proportional to viscosity</li>
                                    <li><strong>Noise:</strong> Silent flow</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="reynolds-calculator">
                            <h6>Reynolds Number Calculator</h6>
                            <div class="reynolds-inputs">
                                <div class="input-item">
                                    <label>Velocity (m/s)</label>
                                    <input type="number" value="2.0" step="0.1" onchange="calculateReynolds()">
                                </div>
                                <div class="input-item">
                                    <label>Diameter (mm)</label>
                                    <input type="number" value="10" step="1" onchange="calculateReynolds()">
                                </div>
                                <div class="input-item">
                                    <label>Density (kg/m³)</label>
                                    <input type="number" value="1.2" step="0.1" onchange="calculateReynolds()">
                                </div>
                                <div class="input-item">
                                    <label>Viscosity (Pa·s)</label>
                                    <input type="number" value="0.000018" step="0.000001" onchange="calculateReynolds()">
                                </div>
                            </div>
                            <div class="reynolds-result">
                                <span class="result-label">Reynolds Number:</span>
                                <span class="result-value" id="reynoldsResult">1333</span>
                                <span class="flow-regime" id="flowRegime">Laminar</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Component Information Panel -->
                    <div class="component-info" id="flowComponentInfo">
                        <h4 id="flowComponentTitle">Gas Flow Dynamics</h4>
                        <p id="flowComponentDescription">Gas flow in medical systems follows fundamental fluid mechanics principles. Understanding laminar vs turbulent flow, pressure-flow relationships, and flowmeter operation is essential for safe anesthetic gas delivery.</p>
                        <div id="flowComponentSpecs"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comprehensive Flow Notes -->
    <section class="lecture-notes">
        <div class="notes-container">
            <h2 class="section-title">Gas Flow Dynamics Fundamentals</h2>
            
            <div class="notes-grid">
                <!-- Fluid Mechanics -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-wind"></i>
                        </div>
                        <h3>Fluid Mechanics Principles</h3>
                    </div>
                    <div class="note-content">
                        <h4>Flow Types</h4>
                        <ul>
                            <li><strong>Laminar Flow:</strong> Re &lt; 2000, smooth parallel layers</li>
                            <li><strong>Turbulent Flow:</strong> Re &gt; 4000, chaotic mixing</li>
                            <li><strong>Transitional:</strong> Re 2000-4000, unstable flow</li>
                        </ul>
                        
                        <h4>Poiseuille's Law</h4>
                        <ul>
                            <li><strong>Equation:</strong> Q = (πr⁴ΔP)/(8μL)</li>
                            <li><strong>Flow ∝ r⁴:</strong> Radius has greatest effect</li>
                            <li><strong>Flow ∝ ΔP:</strong> Linear pressure relationship</li>
                            <li><strong>Flow ∝ 1/L:</strong> Inverse length relationship</li>
                        </ul>
                        
                        <h4>Reynolds Number</h4>
                        <div class="reference-table">
                            <div class="table-row">
                                <span class="parameter">Formula</span>
                                <span class="range">Re = ρvd/μ</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Laminar</span>
                                <span class="range">Re &lt; 2000</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Transitional</span>
                                <span class="range">Re 2000-4000</span>
                            </div>
                            <div class="table-row">
                                <span class="parameter">Turbulent</span>
                                <span class="range">Re &gt; 4000</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Flowmeter Principles -->
                <div class="note-card">
                    <div class="note-header">
                        <div class="note-icon">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <h3>Flowmeter Operation</h3>
                    </div>
                    <div class="note-content">
                        <h4>Variable Orifice Design</h4>
                        <ul>
                            <li><strong>Tapered Tube:</strong> Increasing diameter upward</li>
                            <li><strong>Float/Bobbin:</strong> Flow indicator with constant ΔP</li>
                            <li><strong>Equilibrium:</strong> Weight = Pressure force</li>
                            <li><strong>Gas Specific:</strong> Calibrated for individual gases</li>
                        </ul>
                        
                        <h4>Safety Features</h4>
                        <ul>
                            <li><strong>Sequence:</strong> O₂ downstream of other gases</li>
                            <li><strong>Hypoxic Guard:</strong> Minimum 25% O₂</li>
                            <li><strong>Link-25:</strong> Mechanical O₂/N₂O proportioning</li>
                            <li><strong>Fail-Safe:</strong> Loss of O₂ pressure stops N₂O</li>
                        </ul>
                        
                        <h4>Accuracy Factors</h4>
                        <ul>
                            <li><strong>Temperature:</strong> Gas density changes</li>
                            <li><strong>Pressure:</strong> Atmospheric variations</li>
                            <li><strong>Humidity:</strong> Water vapor effects</li>
                            <li><strong>Back Pressure:</strong> Downstream resistance</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="../JS/enhanced-modules.js"></script>
    <script src="../JS/gas-flow-simulator.js"></script>
    <script src="../JS/flow-physics.js"></script>
    <script>
        // Initialize module
        document.addEventListener('DOMContentLoaded', function() {
            initializeGasFlowSimulator();
            initializeFlowPhysics();
            startFlowAnimations();
        });
    </script>
</body>
</html>
