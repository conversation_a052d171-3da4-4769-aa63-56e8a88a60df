// ===== LECTURE DATA REPOSITORY =====

// Lecture Database
const lectureDatabase = {
    'vital-signs-intro': {
        id: 'vital-signs-intro',
        title: 'Introduction to Vital Signs',
        subtitle: 'Fundamental Concepts of Patient Monitoring',
        description: 'Understanding the five essential vital signs and their clinical significance',
        duration: 15,
        category: 'patient-monitoring',
        difficulty: 'beginner',
        slides: [
            {
                type: 'title',
                title: 'Introduction to Vital Signs',
                subtitle: 'The Foundation of Patient Assessment',
                description: 'Understanding the five essential parameters that indicate life and health status',
                visual: {
                    type: 'heartbeat',
                    description: 'Animated heartbeat visualization'
                }
            },
            {
                type: 'content',
                title: 'What Are Vital Signs?',
                content: [
                    'Vital signs are measurable indicators of essential body functions',
                    'They provide critical information about a patient\'s physiological status',
                    'Changes in vital signs can indicate medical emergencies or treatment effectiveness',
                    {
                        type: 'highlight',
                        text: 'The five primary vital signs are: Heart Rate, Blood Pressure, Respiratory Rate, Temperature, and Oxygen Saturation'
                    }
                ]
            },
            {
                type: 'visual',
                title: 'Heart Rate (HR)',
                description: 'The number of heartbeats per minute, reflecting cardiac function and overall cardiovascular health.',
                visual: {
                    type: 'heartbeat',
                    description: 'Animated heart rate visualization'
                }
            },
            {
                type: 'content',
                title: 'Heart Rate - Normal Values',
                content: [
                    {
                        type: 'list',
                        items: [
                            'Adults (resting): 60-100 beats per minute',
                            'Children (1-10 years): 70-120 beats per minute',
                            'Infants (0-12 months): 100-160 beats per minute',
                            'Athletes may have resting rates as low as 40-60 bpm'
                        ]
                    },
                    'Factors affecting heart rate include age, fitness level, medications, stress, and medical conditions.'
                ]
            },
            {
                type: 'visual',
                title: 'Blood Pressure (BP)',
                description: 'The force exerted by blood against arterial walls, measured as systolic over diastolic pressure.',
                visual: {
                    type: 'blood-pressure',
                    description: 'Animated blood pressure gauge'
                }
            },
            {
                type: 'content',
                title: 'Blood Pressure Categories',
                content: [
                    {
                        type: 'list',
                        items: [
                            'Normal: Less than 120/80 mmHg',
                            'Elevated: 120-129 systolic, less than 80 diastolic',
                            'Stage 1 Hypertension: 130-139/80-89 mmHg',
                            'Stage 2 Hypertension: 140/90 mmHg or higher',
                            'Hypertensive Crisis: Higher than 180/120 mmHg'
                        ]
                    }
                ]
            },
            {
                type: 'visual',
                title: 'Respiratory Rate (RR)',
                description: 'The number of breaths taken per minute, indicating respiratory function and oxygen exchange.',
                visual: {
                    type: 'breathing',
                    description: 'Animated breathing cycle'
                }
            },
            {
                type: 'content',
                title: 'Respiratory Rate - Normal Ranges',
                content: [
                    {
                        type: 'list',
                        items: [
                            'Adults: 12-20 breaths per minute',
                            'Children (1-12 years): 20-30 breaths per minute',
                            'Infants (0-12 months): 30-60 breaths per minute'
                        ]
                    },
                    'Abnormal respiratory rates may indicate respiratory distress, metabolic disorders, or neurological conditions.'
                ]
            },
            {
                type: 'content',
                title: 'Body Temperature',
                content: [
                    'Core body temperature reflects metabolic activity and thermoregulation',
                    {
                        type: 'list',
                        items: [
                            'Normal range: 36.5-37.5°C (97.7-99.5°F)',
                            'Fever: Above 38°C (100.4°F)',
                            'Hypothermia: Below 35°C (95°F)',
                            'Hyperthermia: Above 40°C (104°F)'
                        ]
                    },
                    'Temperature can be measured orally, rectally, axillary, tympanic, or temporally.'
                ]
            },
            {
                type: 'content',
                title: 'Oxygen Saturation (SpO2)',
                content: [
                    'Percentage of oxygen-saturated hemoglobin in the blood',
                    {
                        type: 'list',
                        items: [
                            'Normal range: 95-100%',
                            'Mild hypoxemia: 90-94%',
                            'Moderate hypoxemia: 85-89%',
                            'Severe hypoxemia: Below 85%'
                        ]
                    },
                    {
                        type: 'highlight',
                        text: 'SpO2 below 90% requires immediate medical attention'
                    }
                ]
            },
            {
                type: 'interactive',
                title: 'Knowledge Check',
                description: 'Test your understanding of normal vital sign ranges',
                interactive: {
                    type: 'quiz',
                    question: 'What is the normal resting heart rate range for a healthy adult?',
                    options: [
                        '40-60 bpm',
                        '60-100 bpm',
                        '100-120 bpm',
                        '120-140 bpm'
                    ],
                    correct: 1,
                    explanation: 'The normal resting heart rate for a healthy adult is 60-100 beats per minute. Athletes may have lower rates due to increased cardiac efficiency.'
                }
            },
            {
                type: 'summary',
                title: 'Key Takeaways',
                keyPoints: [
                    'Vital signs are essential indicators of physiological function',
                    'Normal ranges vary by age, fitness level, and individual factors',
                    'Trending changes are often more significant than single measurements',
                    'Always consider the clinical context when interpreting vital signs',
                    'Abnormal vital signs may indicate the need for immediate intervention'
                ],
                nextSteps: 'Next, we will explore ECG interpretation and cardiac rhythm analysis in detail.'
            }
        ]
    },
    
    'ecg-interpretation': {
        id: 'ecg-interpretation',
        title: 'ECG Interpretation Basics',
        subtitle: 'Understanding Cardiac Rhythms',
        description: 'Fundamental principles of electrocardiogram analysis and rhythm recognition',
        duration: 25,
        category: 'patient-monitoring',
        difficulty: 'intermediate',
        slides: [
            {
                type: 'title',
                title: 'ECG Interpretation Basics',
                subtitle: 'Reading the Heart\'s Electrical Activity',
                description: 'Understanding electrocardiogram patterns and their clinical significance',
                visual: {
                    type: 'ecg',
                    description: 'Animated ECG waveform'
                }
            },
            {
                type: 'content',
                title: 'What is an ECG?',
                content: [
                    'An electrocardiogram (ECG/EKG) records the electrical activity of the heart',
                    'It shows the heart\'s rhythm, rate, and electrical conduction patterns',
                    {
                        type: 'list',
                        items: [
                            'Non-invasive diagnostic tool',
                            'Provides real-time cardiac information',
                            'Essential for detecting arrhythmias',
                            'Helps diagnose heart attacks and other cardiac conditions'
                        ]
                    }
                ]
            },
            {
                type: 'visual',
                title: 'ECG Waveform Components',
                description: 'The ECG waveform consists of several distinct components, each representing different phases of cardiac electrical activity.',
                visual: {
                    type: 'ecg',
                    description: 'Detailed ECG waveform with labeled components'
                }
            },
            {
                type: 'content',
                title: 'ECG Wave Components',
                content: [
                    {
                        type: 'list',
                        items: [
                            'P Wave: Atrial depolarization (atrial contraction)',
                            'QRS Complex: Ventricular depolarization (ventricular contraction)',
                            'T Wave: Ventricular repolarization (ventricular relaxation)',
                            'PR Interval: Time from atrial to ventricular activation',
                            'QT Interval: Total ventricular electrical activity duration'
                        ]
                    }
                ]
            },
            {
                type: 'content',
                title: 'Normal ECG Values',
                content: [
                    {
                        type: 'list',
                        items: [
                            'Heart Rate: 60-100 beats per minute',
                            'PR Interval: 0.12-0.20 seconds (120-200 ms)',
                            'QRS Duration: Less than 0.12 seconds (120 ms)',
                            'QT Interval: 0.36-0.44 seconds (varies with heart rate)',
                            'Rhythm: Regular sinus rhythm'
                        ]
                    }
                ]
            }
        ]
    },
    
    'blood-pressure-physiology': {
        id: 'blood-pressure-physiology',
        title: 'Blood Pressure Physiology',
        subtitle: 'Cardiovascular Hemodynamics',
        description: 'Understanding the physiological mechanisms of blood pressure regulation',
        duration: 20,
        category: 'patient-monitoring',
        difficulty: 'intermediate',
        slides: [
            {
                type: 'title',
                title: 'Blood Pressure Physiology',
                subtitle: 'Understanding Cardiovascular Hemodynamics',
                description: 'The mechanisms that regulate blood pressure and maintain cardiovascular homeostasis',
                visual: {
                    type: 'blood-pressure',
                    description: 'Animated blood pressure visualization'
                }
            },
            {
                type: 'content',
                title: 'Blood Pressure Fundamentals',
                content: [
                    'Blood pressure is the force exerted by circulating blood on vessel walls',
                    'It is determined by cardiac output and peripheral vascular resistance',
                    {
                        type: 'highlight',
                        text: 'BP = Cardiac Output × Peripheral Vascular Resistance'
                    },
                    'Measured in millimeters of mercury (mmHg) as systolic/diastolic pressure'
                ]
            }
        ]
    },
    
    'anesthesia-machine-overview': {
        id: 'anesthesia-machine-overview',
        title: 'Anesthesia Machine Overview',
        subtitle: 'Complete System Understanding',
        description: 'Comprehensive overview of anesthesia machine components and the SPDD model',
        duration: 30,
        category: 'anesthesia-machine',
        difficulty: 'beginner',
        slides: [
            {
                type: 'title',
                title: 'Anesthesia Machine Overview',
                subtitle: 'Understanding the Complete System',
                description: 'A comprehensive guide to anesthesia machine components and operations',
                visual: {
                    type: 'gas-flow',
                    description: 'Animated gas flow visualization'
                }
            },
            {
                type: 'content',
                title: 'The SPDD Model',
                content: [
                    'The SPDD model provides a systematic approach to understanding anesthesia machines:',
                    {
                        type: 'list',
                        items: [
                            'Supply: Gas sources and delivery systems',
                            'Processing: Gas mixing and flow control',
                            'Delivery: Patient breathing circuits',
                            'Disposal: Scavenging and waste gas removal'
                        ]
                    }
                ]
            }
        ]
    },
    
    'gas-flow-dynamics': {
        id: 'gas-flow-dynamics',
        title: 'Gas Flow Dynamics',
        subtitle: 'Understanding Flow Principles',
        description: 'Detailed analysis of gas flow principles and flowmeter operations',
        duration: 35,
        category: 'anesthesia-machine',
        difficulty: 'advanced',
        slides: [
            {
                type: 'title',
                title: 'Gas Flow Dynamics',
                subtitle: 'Principles of Flow and Pressure',
                description: 'Understanding the physics of gas flow in anesthesia delivery systems',
                visual: {
                    type: 'flowmeter',
                    description: 'Animated flowmeter operation'
                }
            }
        ]
    },
    
    'mechanical-ventilation': {
        id: 'mechanical-ventilation',
        title: 'Mechanical Ventilation Principles',
        subtitle: 'Respiratory Support Fundamentals',
        description: 'Understanding the principles and applications of mechanical ventilation',
        duration: 28,
        category: 'ventilator-systems',
        difficulty: 'intermediate',
        slides: [
            {
                type: 'title',
                title: 'Mechanical Ventilation Principles',
                subtitle: 'Supporting Respiratory Function',
                description: 'Understanding how mechanical ventilators support and replace natural breathing',
                visual: {
                    type: 'breathing',
                    description: 'Animated breathing mechanics'
                }
            }
        ]
    }
};

// ===== LECTURE DATA ACCESS FUNCTIONS =====

function getLectureData(lectureId) {
    return lectureDatabase[lectureId] || null;
}

function getAllLectures() {
    return Object.values(lectureDatabase);
}

function getLecturesByCategory(category) {
    return Object.values(lectureDatabase).filter(lecture => lecture.category === category);
}

function getLecturesByDifficulty(difficulty) {
    return Object.values(lectureDatabase).filter(lecture => lecture.difficulty === difficulty);
}

function searchLectures(query) {
    const searchTerm = query.toLowerCase();
    return Object.values(lectureDatabase).filter(lecture => 
        lecture.title.toLowerCase().includes(searchTerm) ||
        lecture.description.toLowerCase().includes(searchTerm) ||
        lecture.category.toLowerCase().includes(searchTerm)
    );
}

// ===== LECTURE CONTENT GENERATORS =====

function generateQuizElement(interactive) {
    let html = '<div class="quiz-container fade-in-delay-2">';
    html += `<h4>${interactive.question}</h4>`;
    html += '<div class="quiz-options">';
    
    interactive.options.forEach((option, index) => {
        html += `<button class="quiz-option" onclick="selectQuizAnswer(${index}, ${interactive.correct})">${option}</button>`;
    });
    
    html += '</div>';
    html += '<div class="quiz-feedback" id="quizFeedback" style="display: none;"></div>';
    html += '</div>';
    
    return html;
}

function generateSimulationElement(interactive) {
    let html = '<div class="simulation-container fade-in-delay-2">';
    html += `<h4>${interactive.title || 'Interactive Simulation'}</h4>`;
    html += `<p>${interactive.description}</p>`;
    html += '<div class="simulation-controls">';
    html += '<button class="btn-primary" onclick="launchSimulation()">Launch Simulation</button>';
    html += '</div>';
    html += '</div>';
    
    return html;
}

// ===== INTERACTIVE FUNCTIONS =====

function selectQuizAnswer(selectedIndex, correctIndex) {
    const options = document.querySelectorAll('.quiz-option');
    const feedback = document.getElementById('quizFeedback');
    
    // Disable all options
    options.forEach((option, index) => {
        option.disabled = true;
        if (index === correctIndex) {
            option.classList.add('correct');
        } else if (index === selectedIndex && selectedIndex !== correctIndex) {
            option.classList.add('incorrect');
        }
    });
    
    // Show feedback
    if (feedback) {
        feedback.style.display = 'block';
        if (selectedIndex === correctIndex) {
            feedback.className = 'quiz-feedback correct';
            feedback.innerHTML = '<i class="fas fa-check-circle"></i> Correct! Well done.';
        } else {
            feedback.className = 'quiz-feedback incorrect';
            feedback.innerHTML = '<i class="fas fa-times-circle"></i> Incorrect. The correct answer is highlighted.';
        }
    }
    
    // Track quiz interaction
    trackLectureEvent('quiz_answered', lectureState.currentLecture?.id, {
        question: selectedIndex,
        correct: selectedIndex === correctIndex,
        slideIndex: lectureState.currentSlide
    });
}

function launchSimulation() {
    // This would launch the appropriate simulation
    showNotification('Launching simulation...', 'info');
    
    // Track simulation launch
    trackLectureEvent('simulation_launched', lectureState.currentLecture?.id, {
        slideIndex: lectureState.currentSlide
    });
}

// ===== LECTURE METADATA =====

function getLectureMetadata() {
    const metadata = {
        totalLectures: Object.keys(lectureDatabase).length,
        categories: {},
        difficulties: {},
        totalDuration: 0
    };
    
    Object.values(lectureDatabase).forEach(lecture => {
        // Count by category
        metadata.categories[lecture.category] = (metadata.categories[lecture.category] || 0) + 1;
        
        // Count by difficulty
        metadata.difficulties[lecture.difficulty] = (metadata.difficulties[lecture.difficulty] || 0) + 1;
        
        // Sum total duration
        metadata.totalDuration += lecture.duration;
    });
    
    return metadata;
}

// ===== EXPORT FUNCTIONS =====
window.getLectureData = getLectureData;
window.getAllLectures = getAllLectures;
window.getLecturesByCategory = getLecturesByCategory;
window.getLecturesByDifficulty = getLecturesByDifficulty;
window.searchLectures = searchLectures;
window.selectQuizAnswer = selectQuizAnswer;
window.launchSimulation = launchSimulation;
window.getLectureMetadata = getLectureMetadata;
