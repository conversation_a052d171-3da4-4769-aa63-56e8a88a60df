<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anesthesia Machine Pneumatics Explorer</title>
    <style>
        body {
            font-family: 'Arial Rounded MT Bold', 'Helvetica Rounded', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #e0f7fa;
            color: #004d40;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            line-height: 1.6;
        }

        .app-container {
            width: 95%;
            max-width: 1200px;
            background-color: #ffffff;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #4db6ac;
            padding-bottom: 10px;
        }

        header h1 {
            color: #00796b;
            margin: 0;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #b2dfdb;
            border-radius: 8px;
        }

        .mode-buttons button, .quiz-controls button {
            background-color: #00796b;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        .mode-buttons button:hover, .quiz-controls button:hover {
            background-color: #004d40;
        }
        .mode-buttons button.active {
            background-color: #004d40;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
        }

        #score, #quiz-question {
            font-size: 1.1em;
            color: #004d40;
            font-weight: bold;
        }
        #quiz-question {
            margin-top: 10px;
            padding: 8px;
            background-color: #e0f2f1;
            border-radius: 5px;
            text-align: center;
        }

        .diagram-container {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 10px;
            padding: 10px;
            box-sizing: border-box;
            margin-bottom: 20px;
            overflow: hidden; /* Ensures SVG fits */
        }

        #pneumatic-diagram {
            width: 100%;
            height: auto; /* For responsive SVG */
            display: block; /* Removes extra space below SVG */
            border: 1px solid #ccc;
            border-radius: 8px;
        }

        .component {
            cursor: pointer;
            transition: opacity 0.2s ease, transform 0.2s ease;
        }
        .component:hover > :first-child { /* Target the shape, not text */
            opacity: 0.7;
            stroke-width: 3;
        }
        .component.highlighted > :first-child {
            stroke: #ffeb3b; /* Yellow highlight */
            stroke-width: 4px;
        }
        .component.correct > :first-child {
            stroke: #4CAF50; /* Green for correct */
            stroke-width: 5px;
            fill: #c8e6c9;
        }
        .component.incorrect > :first-child {
            stroke: #F44336; /* Red for incorrect */
            stroke-width: 5px;
            fill: #ffcdd2;
        }

        /* Pressure system zone styles */
        .high-pressure-zone { fill: #ffcdd2; opacity: 0.3; } /* Light Red */
        .intermediate-pressure-zone { fill: #c5cae9; opacity: 0.3; } /* Light Indigo */
        .low-pressure-zone { fill: #b2dfdb; opacity: 0.3; } /* Light Teal */

        .gas-line {
            stroke: #78909c; /* Blue Grey */
            stroke-width: 3;
            fill: none;
        }
        .gas-line-o2 { stroke: #4caf50; } /* Green */
        .gas-line-n2o { stroke: #2196f3; } /* Blue */
        .gas-line-air { stroke: #ffeb3b; } /* Yellow */
        .gas-line-mixed { stroke: #9c27b0; } /* Purple */


        /* Component specific styles */
        .cylinder-shape { fill: #757575; stroke: #424242; } /* Grey */
        .gauge-shape { fill: #fff; stroke: #616161; }
        .regulator-shape { fill: #90a4ae; stroke: #546e7a; } /* Blue Grey */
        .valve-shape { fill: #ef9a9a; stroke: #c62828; } /* Light Red for valves */
        .flowmeter-shape { fill: #e1f5fe; stroke: #4fc3f7; } /* Light Blue */
        .connector-shape { fill: #a1887f; stroke: #5d4037; } /* Brown */
        .safety-device-shape { fill: #fff59d; stroke: #fbc02d; } /* Yellow */
        .outlet-shape { fill: #ce93d8; stroke: #7b1fa2; } /* Purple */

        .component-text {
            font-size: 10px;
            font-family: Arial, sans-serif;
            fill: #333;
            text-anchor: middle;
            pointer-events: none; /* So text doesn't interfere with click on shape */
        }
        .zone-text {
            font-size: 16px;
            font-weight: bold;
            fill: #555;
            text-anchor: middle;
        }


        #info-box {
            background-color: #e8f5e9; /* Light Green */
            border: 1px solid #a5d6a7; /* Green border */
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            min-height: 100px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        #info-box h3 {
            margin-top: 0;
            color: #2e7d32; /* Dark Green */
        }
        #info-box p {
            margin-bottom: 5px;
        }
        #info-box strong {
            color: #1b5e20; /* Darker Green */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            .mode-buttons, .quiz-controls {
                display: flex;
                justify-content: space-around;
                width: 100%;
            }
            .mode-buttons button, .quiz-controls button {
                flex-grow: 1;
                margin: 5px;
            }
            #score {
                text-align: center;
                margin-top: 10px;
            }
            .component-text {
                font-size: 8px; /* Smaller text on mobile for SVG */
            }
            .zone-text {
                font-size: 12px;
            }
        }
        @media (max-width: 480px) {
            header h1 { font-size: 1.5em; }
            .mode-buttons button, .quiz-controls button { font-size: 0.9em; padding: 8px 10px;}
            #info-box { padding: 10px; }
            #info-box h3 { font-size: 1.1em; }
            #info-box p { font-size: 0.9em; }
        }

    </style>
</head>
<body>
    <div class="app-container">
        <header>
            <h1>Anesthesia Machine Pneumatic System Explorer</h1>
        </header>

        <div class="controls">
            <div class="mode-buttons">
                <button id="explore-mode-btn" class="active">Explore Mode</button>
                <button id="quiz-mode-btn">Quiz Mode</button>
            </div>
            <div class="quiz-controls" style="display: none;">
                <span id="score">Score: 0/0</span>
                <button id="next-question-btn" style="display: none;">Next Question</button>
            </div>
        </div>
        <div id="quiz-question" style="display: none;">Click on a component!</div>


        <div class="diagram-container">
            <svg id="pneumatic-diagram" viewBox="0 0 1200 750" preserveAspectRatio="xMidYMid meet">
                <!-- Pressure System Zones -->
                <rect x="10" y="10" width="1180" height="200" class="high-pressure-zone" rx="10" />
                <text x="600" y="35" class="zone-text">HIGH PRESSURE SYSTEM</text>
                
                <rect x="10" y="220" width="1180" height="200" class="intermediate-pressure-zone" rx="10" />
                <text x="600" y="245" class="zone-text">INTERMEDIATE PRESSURE SYSTEM</text>

                <rect x="10" y="430" width="1180" height="310" class="low-pressure-zone" rx="10" />
                <text x="600" y="455" class="zone-text">LOW PRESSURE SYSTEM</text>

                <!-- Gas Lines -->
                <!-- O2 Cylinder to Regulator -->
                <line x1="80" y1="180" x2="80" y2="200" class="gas-line gas-line-o2" />
                <line x1="80" y1="200" x2="250" y2="200" class="gas-line gas-line-o2" />
                <line x1="250" y1="200" x2="250" y2="280" class="gas-line gas-line-o2" /> <!-- Regulator output to IPS -->
                
                <!-- Pipeline O2 to IPS -->
                <line x1="50" y1="320" x2="250" y2="320" class="gas-line gas-line-o2" />
                <!-- Pipeline N2O to IPS -->
                <line x1="50" y1="360" x2="400" y2="360" class="gas-line gas-line-n2o" />
                <!-- Pipeline Air to IPS (less common in basic diagrams, but good to show path) -->
                <!-- <line x1="50" y1="400" x2="550" y2="400" class="gas-line gas-line-air" /> -->

                <!-- IPS to Flow Controls -->
                <line x1="250" y1="320" x2="250" y2="480" class="gas-line gas-line-o2" /> <!-- O2 from IPS to O2 flow control -->
                <line x1="400" y1="360" x2="400" y2="480" class="gas-line gas-line-n2o" /> <!-- N2O from IPS to N2O flow control -->
                
                <!-- O2 Flush -->
                <line x1="250" y1="320" x2="600" y2="320" class="gas-line gas-line-o2" /> <!-- O2 line in IPS -->
                <line x1="600" y1="320" x2="600" y2="380" class="gas-line gas-line-o2" /> <!-- To O2 Flush Valve -->
                <line x1="600" y1="380" x2="1050" y2="380" class="gas-line gas-line-o2" /> <!-- O2 Flush to CGO path -->
                <line x1="1050" y1="380" x2="1050" y2="650" class="gas-line gas-line-o2" />

                <!-- LPS Flow -->
                <!-- O2 Flow -->
                <line x1="250" y1="530" x2="250" y2="580" class="gas-line gas-line-o2" /> <!-- O2 Flow control to flowmeter -->
                <line x1="250" y1="630" x2="250" y2="650" class="gas-line gas-line-o2" /> <!-- O2 Flowmeter to common path -->
                <line x1="250" y1="650" x2="500" y2="650" class="gas-line gas-line-o2" />
                
                <!-- N2O Flow -->
                <line x1="400" y1="530" x2="400" y2="580" class="gas-line gas-line-n2o" /> <!-- N2O Flow control to flowmeter -->
                <line x1="400" y1="630" x2="400" y2="650" class="gas-line gas-line-n2o" /> <!-- N2O Flowmeter to common path -->
                <line x1="400" y1="650" x2="500" y2="650" class="gas-line gas-line-n2o" />

                <!-- Hypoxia device connects O2 and N2O flow paths conceptually -->

                <!-- Common path in LPS -->
                <line x1="500" y1="650" x2="850" y2="650" class="gas-line gas-line-mixed" /> <!-- After mixing, before CGO check valve -->
                <line x1="900" y1="650" x2="1050" y2="650" class="gas-line gas-line-mixed" /> <!-- After CGO check valve to CGO -->

                <!-- Pressure relief connection -->
                <line x1="975" y1="650" x2="975" y2="600" class="gas-line gas-line-mixed" />


                <!-- Components -->
                <!-- High Pressure System -->
                <g id="hangerYoke" class="component">
                    <rect x="30" y="70" width="100" height="40" class="connector-shape" rx="5"/>
                    <text x="80" y="95" class="component-text">Hanger Yoke</text>
                    <text x="80" y="107" class="component-text">(+ Check Valve)</text>
                </g>
                <g id="o2Cylinder" class="component">
                    <rect x="55" y="110" width="50" height="70" class="cylinder-shape" rx="5"/>
                    <text x="80" y="150" class="component-text">O2 Cylinder</text>
                </g>
                <g id="cylinderPressureIndicator" class="component">
                    <circle cx="180" cy="90" r="25" class="gauge-shape"/>
                    <line x1="180" y1="90" x2="195" y2="80" stroke="black" stroke-width="2"/>
                    <text x="180" y="125" class="component-text">Cylinder P</text>
                    <text x="180" y="135" class="component-text">Indicator</text>
                </g>
                 <g id="pressureRegulator" class="component">
                    <rect x="220" y="170" width="60" height="60" class="regulator-shape" rx="5"/>
                    <text x="250" y="205" class="component-text">Pressure</text>
                    <text x="250" y="217" class="component-text">Regulator</text>
                </g>

                <!-- Intermediate Pressure System -->
                <g id="pipelineInletO2" class="component">
                    <rect x="20" y="295" width="60" height="50" class="connector-shape" rx="5"/>
                    <text x="50" y="325" class="component-text">Pipeline O2</text>
                    <text x="50" y="335" class="component-text">Inlet (+CV)</text>
                </g>
                <g id="pipelineInletN2O" class="component">
                    <rect x="20" y="350" width="60" height="30" class="connector-shape" rx="5" style="fill: #81d4fa; stroke: #039be5;"/>
                    <text x="50" y="370" class="component-text">Pipeline N2O</text>
                     <text x="50" y="380" class="component-text">Inlet (+CV)</text>
                </g>
                <g id="pipelinePressureIndicatorO2" class="component">
                    <circle cx="150" cy="300" r="25" class="gauge-shape"/>
                    <line x1="150" y1="300" x2="165" y2="290" stroke="black" stroke-width="2"/>
                    <text x="150" y="335" class="component-text">Pipeline O2</text>
                    <text x="150" y="345" class="component-text">P Indicator</text>
                </g>
                <g id="pipelinePressureIndicatorN2O" class="component">
                    <circle cx="250" cy="360" r="25" class="gauge-shape" style="stroke: #039be5;"/>
                    <line x1="250" y1="360" x2="265" y2="350" stroke="black" stroke-width="2"/>
                    <text x="250" y="395" class="component-text">Pipeline N2O</text>
                    <text x="250" y="405" class="component-text">P Indicator</text>
                </g>
                <g id="oxygenFlushValve" class="component">
                    <ellipse cx="600" cy="350" rx="40" ry="25" class="valve-shape" style="fill: #80cbc4; stroke: #00796b;"/>
                    <text x="600" y="355" class="component-text">O2 Flush</text>
                    <text x="600" y="367" class="component-text">Valve</text>
                </g>

                <!-- Low Pressure System -->
                <g id="flowControlO2" class="component">
                    <rect x="220" y="480" width="60" height="50" class="valve-shape" style="fill: #a5d6a7; stroke: #388e3c;"/> <!-- Greenish for O2 -->
                    <text x="250" y="510" class="component-text">O2 Flow</text>
                    <text x="250" y="522" class="component-text">Control</text>
                </g>
                <g id="flowMeterO2" class="component">
                    <rect x="230" y="580" width="40" height="50" class="flowmeter-shape" style="stroke: #388e3c;"/>
                    <text x="250" y="610" class="component-text">O2 Flow</text>
                    <text x="250" y="622" class="component-text">Meter</text>
                </g>
                <g id="flowControlN2O" class="component">
                    <rect x="370" y="480" width="60" height="50" class="valve-shape" style="fill: #90caf9; stroke: #1976d2;"/> <!-- Bluish for N2O -->
                    <text x="400" y="510" class="component-text">N2O Flow</text>
                    <text x="400" y="522" class="component-text">Control</text>
                </g>
                <g id="flowMeterN2O" class="component">
                    <rect x="380" y="580" width="40" height="50" class="flowmeter-shape" style="stroke: #1976d2;"/>
                    <text x="400" y="610" class="component-text">N2O Flow</text>
                    <text x="400" y="622" class="component-text">Meter</text>
                </g>
                <g id="hypoxiaPreventionDevice" class="component">
                    <rect x="500" y="550" width="120" height="40" class="safety-device-shape" rx="5"/>
                    <text x="560" y="570" class="component-text">Hypoxia Prevention</text>
                    <text x="560" y="582" class="component-text">Device</text>
                    <!-- Conceptual link lines -->
                    <line x1="250" y1="635" x2="500" y2="570" stroke-dasharray="4 2" class="gas-line" />
                    <line x1="400" y1="635" x2="500" y2="570" stroke-dasharray="4 2" class="gas-line" />
                </g>
                <g id="unidirectionalCheckValve" class="component">
                    <ellipse cx="875" cy="650" rx="30" ry="20" class="valve-shape" style="fill: #ffab91; stroke: #e64a19;"/>
                    <text x="875" y="655" class="component-text">Outlet Check</text>
                    <text x="875" y="667" class="component-text">Valve</text>
                </g>
                <g id="pressureReliefDevice" class="component">
                    <rect x="945" y="550" width="60" height="50" class="safety-device-shape" style="fill: #ff8a65; stroke: #d84315;" rx="5"/>
                    <text x="975" y="575" class="component-text">Pressure</text>
                    <text x="975" y="587" class="component-text">Relief</text>
                </g>
                <g id="commonGasOutlet" class="component">
                    <rect x="1020" y="670" width="60" height="40" class="outlet-shape" rx="5"/>
                    <text x="1050" y="695" class="component-text">Common</text>
                    <text x="1050" y="707" class="component-text">Gas Outlet</text>
                </g>

            </svg>
        </div>

        <div id="info-box">
            <h3>Component Information</h3>
            <p>Click on a component in the diagram to learn more about it.</p>
        </div>
    </div>

    <script>
        const componentsData = {
            hangerYoke: {
                name: "Hanger Yoke Assembly (with Check Valve)",
                system: "High Pressure",
                function: "Connects gas cylinder (O2, N2O, Air) to the machine. Ensures unidirectional flow using an internal check valve and correct gas attachment via Pin Index Safety System (PISS).",
                pressureRange: "Cylinder pressure (e.g., O2 ~2000-2200 psig full)."
            },
            o2Cylinder: {
                name: "Oxygen Cylinder",
                system: "High Pressure",
                function: "Provides a reserve supply of oxygen gas.",
                pressureRange: "~2200 psig (E-cylinder, full O2)."
            },
            cylinderPressureIndicator: {
                name: "Cylinder Pressure Indicator/Gauge",
                system: "High Pressure",
                function: "Displays the pressure of gas remaining in the cylinder. Usually a Bourdon gauge.",
                pressureRange: "Measures cylinder pressure (0 - ~2500 psig)."
            },
            pressureRegulator: {
                name: "Cylinder Pressure Regulator (First Stage)",
                system: "High Pressure",
                function: "Reduces the high and variable cylinder pressure to a lower, constant intermediate pressure.",
                pressureRange: "Input: Cylinder pressure. Output: ~40-55 psig."
            },
            pipelineInletO2: {
                name: "Pipeline O2 Inlet Connection (with Check Valve)",
                system: "Intermediate Pressure",
                function: "Connects the machine to the hospital's piped oxygen supply. Includes a Diameter Index Safety System (DISS) fitting and an internal check valve to prevent reverse flow.",
                pressureRange: "Receives ~50-55 psig."
            },
            pipelineInletN2O: {
                name: "Pipeline N2O Inlet Connection (with Check Valve)",
                system: "Intermediate Pressure",
                function: "Connects the machine to the hospital's piped nitrous oxide supply. Includes DISS fitting and check valve.",
                pressureRange: "Receives ~50-55 psig."
            },
            pipelinePressureIndicatorO2: {
                name: "Pipeline O2 Pressure Indicator/Gauge",
                system: "Intermediate Pressure",
                function: "Displays the pressure of the oxygen supplied from the pipeline.",
                pressureRange: "Measures pipeline pressure (~50-55 psig)."
            },
            pipelinePressureIndicatorN2O: {
                name: "Pipeline N2O Pressure Indicator/Gauge",
                system: "Intermediate Pressure",
                function: "Displays the pressure of the nitrous oxide supplied from the pipeline.",
                pressureRange: "Measures pipeline pressure (~50-55 psig)."
            },
            oxygenFlushValve: {
                name: "Oxygen Flush Valve",
                system: "Intermediate Pressure (delivers to Low/Outlet)",
                function: "Delivers a high flow of oxygen (35-75 L/min) directly to the common gas outlet, bypassing flowmeters and vaporizers. Used for rapid circuit fill or emergency oxygenation.",
                pressureRange: "Receives O2 at intermediate pressure (~40-55 psig)."
            },
            flowControlO2: {
                name: "Oxygen Flow Control Valve (Needle Valve)",
                system: "Low Pressure (receives from Intermediate)",
                function: "Allows fine adjustment of oxygen flow to the flowmeter, controlled by the anesthetist.",
                pressureRange: "Input: ~40-55 psig. Output: Variable, near atmospheric to flowmeter."
            },
            flowMeterO2: {
                name: "Oxygen Flow Meter (Thorpe Tube)",
                system: "Low Pressure",
                function: "Measures and indicates the flow rate of oxygen being delivered. Typically a tapered tube with a bobbin or ball float.",
                pressureRange: "Operates at low pressure, slightly above atmospheric."
            },
            flowControlN2O: {
                name: "Nitrous Oxide Flow Control Valve (Needle Valve)",
                system: "Low Pressure (receives from Intermediate)",
                function: "Allows fine adjustment of nitrous oxide flow to its flowmeter.",
                pressureRange: "Input: ~40-55 psig. Output: Variable, near atmospheric."
            },
            flowMeterN2O: {
                name: "Nitrous Oxide Flow Meter (Thorpe Tube)",
                system: "Low Pressure",
                function: "Measures and indicates the flow rate of nitrous oxide.",
                pressureRange: "Operates at low pressure, slightly above atmospheric."
            },
            hypoxiaPreventionDevice: {
                name: "Hypoxia Prevention Safety Device (e.g., Link-25)",
                system: "Low Pressure",
                function: "Prevents delivery of a hypoxic gas mixture by ensuring a minimum oxygen concentration (typically 21-25%) when nitrous oxide is also flowing. Often a mechanical or pneumatic link between O2 and N2O flow controls.",
                pressureRange: "Operates within the low-pressure system."
            },
            unidirectionalCheckValve: {
                name: "Unidirectional Check Valve (Outlet/CGO Check Valve)",
                system: "Low Pressure",
                function: "Located near the common gas outlet, before gas exits to the breathing circuit. Prevents backflow of gas from the breathing circuit into the machine (especially vaporizers), and can prevent rebreathing if positive pressure is applied to the circuit (e.g. during jet ventilation via CGO).",
                pressureRange: "Low pressure. Opens with minimal forward pressure."
            },
            pressureReliefDevice: {
                name: "Low-Pressure System Pressure Relief Device",
                system: "Low Pressure",
                function: "Protects the low-pressure components (flowmeters, vaporizers) from excessive pressure buildup by venting gas if pressure exceeds a set limit (e.g., 35-75 cm H2O or ~2 psi).",
                pressureRange: "Opens at a low, preset relief pressure."
            },
            commonGasOutlet: {
                name: "Common Gas Outlet (CGO)",
                system: "Low Pressure",
                function: "The port where the final mixed gases (O2, N2O, air, anesthetic vapor) exit the anesthesia machine to be delivered to the patient breathing circuit.",
                pressureRange: "Delivers gas at low pressure, slightly above atmospheric."
            }
        };

        const svgDiagram = document.getElementById('pneumatic-diagram');
        const infoBox = document.getElementById('info-box');
        const exploreModeBtn = document.getElementById('explore-mode-btn');
        const quizModeBtn = document.getElementById('quiz-mode-btn');
        const quizControls = document.querySelector('.quiz-controls');
        const scoreDisplay = document.getElementById('score');
        const quizQuestionDisplay = document.getElementById('quiz-question');
        const nextQuestionBtn = document.getElementById('next-question-btn');

        let currentMode = 'explore'; // 'explore' or 'quiz'
        let quizScore = 0;
        let questionsAsked = 0;
        let quizQuestions = [];
        let currentQuestionIndex = 0;
        let currentCorrectAnswerId = null;
        let highlightedComponent = null;

        // Audio context for sound feedback
        let audioCtx;
        try {
            audioCtx = new (window.AudioContext || window.webkitAudioContext)();
        } catch (e) {
            console.warn("Web Audio API is not supported in this browser.");
            audioCtx = null;
        }
        
        function playSound(type) {
            if (!audioCtx) return;
            const oscillator = audioCtx.createOscillator();
            const gainNode = audioCtx.createGain();
            oscillator.connect(gainNode);
            gainNode.connect(audioCtx.destination);
            gainNode.gain.setValueAtTime(0.1, audioCtx.currentTime);

            if (type === 'correct') {
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(660, audioCtx.currentTime); // E5
                gainNode.gain.exponentialRampToValueAtTime(0.00001, audioCtx.currentTime + 0.3);
            } else if (type === 'incorrect') {
                oscillator.type = 'square';
                oscillator.frequency.setValueAtTime(150, audioCtx.currentTime); // D3
                gainNode.gain.exponentialRampToValueAtTime(0.00001, audioCtx.currentTime + 0.4);
            }
            oscillator.start();
            oscillator.stop(audioCtx.currentTime + (type === 'correct' ? 0.3 : 0.4));
        }


        function displayComponentInfo(componentId) {
            const component = componentsData[componentId];
            if (component) {
                infoBox.innerHTML = `
                    <h3>${component.name}</h3>
                    <p><strong>System:</strong> ${component.system}</p>
                    <p><strong>Function:</strong> ${component.function}</p>
                    <p><strong>Typical Pressure:</strong> ${component.pressureRange}</p>
                `;
            } else {
                infoBox.innerHTML = `<h3>Component Information</h3><p>Select a component to see details.</p>`;
            }
        }
        
        function highlightSvgComponent(elementId, styleClass = 'highlighted') {
            if (highlightedComponent) {
                highlightedComponent.classList.remove('highlighted', 'correct', 'incorrect');
            }
            const el = document.getElementById(elementId);
            if (el) {
                el.classList.add(styleClass);
                highlightedComponent = el;
            }
        }
        
        function clearHighlight() {
            if (highlightedComponent) {
                highlightedComponent.classList.remove('highlighted', 'correct', 'incorrect');
                highlightedComponent = null;
            }
        }

        function handleComponentClick(event) {
            let target = event.target;
            // Traverse up to find the group element with an ID (the component)
            while (target && target !== svgDiagram && !target.id) {
                target = target.parentNode;
            }

            if (target && target.id && componentsData[target.id]) {
                const componentId = target.id;
                
                if (currentMode === 'explore') {
                    displayComponentInfo(componentId);
                    highlightSvgComponent(componentId);
                } else if (currentMode === 'quiz' && currentCorrectAnswerId) {
                    // Quiz mode answer checking
                    if (componentId === currentCorrectAnswerId) {
                        quizScore++;
                        quizQuestionDisplay.textContent = `Correct! It's the ${componentsData[componentId].name}.`;
                        highlightSvgComponent(componentId, 'correct');
                        playSound('correct');
                    } else {
                        quizQuestionDisplay.textContent = `Oops! That's the ${componentsData[componentId].name}. The correct answer was ${componentsData[currentCorrectAnswerId].name}.`;
                        highlightSvgComponent(componentId, 'incorrect');
                        playSound('incorrect');
                        // Optionally highlight the correct one too after a delay or on next
                        setTimeout(() => {
                            const correctEl = document.getElementById(currentCorrectAnswerId);
                            if(correctEl && highlightedComponent !== correctEl) { // don't re-highlight if it was the one clicked
                                correctEl.classList.add('correct'); // show correct one briefly
                            }
                        }, 100);
                    }
                    questionsAsked++;
                    updateScoreDisplay();
                    currentCorrectAnswerId = null; // Prevent multiple answers for one question
                    nextQuestionBtn.style.display = 'inline-block';
                }
            } else {
                 if (currentMode === 'explore') {
                    clearHighlight();
                    displayComponentInfo(null); // Clear info box if non-component is clicked
                 }
            }
        }

        svgDiagram.addEventListener('click', handleComponentClick);

        exploreModeBtn.addEventListener('click', () => setMode('explore'));
        quizModeBtn.addEventListener('click', () => setMode('quiz'));
        nextQuestionBtn.addEventListener('click', setupNextQuestion);

        function setMode(mode) {
            currentMode = mode;
            clearHighlight();
            displayComponentInfo(null);

            if (mode === 'explore') {
                exploreModeBtn.classList.add('active');
                quizModeBtn.classList.remove('active');
                quizControls.style.display = 'none';
                quizQuestionDisplay.style.display = 'none';
                infoBox.style.display = 'block';
                displayComponentInfo(null); // Reset info box
            } else if (mode === 'quiz') {
                quizModeBtn.classList.add('active');
                exploreModeBtn.classList.remove('active');
                quizControls.style.display = 'flex';
                quizQuestionDisplay.style.display = 'block';
                infoBox.style.display = 'none'; // Hide info box in quiz mode
                startQuiz();
            }
        }

        function startQuiz() {
            quizScore = 0;
            questionsAsked = 0;
            // Create a shuffled list of component IDs for questions
            quizQuestions = Object.keys(componentsData).sort(() => 0.5 - Math.random());
            currentQuestionIndex = 0;
            updateScoreDisplay();
            setupNextQuestion();
        }

        function setupNextQuestion() {
            clearHighlight(); // Clear highlights from previous question
            if (currentQuestionIndex < quizQuestions.length) {
                currentCorrectAnswerId = quizQuestions[currentQuestionIndex];
                const componentName = componentsData[currentCorrectAnswerId].name;
                quizQuestionDisplay.textContent = `Question ${currentQuestionIndex + 1}/${quizQuestions.length}: Click on the "${componentName}".`;
                currentQuestionIndex++;
                nextQuestionBtn.style.display = 'none';
            } else {
                quizQuestionDisplay.textContent = `Quiz finished! Your score: ${quizScore}/${quizQuestions.length}. Select "Quiz Mode" to play again.`;
                currentCorrectAnswerId = null;
                nextQuestionBtn.style.display = 'none';
            }
            updateScoreDisplay();
        }

        function updateScoreDisplay() {
            scoreDisplay.textContent = `Score: ${quizScore}/${questionsAsked}`;
        }

        // Initialize
        setMode('explore'); // Start in explore mode
        
        // Make all component groups focusable for accessibility, though not fully implemented here
        Object.keys(componentsData).forEach(id => {
            const el = document.getElementById(id);
            if (el) el.setAttribute('tabindex', '0');
        });

    </script>
</body>
</html>
