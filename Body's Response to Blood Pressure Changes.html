<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hypertension Response Simulator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #eef2f7; /* Light grayish blue */
            padding: 20px;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .container {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 95%;
            width: 700px;
        }

        h1 {
            color: #2c3e50; /* Dark blue-gray */
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        label {
            font-size: 1em;
            color: #34495e; 
        }

        input[type="range"] {
            width: 200px;
            cursor: pointer;
            accent-color: #3498db; 
        }

        #bp-status-label {
            font-weight: bold;
            color: #2980b9; 
            min-width: 50px; 
            text-align: left;
        }

        #message-area {
            margin-top: 15px;
            font-weight: bold;
            min-height: 24px; 
            font-size: 1.1em;
            color: #007bff; 
        }

        #cardiovascular-system-svg {
            width: 100%;
            max-width: 600px;
            height: auto;
            border: 1px solid #bdc3c7; 
            border-radius: 8px;
            margin-top: 20px;
            overflow: visible; /* Allows ANP effect to go outside SVG bounds if needed, though rineset */
        }

        .label { font-size: 11px; fill: #333; text-anchor: middle; font-weight: bold;}
        .label-small { font-size: 9px; fill: #555; text-anchor: middle; }

        #heart {
            fill: #e74c3c; /* Red */
            stroke: #c0392b; /* Darker red */
            stroke-width: 2;
            transform-origin: center center; 
        }
        .heart-beat-normal { animation: heartBeat 1s infinite ease-in-out; } /* 60 bpm */
        .heart-beat-slow { animation: heartBeat 1.5s infinite ease-in-out; } /* 40 bpm */
        .heart-beat-fast { animation: heartBeat 0.75s infinite ease-in-out; } /* 80 bpm */
        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.08); }
        }

        #anp-effect {
            fill: rgba(135, 206, 250, 0.7); /* LightSkyBlue with alpha */
            stroke: #4682B4; /* SteelBlue */
            stroke-width: 1;
            r: 0; 
            opacity: 0; 
            transform-origin: center center;
        }
        .anp-triggered {
            animation: anpPulse 1.5s ease-out; /* Pulse duration */
        }
        @keyframes anpPulse {
            0% { r: 5px; opacity: 0.8; }
            100% { r: 50px; opacity: 0; }
        }

        .baroreceptor {
            fill: #f1c40f; /* Sunflower yellow */
            stroke: #7f8c8d; /* Grayish */
            stroke-width: 0.5;
        }
        .baroreceptor-firing {
            animation: flashEffect 0.4s infinite alternate; 
        }
        @keyframes flashEffect { /* Renamed from flash to avoid potential conflicts */
            from { opacity: 1; r: 6px; }
            to { opacity: 0.4; r: 7px; } 
        }

        .nerve { stroke-width: 2.5; fill: none; transition: stroke 0.5s ease, opacity 0.5s ease; }
        #sympathetic-nerve { stroke: #f39c12; /* Orange */ stroke-dasharray: 5,3; }
        #parasympathetic-nerve { stroke: #3498db; /* Peter River Blue */ stroke-dasharray: 7,4;}

        .artery {
            fill: none;
            stroke: #c0392b; 
            transition: stroke-width 0.5s ease;
        }

        .kidney { fill: #9b59b6; /* Amethyst purple */ }
        #renin-text {
            font-size: 14px;
            font-weight: bold;
            fill: #27ae60; /* Nephritis green */
            text-anchor: middle;
            transition: opacity 0.5s ease;
        }

        #adh-status { fill: #2c3e50; font-size: 12px; text-anchor: start; font-weight: 500;}
    </style>
</head>
<body>
    <div class="container">
        <h1>Body's Response to Blood Pressure Changes</h1>
        <div class="controls">
            <label for="bp-slider">Blood Pressure:</label>
            <input type="range" id="bp-slider" min="0" max="2" value="1" step="1">
            <span id="bp-status-label">Normal</span>
        </div>
        <div id="message-area">Normal blood pressure!</div>

        <svg id="cardiovascular-system-svg" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
            <!-- Nerves -->
            <path id="sympathetic-nerve" class="nerve" d="M 180 30 Q 230 100 280 170 M 280 170 Q 350 180 380 250 M 280 170 Q 250 250 200 300"/>
            <text x="160" y="25" class="label">Sympathetic NS</text>
            <path id="parasympathetic-nerve" class="nerve" d="M 420 30 Q 370 100 320 175"/> 
            <text x="440" y="25" class="label">Parasympathetic NS</text>

            <!-- Arteries -->
            <g id="arteries-group">
                <path id="aorta-arch-left" class="artery" d="M 300 190 Q 300 130 250 130 Q 220 130 220 100" stroke-width="10"/> 
                <path id="aorta-arch-right" class="artery" d="M 300 190 Q 300 130 350 130 Q 380 130 380 100" stroke-width="10"/> 
                <path id="descending-aorta" class="artery" d="M 300 190 L 300 300" stroke-width="10"/>
                <path id="kidney-artery-left" class="artery" d="M 300 280 Q 200 280 130 310" stroke-width="5"/>
                <path id="kidney-artery-right" class="artery" d="M 300 280 Q 400 280 470 310" stroke-width="5"/>
            </g>

            <!-- Heart and ANP effect -->
            <g id="heart-group" transform="translate(300, 210)"> 
                <path id="heart" d="M 0 -20 C -40 -20 -40 20 0 40 C 40 20 40 -20 0 -20 Z"/>
                <circle id="anp-effect" cx="0" cy="0" /> 
            </g>
            <text x="300" y="265" class="label">Heart</text>

            <!-- Baroreceptors -->
            <g id="baroreceptors">
                <circle id="aortic-arch-baroreceptor" class="baroreceptor" cx="275" cy="145" r="6"/>
                <text x="275" y="130" class="label-small">Aortic Arch BR</text>
                <circle id="carotid-sinus-baroreceptor-left" class="baroreceptor" cx="220" cy="90" r="6"/>
                <text x="220" y="80" class="label-small">Carotid Sinus BR</text>
                <circle id="carotid-sinus-baroreceptor-right" class="baroreceptor" cx="380" cy="90" r="6"/>
                <text x="380" y="80" class="label-small">Carotid Sinus BR</text>
            </g>

            <!-- Kidneys & JGA -->
            <g id="left-kidney-group">
                <path id="kidney-left" class="kidney" d="M 110 300 C 70 300 70 350 110 350 C 150 350 150 300 110 300 Z"/>
                <text x="125" y="295" class="label-small">JGA</text>
            </g>
            <g id="right-kidney-group">
                <path id="kidney-right" class="kidney" d="M 490 300 C 450 300 450 350 490 350 C 530 350 530 300 490 300 Z"/>
                <text x="475" y="295" class="label-small">JGA</text>
            </g>
            <text x="300" y="370" class="label">Kidneys</text>
            <text id="renin-text" x="300" y="340">Renin</text>

            <text id="adh-status" x="15" y="385">ADH: Normal</text>
        </svg>
    </div>

    <script>
        const slider = document.getElementById('bp-slider');
        const bpStatusLabel = document.getElementById('bp-status-label');
        const messageArea = document.getElementById('message-area');

        const heart = document.getElementById('heart');
        const anpEffect = document.getElementById('anp-effect');
        const baroreceptors = [
            document.getElementById('aortic-arch-baroreceptor'),
            document.getElementById('carotid-sinus-baroreceptor-left'),
            document.getElementById('carotid-sinus-baroreceptor-right')
        ];
        const sympatheticNerve = document.getElementById('sympathetic-nerve');
        const parasympatheticNerve = document.getElementById('parasympathetic-nerve');
        const allArteries = document.querySelectorAll('.artery');
        const reninText = document.getElementById('renin-text');
        const adhStatusText = document.getElementById('adh-status');

        const initialArteryStrokeWidths = new Map();
        allArteries.forEach(arteryEl => {
            initialArteryStrokeWidths.set(arteryEl, parseFloat(getComputedStyle(arteryEl).strokeWidth));
        });

        const initialNerveStyles = {
            sympathetic: { stroke: '', opacity: '' },
            parasympathetic: { stroke: '', opacity: '' }
        };

        function cleanANP() {
            anpEffect.classList.remove('anp-triggered');
            anpEffect.style.animation = 'none'; 
            anpEffect.style.r = '0';
            anpEffect.style.opacity = '0';
        }

        function resetToNormalState() {
            messageArea.textContent = "Normal blood pressure!";
            messageArea.style.color = '#007bff';

            baroreceptors.forEach(br => br.classList.remove('baroreceptor-firing'));
            cleanANP();

            sympatheticNerve.style.stroke = initialNerveStyles.sympathetic.stroke;
            sympatheticNerve.style.opacity = initialNerveStyles.sympathetic.opacity;
            parasympatheticNerve.style.stroke = initialNerveStyles.parasympathetic.stroke;
            parasympatheticNerve.style.opacity = initialNerveStyles.parasympathetic.opacity;

            heart.className = ''; 
            heart.classList.add('heart-beat-normal');

            allArteries.forEach(artery => {
                artery.style.strokeWidth = (initialArteryStrokeWidths.get(artery) || 5) + 'px';
            });

            reninText.style.opacity = '1';
            adhStatusText.textContent = "ADH: Normal";
        }

        function setHighBloodPressure() {
            messageArea.textContent = "High Blood Pressure Response Activated!";
            messageArea.style.color = '#e74c3c'; 

            baroreceptors.forEach(br => br.classList.add('baroreceptor-firing'));

            cleanANP(); // Clean previous state/animation
            void anpEffect.offsetWidth; // Force reflow
            anpEffect.classList.add('anp-triggered');
            anpEffect.addEventListener('animationend', cleanANP, { once: true });

            sympatheticNerve.style.stroke = '#fce1c2'; // Lighter orange for decreased activity
            sympatheticNerve.style.opacity = '0.5';
            parasympatheticNerve.style.stroke = '#2980b9'; // Stronger blue for increased activity
            parasympatheticNerve.style.opacity = '1';

            heart.className = '';
            heart.classList.add('heart-beat-slow');

            allArteries.forEach(artery => {
                artery.style.strokeWidth = ((initialArteryStrokeWidths.get(artery) || 5) * 1.35) + 'px'; // Vasodilation: 35% increase
            });

            reninText.style.opacity = '0.3';
            adhStatusText.textContent = "ADH Release: Inhibited";
        }

        function setLowBloodPressure() {
            messageArea.textContent = "Low Blood Pressure Response Activated!";
            messageArea.style.color = '#1abc9c'; 

            baroreceptors.forEach(br => br.classList.remove('baroreceptor-firing'));
            cleanANP();

            sympatheticNerve.style.stroke = '#e67e22'; // Stronger orange for increased activity
            sympatheticNerve.style.opacity = '1';
            parasympatheticNerve.style.stroke = '#a9dff7'; // Lighter blue for decreased activity
            parasympatheticNerve.style.opacity = '0.5';

            heart.className = '';
            heart.classList.add('heart-beat-fast');

            allArteries.forEach(artery => {
                artery.style.strokeWidth = ((initialArteryStrokeWidths.get(artery) || 5) * 0.7) + 'px'; // Vasoconstriction: 30% decrease
            });

            reninText.style.opacity = '1'; 
            adhStatusText.textContent = "ADH Release: Stimulated";
        }

        slider.addEventListener('input', function() {
            const value = parseInt(this.value);
            if (value === 0) {
                bpStatusLabel.textContent = 'Low';
                setLowBloodPressure();
            } else if (value === 1) {
                bpStatusLabel.textContent = 'Normal';
                resetToNormalState();
            } else {
                bpStatusLabel.textContent = 'High';
                setHighBloodPressure();
            }
        });
        
        document.addEventListener('DOMContentLoaded', () => {
            // Store initial computed styles for nerves for reliable reset
            initialNerveStyles.sympathetic.stroke = getComputedStyle(sympatheticNerve).stroke;
            initialNerveStyles.sympathetic.opacity = getComputedStyle(sympatheticNerve).opacity;
            initialNerveStyles.parasympathetic.stroke = getComputedStyle(parasympatheticNerve).stroke;
            initialNerveStyles.parasympathetic.opacity = getComputedStyle(parasympatheticNerve).opacity;
            
            initializeApp();
        });

        function initializeApp() {
            const initialValue = parseInt(slider.value);
            // Call the appropriate function to set the initial state based on slider value
            // This also ensures the message area and labels are correctly set on load.
            if (initialValue === 0) {
                bpStatusLabel.textContent = 'Low';
                setLowBloodPressure();
            } else if (initialValue === 1) {
                bpStatusLabel.textContent = 'Normal';
                resetToNormalState();
            } else { // initialValue === 2
                bpStatusLabel.textContent = 'High';
                setHighBloodPressure();
            }
        }
    </script>
</body>
</html>
