// ===== INTERACTIVE DIAGRAMS SYSTEM ===== //

// Component Information Database
const componentInfo = {
    patient: {
        title: "Patient - Signal Source",
        description: "The patient generates physiological signals through normal body functions. These bioelectric and biomechanical signals are the foundation of all patient monitoring.",
        specs: [
            "ECG signals: 0.5-4.0 mV amplitude",
            "Heart rate: 60-100 bpm (normal adult)",
            "Blood pressure: Systolic 90-140 mmHg, Diastolic 60-90 mmHg",
            "Respiratory rate: 12-20 breaths/min",
            "Body temperature: 36.5-37.5°C",
            "SpO₂: 95-100% (normal)"
        ]
    },
    sensors: {
        title: "ECG Electrodes - Signal Acquisition",
        description: "Silver/silver chloride electrodes detect cardiac electrical activity. Lead II configuration provides optimal P-wave and QRS complex visualization.",
        specs: [
            "Material: Ag/AgCl with conductive gel",
            "Impedance: <5kΩ at 10Hz",
            "Frequency response: 0.05-150 Hz",
            "Common mode rejection: >90dB",
            "Input bias current: <100pA",
            "Noise level: <50μV RMS"
        ]
    },
    "bp-cuff": {
        title: "Blood Pressure Cuff - Oscillometric Method",
        description: "Automated blood pressure measurement using oscillometric technique. Detects arterial wall oscillations during cuff deflation to determine systolic and diastolic pressures.",
        specs: [
            "Measurement range: 30-300 mmHg",
            "Accuracy: ±3 mmHg or ±2%",
            "Cuff pressure: 0-300 mmHg",
            "Deflation rate: 2-3 mmHg/sec",
            "Measurement time: 15-45 seconds",
            "Calibration: Annual verification required"
        ]
    },
    "pulse-ox": {
        title: "Pulse Oximeter - SpO₂ Measurement",
        description: "Non-invasive oxygen saturation monitoring using dual-wavelength photoplethysmography. Red (660nm) and infrared (940nm) LEDs measure oxyhemoglobin vs deoxyhemoglobin ratios.",
        specs: [
            "Wavelengths: 660nm (red), 940nm (infrared)",
            "SpO₂ range: 70-100%",
            "Accuracy: ±2% (70-100%)",
            "Pulse rate: 30-250 bpm",
            "Response time: <30 seconds",
            "Motion artifact rejection: Advanced algorithms"
        ]
    },
    processing: {
        title: "Signal Processing Unit",
        description: "Advanced digital signal processing system that amplifies, filters, and analyzes physiological signals. Implements real-time algorithms for parameter extraction and alarm detection.",
        specs: [
            "ADC Resolution: 16-bit minimum",
            "Sampling rate: 1000 Hz (ECG), 125 Hz (other)",
            "Amplification: 1000x (ECG), variable (others)",
            "Filters: 0.05-150 Hz bandpass (ECG)",
            "Processing: ARM Cortex-M7 @ 400MHz",
            "Memory: 2MB RAM, 16MB Flash"
        ]
    },
    display: {
        title: "Patient Monitor Display",
        description: "High-resolution color display showing real-time waveforms, numeric parameters, trends, and alarms. Touch-screen interface for user interaction and configuration.",
        specs: [
            "Screen size: 15-21 inches",
            "Resolution: 1920x1080 minimum",
            "Refresh rate: 60 Hz",
            "Waveform update: Real-time",
            "Alarm response: <3 seconds",
            "Touch interface: Multi-touch capacitive"
        ]
    }
};

// Diagram state
const diagramState = {
    currentDiagram: 'block',
    selectedComponent: null,
    animationEnabled: true
};

// Initialize Diagrams
function initializeDiagrams() {
    console.log('Initializing Interactive Diagrams...');
    
    // Set up event listeners
    setupDiagramEventListeners();
    
    // Initialize component info panel
    updateComponentInfo('patient');
    
    // Start animations
    if (diagramState.animationEnabled) {
        startDiagramAnimations();
    }
    
    console.log('Interactive Diagrams initialized');
}

// Setup Event Listeners
function setupDiagramEventListeners() {
    // Tab switching
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const diagramType = e.target.textContent.toLowerCase().replace(' ', '');
            showDiagram(diagramType);
        });
    });
    
    // Component hover effects
    const components = document.querySelectorAll('.diagram-component');
    components.forEach(component => {
        component.addEventListener('mouseenter', () => {
            component.style.filter = 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.5))';
        });
        
        component.addEventListener('mouseleave', () => {
            component.style.filter = 'none';
        });
    });
}

// Show Diagram
function showDiagram(type) {
    // Update tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.textContent.toLowerCase().includes(type)) {
            btn.classList.add('active');
        }
    });
    
    // Hide all diagram panels
    const panels = document.querySelectorAll('.diagram-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
    });
    
    // Show selected diagram
    let targetPanel;
    switch (type) {
        case 'block':
            targetPanel = document.getElementById('blockDiagram');
            break;
        case 'schematic':
            targetPanel = document.getElementById('schematicDiagram');
            if (!targetPanel) {
                createSchematicDiagram();
            }
            break;
        case 'signal':
            targetPanel = document.getElementById('signalDiagram');
            if (!targetPanel) {
                createSignalFlowDiagram();
            }
            break;
    }
    
    if (targetPanel) {
        targetPanel.classList.add('active');
    }
    
    diagramState.currentDiagram = type;
}

// Show Component Information
function showComponentInfo(componentId) {
    const info = componentInfo[componentId];
    if (!info) return;
    
    diagramState.selectedComponent = componentId;
    updateComponentInfo(componentId);
    
    // Highlight selected component
    const components = document.querySelectorAll('.diagram-component');
    components.forEach(comp => {
        comp.style.opacity = '0.6';
    });
    
    const selectedComp = document.querySelector(`.diagram-component.${componentId}`);
    if (selectedComp) {
        selectedComp.style.opacity = '1';
        selectedComp.style.filter = 'drop-shadow(0 0 15px rgba(59, 130, 246, 0.8))';
    }
    
    // Reset after 3 seconds
    setTimeout(() => {
        components.forEach(comp => {
            comp.style.opacity = '1';
            comp.style.filter = 'none';
        });
    }, 3000);
}

// Update Component Information Panel
function updateComponentInfo(componentId) {
    const info = componentInfo[componentId];
    if (!info) return;
    
    const titleElement = document.getElementById('componentTitle');
    const descElement = document.getElementById('componentDescription');
    const specsElement = document.getElementById('componentSpecs');
    
    if (titleElement) titleElement.textContent = info.title;
    if (descElement) descElement.textContent = info.description;
    
    if (specsElement && info.specs) {
        specsElement.innerHTML = `
            <h5 style="color: var(--module-text-primary); margin: 1rem 0 0.5rem 0; font-weight: 600;">Technical Specifications:</h5>
            <ul style="color: var(--module-text-secondary); line-height: 1.6; margin: 0; padding-left: 1.5rem;">
                ${info.specs.map(spec => `<li>${spec}</li>`).join('')}
            </ul>
        `;
    }
}

// Create Schematic Diagram
function createSchematicDiagram() {
    const diagramContainer = document.querySelector('.diagram-container');
    if (!diagramContainer) return;
    
    const schematicPanel = document.createElement('div');
    schematicPanel.id = 'schematicDiagram';
    schematicPanel.className = 'diagram-panel';
    
    schematicPanel.innerHTML = `
        <svg class="interactive-diagram" viewBox="0 0 800 600">
            <!-- ECG Amplifier Circuit -->
            <g class="circuit-section">
                <text x="400" y="30" class="section-title" text-anchor="middle" fill="var(--module-text-primary)" font-size="16" font-weight="600">ECG Amplifier Circuit</text>
                
                <!-- Input Stage -->
                <g class="circuit-element" onclick="showComponentInfo('input-stage')">
                    <!-- Differential Amplifier -->
                    <polygon points="100,150 150,130 150,170" class="op-amp" fill="var(--module-bg-primary)" stroke="var(--module-primary)" stroke-width="2"/>
                    <text x="125" y="155" text-anchor="middle" fill="var(--module-text-primary)" font-size="10">OP1</text>
                    
                    <!-- Input resistors -->
                    <rect x="50" y="135" width="30" height="8" class="resistor" fill="none" stroke="var(--module-text-primary)" stroke-width="1"/>
                    <text x="65" y="130" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">R1 1MΩ</text>
                    
                    <rect x="50" y="165" width="30" height="8" class="resistor" fill="none" stroke="var(--module-text-primary)" stroke-width="1"/>
                    <text x="65" y="185" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">R2 1MΩ</text>
                </g>
                
                <!-- Filter Stage -->
                <g class="circuit-element" onclick="showComponentInfo('filter-stage')">
                    <!-- Low-pass filter -->
                    <rect x="200" y="145" width="30" height="8" class="resistor"/>
                    <text x="215" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">R3 10kΩ</text>
                    
                    <!-- Capacitor -->
                    <line x1="250" y1="140" x2="250" y2="160" class="capacitor"/>
                    <line x1="255" y1="140" x2="255" y2="160" class="capacitor"/>
                    <text x="252" y="175" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">C1 1μF</text>
                </g>
                
                <!-- Gain Stage -->
                <g class="circuit-element" onclick="showComponentInfo('gain-stage')">
                    <polygon points="300,150 350,130 350,170" class="op-amp" fill="var(--module-bg-primary)" stroke="var(--module-secondary)" stroke-width="2"/>
                    <text x="325" y="155" text-anchor="middle" fill="var(--module-text-primary)" font-size="10">OP2</text>
                    
                    <!-- Feedback resistor -->
                    <rect x="320" y="110" width="30" height="8" class="resistor"/>
                    <text x="335" y="105" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">Rf 100kΩ</text>
                </g>
                
                <!-- ADC -->
                <g class="circuit-element" onclick="showComponentInfo('adc')">
                    <rect x="400" y="130" width="60" height="40" fill="var(--module-bg-primary)" stroke="var(--module-accent)" stroke-width="2"/>
                    <text x="430" y="145" text-anchor="middle" fill="var(--module-text-primary)" font-size="10">ADC</text>
                    <text x="430" y="160" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">16-bit</text>
                </g>
                
                <!-- Microcontroller -->
                <g class="circuit-element" onclick="showComponentInfo('mcu')">
                    <rect x="500" y="120" width="80" height="60" fill="var(--module-bg-primary)" stroke="var(--module-warning)" stroke-width="2"/>
                    <text x="540" y="140" text-anchor="middle" fill="var(--module-text-primary)" font-size="10">MCU</text>
                    <text x="540" y="155" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">ARM Cortex-M7</text>
                    <text x="540" y="170" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">400MHz</text>
                </g>
                
                <!-- Connections -->
                <line x1="80" y1="139" x2="100" y2="139" class="wire"/>
                <line x1="80" y1="169" x2="100" y2="169" class="wire"/>
                <line x1="150" y1="150" x2="200" y2="150" class="wire"/>
                <line x1="230" y1="150" x2="300" y2="150" class="wire"/>
                <line x1="350" y1="150" x2="400" y2="150" class="wire"/>
                <line x1="460" y1="150" x2="500" y2="150" class="wire"/>
                
                <!-- Ground symbols -->
                <g class="ground">
                    <line x1="250" y1="160" x2="250" y2="180"/>
                    <line x1="240" y1="180" x2="260" y2="180"/>
                    <line x1="245" y1="185" x2="255" y2="185"/>
                </g>
                
                <!-- Power supply -->
                <circle cx="430" cy="100" r="15" class="voltage-source"/>
                <text x="430" y="105" text-anchor="middle" fill="var(--module-text-primary)" font-size="10">+5V</text>
            </g>
            
            <!-- Signal Flow Indicators -->
            <g class="signal-flow">
                <circle cx="30" cy="150" r="4" class="signal-node"/>
                <text x="30" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">ECG Input</text>
                
                <circle cx="620" cy="150" r="4" class="signal-node"/>
                <text x="620" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="8">Digital Output</text>
            </g>
        </svg>
    `;
    
    diagramContainer.appendChild(schematicPanel);
}

// Create Signal Flow Diagram
function createSignalFlowDiagram() {
    const diagramContainer = document.querySelector('.diagram-container');
    if (!diagramContainer) return;
    
    const signalPanel = document.createElement('div');
    signalPanel.id = 'signalDiagram';
    signalPanel.className = 'diagram-panel';
    
    signalPanel.innerHTML = `
        <svg class="interactive-diagram" viewBox="0 0 800 600">
            <!-- Signal Flow Path -->
            <g class="signal-flow-diagram">
                <text x="400" y="30" class="section-title" text-anchor="middle" fill="var(--module-text-primary)" font-size="16" font-weight="600">Signal Processing Flow</text>
                
                <!-- Raw Signal -->
                <g class="signal-stage">
                    <rect x="50" y="100" width="100" height="60" rx="10" fill="var(--module-bg-primary)" stroke="var(--module-success)" stroke-width="2"/>
                    <text x="100" y="125" text-anchor="middle" fill="var(--module-text-primary)" font-size="12" font-weight="600">Raw ECG</text>
                    <text x="100" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">0.5-4.0 mV</text>
                    <text x="100" y="150" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">0.05-150 Hz</text>
                </g>
                
                <!-- Amplification -->
                <g class="signal-stage">
                    <rect x="200" y="100" width="100" height="60" rx="10" fill="var(--module-bg-primary)" stroke="var(--module-primary)" stroke-width="2"/>
                    <text x="250" y="125" text-anchor="middle" fill="var(--module-text-primary)" font-size="12" font-weight="600">Amplification</text>
                    <text x="250" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">Gain: 1000x</text>
                    <text x="250" y="150" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">0.5-4.0 V</text>
                </g>
                
                <!-- Filtering -->
                <g class="signal-stage">
                    <rect x="350" y="100" width="100" height="60" rx="10" fill="var(--module-bg-primary)" stroke="var(--module-secondary)" stroke-width="2"/>
                    <text x="400" y="125" text-anchor="middle" fill="var(--module-text-primary)" font-size="12" font-weight="600">Filtering</text>
                    <text x="400" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">Bandpass</text>
                    <text x="400" y="150" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">0.05-150 Hz</text>
                </g>
                
                <!-- Digitization -->
                <g class="signal-stage">
                    <rect x="500" y="100" width="100" height="60" rx="10" fill="var(--module-bg-primary)" stroke="var(--module-accent)" stroke-width="2"/>
                    <text x="550" y="125" text-anchor="middle" fill="var(--module-text-primary)" font-size="12" font-weight="600">ADC</text>
                    <text x="550" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">16-bit</text>
                    <text x="550" y="150" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">1000 Hz</text>
                </g>
                
                <!-- Processing -->
                <g class="signal-stage">
                    <rect x="650" y="100" width="100" height="60" rx="10" fill="var(--module-bg-primary)" stroke="var(--module-warning)" stroke-width="2"/>
                    <text x="700" y="125" text-anchor="middle" fill="var(--module-text-primary)" font-size="12" font-weight="600">DSP</text>
                    <text x="700" y="140" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">Algorithm</text>
                    <text x="700" y="150" text-anchor="middle" fill="var(--module-text-secondary)" font-size="10">Processing</text>
                </g>
                
                <!-- Signal flow arrows -->
                <path d="M 150 130 L 190 130" class="signal-path" marker-end="url(#signal-arrow)"/>
                <path d="M 300 130 L 340 130" class="signal-path" marker-end="url(#signal-arrow)"/>
                <path d="M 450 130 L 490 130" class="signal-path" marker-end="url(#signal-arrow)"/>
                <path d="M 600 130 L 640 130" class="signal-path" marker-end="url(#signal-arrow)"/>
                
                <!-- Output branches -->
                <path d="M 700 160 L 700 200 L 400 200 L 400 250" class="signal-path" marker-end="url(#signal-arrow)"/>
                <path d="M 500 200 L 500 250" class="signal-path" marker-end="url(#signal-arrow)"/>
                <path d="M 600 200 L 600 250" class="signal-path" marker-end="url(#signal-arrow)"/>
                
                <!-- Output displays -->
                <rect x="350" y="250" width="100" height="40" rx="5" fill="var(--module-bg-primary)" stroke="var(--module-success)" stroke-width="2"/>
                <text x="400" y="275" text-anchor="middle" fill="var(--module-text-primary)" font-size="11" font-weight="600">Waveform Display</text>
                
                <rect x="450" y="250" width="100" height="40" rx="5" fill="var(--module-bg-primary)" stroke="var(--module-primary)" stroke-width="2"/>
                <text x="500" y="275" text-anchor="middle" fill="var(--module-text-primary)" font-size="11" font-weight="600">Heart Rate</text>
                
                <rect x="550" y="250" width="100" height="40" rx="5" fill="var(--module-bg-primary)" stroke="var(--module-danger)" stroke-width="2"/>
                <text x="600" y="275" text-anchor="middle" fill="var(--module-text-primary)" font-size="11" font-weight="600">Alarms</text>
                
                <!-- Arrow marker for signal flow -->
                <defs>
                    <marker id="signal-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="var(--module-accent)"/>
                    </marker>
                </defs>
            </g>
        </svg>
    `;
    
    diagramContainer.appendChild(signalPanel);
}

// Start Diagram Animations
function startDiagramAnimations() {
    // Animate signal flow
    const signalPaths = document.querySelectorAll('.signal-path');
    signalPaths.forEach(path => {
        path.style.animation = 'signalFlow 1.5s linear infinite';
    });
    
    // Animate signal nodes
    const signalNodes = document.querySelectorAll('.signal-node');
    signalNodes.forEach(node => {
        node.style.animation = 'signalPulse 1s ease-in-out infinite';
    });
    
    // Animate connection lines
    const connectionLines = document.querySelectorAll('.connection-line');
    connectionLines.forEach(line => {
        line.style.animation = 'flowAnimation 2s ease-in-out infinite';
    });
}

// Export functions for global access
window.initializeDiagrams = initializeDiagrams;
window.showDiagram = showDiagram;
window.showComponentInfo = showComponentInfo;
